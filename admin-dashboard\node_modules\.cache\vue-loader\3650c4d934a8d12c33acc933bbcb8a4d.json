{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue?vue&type=template&id=2f5d6230&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue", "mtime": 1753349360417}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749542425518}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}