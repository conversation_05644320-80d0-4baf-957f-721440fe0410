<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.daka.pro.mapper.ParticipantSalaryConfigMapper">
    
    <!-- 根据项目编号和参建单位编号查询工资计算方式配置 -->
    <select id="getByProjectAndParticipant" resultType="com.daka.pro.domain.ParticipantSalaryConfig">
        SELECT *
        FROM participant_salary_config
        WHERE project_code = #{projectCode}
          AND participant_code = #{participantCode}
        LIMIT 1
    </select>
    
    <!-- 根据项目编号查询所有参建单位的工资计算方式配置 -->
    <select id="getByProjectCode" resultType="com.daka.pro.domain.ParticipantSalaryConfig">
        SELECT *
        FROM participant_salary_config
        WHERE project_code = #{projectCode}
        ORDER BY create_time DESC
    </select>
    
</mapper> 