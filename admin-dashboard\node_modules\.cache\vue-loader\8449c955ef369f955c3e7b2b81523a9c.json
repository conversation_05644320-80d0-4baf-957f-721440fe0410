{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue?vue&type=style&index=0&id=2f5d6230&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue", "mtime": 1753349360417}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749542423828}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749542425518}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749542425132}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractStep1.vue"], "names": [], "mappings": ";AA+0CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ContractStep1.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\n  <div class=\"contract-step1-container\">\n    <h3>步骤1：工人身份信息识别</h3>\n    \n    <div class=\"scanner-container\">\n      <div class=\"scanner-preview\">\n        <img id=\"photo\" src=\"\" width=\"600\" height=\"400\" ref=\"scannerPreview\">\n      </div>\n      \n      <div class=\"scanner-controls\">\n        <el-button type=\"primary\" @click=\"startScanner\">启动高拍仪</el-button>\n        <el-button type=\"success\" @click=\"scanIdCard\">识别身份证</el-button>\n        <el-button type=\"warning\" @click=\"stopScanner\">停止高拍仪</el-button>\n        <el-upload\n          class=\"upload-button\"\n          action=\"#\"\n          :show-file-list=\"false\"\n          :before-upload=\"handleUploadImage\">\n          <el-button type=\"primary\" icon=\"el-icon-upload\">上传图片</el-button>\n        </el-upload>\n      </div>\n    </div>\n    \n    <div class=\"worker-info-form\">\n      <el-form :model=\"workerForm\" label-width=\"120px\" ref=\"workerForm\" :rules=\"rules\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\n              <el-input v-model=\"workerForm.workerName\" placeholder=\"请输入工人姓名\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\n              <div class=\"input-with-button\">\n              <el-input v-model=\"workerForm.idCardNumber\" placeholder=\"请输入证件号码\"></el-input>\n                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refreshWorkerInfo\" :disabled=\"!workerForm.idCardNumber\" title=\"重新获取信息\">刷新</el-button>\n              </div>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"性别\" prop=\"gender\">\n              <el-select v-model=\"workerForm.gender\" placeholder=\"请选择性别\" style=\"width: 100%\">\n                <el-option label=\"男\" value=\"男\"></el-option>\n                <el-option label=\"女\" value=\"女\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"家庭住址\" prop=\"homeAddress\">\n              <el-input v-model=\"workerForm.homeAddress\" placeholder=\"请输入家庭住址\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"班组\" prop=\"teamName\">\n              <el-input v-model=\"workerForm.teamName\" placeholder=\"请输入班组名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"工种\" prop=\"jobPosition\">\n              <el-input v-model=\"workerForm.jobPosition\" placeholder=\"请输入工种\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"参建单位\" prop=\"participantName\">\n              <el-input v-model=\"workerForm.participantName\" placeholder=\"请输入参建单位\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      \n      <div class=\"form-actions\">\n        <el-button type=\"primary\" @click=\"searchWorker\">查找工人</el-button>\n        <el-button type=\"success\" @click=\"nextStep\">下一步</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getWorkerDetail, getWorkerByIdCard } from '@/api/roster'\nimport axios from 'axios'\n\nexport default {\n  name: 'ContractStep1',\n  props: {\n    workerInfo: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      webSocket: null,\n      scannerConnected: false,\n      workerForm: {\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        teamName: '',\n        teamCode: '',\n        participantName: '',\n        participantCode: '',\n        projectSubContractorId: '',\n        projectCode: '',\n        projectName: '',\n        jobPosition: ''\n      },\n      rules: {\n        workerName: [\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\n        ],\n        idCardNumber: [\n          { required: true, message: '请输入证件号码', trigger: 'blur' }\n        ],\n        homeAddress: [\n          { required: true, message: '请输入家庭住址', trigger: 'blur' }\n        ],\n        teamName: [\n          { required: true, message: '请输入班组名称', trigger: 'blur' }\n        ],\n        participantName: [\n          { required: true, message: '请输入参建单位', trigger: 'blur' }\n        ]\n      },\n      simulationMode: false, // 模拟模式标志\n      connectionTimeout: null, // 连接超时\n      manuallyDisconnected: false, // 手动断开标志\n      scannerConfig: {\n        wsUrl: 'ws://localhost:1818', // WebSocket连接地址\n        timeout: 3000, // 连接超时时间(毫秒)\n        autoSimulate: true, // 连接失败时是否自动切换到模拟模式\n        ocrApiUrl: '/ocr' // OCR API地址\n      },\n      configDialogVisible: false, // 高拍仪配置对话框可见性\n      tempScannerConfig: { // 临时存储的高拍仪配置\n        wsUrl: 'ws://localhost:1818',\n        timeout: 3000,\n        autoSimulate: true,\n        ocrApiUrl: '/ocr'\n      },\n      currentPhotoPath: '', // 当前拍摄的照片路径\n      isProcessingOcr: false, // 是否正在处理OCR\n      currentImageData: '', // 当前接收到的图像数据\n      waitingForBase64: false // 是否正在等待 Base64Encode 的响应\n    }\n  },\n  created() {\n    // 从props初始化表单\n    this.initFormFromProps()\n    \n    // 从URL获取项目编码\n    const { projectCode } = this.$route.query\n    if (projectCode) {\n      this.workerForm.projectCode = projectCode\n    }\n    \n    // 如果props中没有必要的数据，尝试从本地存储恢复\n    if (!this.workerForm.workerName && !this.workerForm.idCardNumber) {\n      this.tryRestoreFromLocalStorage(false) // 传false参数表示不要emit到父组件\n    }\n    \n    // 添加调试日志\n    console.log('ContractStep1 created - 初始化后的表单数据:', JSON.stringify(this.workerForm))\n  },\n  mounted() {\n    // 初始化高拍仪WebSocket连接\n    this.initScannerWebSocket()\n    \n    console.log('ContractStep1 mounted, 当前表单数据:', JSON.stringify(this.workerForm))\n  },\n  watch: {\n    // 监听workerInfo变化\n    workerInfo: {\n      handler(newVal, oldVal) {\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          console.log('ContractStep1 - workerInfo变化:', JSON.stringify(newVal))\n          \n          // 更新表单数据\n          this.initFormFromProps()\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  beforeDestroy() {\n    // 组件销毁前关闭WebSocket连接\n    this.closeWebSocket()\n\n    // 只有当完全离开合同流程时才清空数据\n    // 不要在步骤之间导航时清空\n    console.log('ContractStep1 beforeDestroy - 当前路径:', this.$route?.path)\n    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {\n      console.log('ContractStep1 - 离开合同流程，清空数据')\n      this.clearContractData()\n    } else {\n      console.log('ContractStep1 - 在合同流程内导航，保留数据')\n      // 确保数据已保存到localStorage\n      this.saveToLocalStorage()\n    }\n  },\n  methods: {\n    // 清空合同数据\n    clearContractData() {\n      console.log('ContractStep1 - 清空合同数据')\n      localStorage.removeItem('contractWorkerInfo')\n    },\n    \n    // 尝试从本地存储恢复数据\n    tryRestoreFromLocalStorage(shouldEmit = true) {\n      try {\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\n        if (savedWorkerInfo) {\n          const parsedData = JSON.parse(savedWorkerInfo)\n          console.log('ContractStep1 - 从本地存储恢复数据:', JSON.stringify(parsedData))\n          \n          // 更新表单数据\n          Object.keys(this.workerForm).forEach(key => {\n            if (parsedData[key] !== undefined && parsedData[key] !== null) {\n              this.workerForm[key] = parsedData[key]\n            }\n          })\n          \n          // 只在需要时更新父组件数据\n          if (shouldEmit) {\n            this.$emit('update-worker-info', { ...parsedData })\n          }\n          \n          return true\n        }\n      } catch (error) {\n        console.error('ContractStep1 - 从本地存储恢复数据失败:', error)\n      }\n      return false\n    },\n    \n    // 从props初始化表单\n    initFormFromProps() {\n      if (this.workerInfo) {\n        console.log('ContractStep1 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))\n        \n        // 复制属性到表单，只复制有值的字段\n        Object.keys(this.workerForm).forEach(key => {\n          if (this.workerInfo[key] !== undefined && this.workerInfo[key] !== null && this.workerInfo[key] !== '') {\n            this.workerForm[key] = this.workerInfo[key]\n          }\n        })\n        \n        console.log('ContractStep1 - 从props初始化数据后:', JSON.stringify(this.workerForm))\n      }\n    },\n    \n    // 初始化高拍仪WebSocket连接\n    initScannerWebSocket() {\n      try {\n        // 添加模拟模式标志\n        this.simulationMode = false;\n        \n        // 尝试连接WebSocket\n        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)\n        \n        // 设置连接超时\n        this.connectionTimeout = setTimeout(() => {\n          if (!this.scannerConnected) {\n            console.warn('高拍仪连接超时，切换到模拟模式');\n            if (this.scannerConfig.autoSimulate) {\n              this.switchToSimulationMode();\n            } else {\n              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\n            }\n          }\n        }, this.scannerConfig.timeout);\n        \n        this.webSocket.onopen = (event) => {\n          console.log('高拍仪WebSocket连接成功')\n          this.scannerConnected = true\n          this.$message.success('高拍仪连接成功')\n          clearTimeout(this.connectionTimeout);\n        }\n        \n        this.webSocket.onclose = (event) => {\n          console.log('高拍仪WebSocket连接关闭')\n          this.scannerConnected = false\n          // 如果不是主动关闭，尝试切换到模拟模式\n          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {\n            this.switchToSimulationMode();\n          }\n        }\n        \n        this.webSocket.onerror = (event) => {\n          console.error('高拍仪WebSocket连接错误', event)\n          this.scannerConnected = false\n          if (this.scannerConfig.autoSimulate) {\n            this.switchToSimulationMode();\n          } else {\n            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\n          }\n        }\n        \n        this.webSocket.onmessage = (event) => {\n          this.handleScannerMessage(event)\n        }\n      } catch (error) {\n        console.error('初始化高拍仪WebSocket失败', error)\n        if (this.scannerConfig.autoSimulate) {\n          this.switchToSimulationMode();\n        } else {\n          this.$message.error(`初始化高拍仪失败: ${error.message}`);\n        }\n      }\n    },\n    \n    // 处理高拍仪消息\n    handleScannerMessage(event) {\n      const begin_data = \"data:image/jpeg;base64,\"\n      \n      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {\n        // 处理条码识别结果\n        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')\n        this.$message.success('识别到条码: ' + barcode)\n        \n        // 如果是身份证号格式，填入表单\n        if (this.isIdCardNumber(barcode)) {\n          this.workerForm.idCardNumber = barcode\n          this.searchWorkerByIdCard(barcode)\n        }\n      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {\n        // 处理保存图片结果\n        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')\n        this.$message.success('图片保存成功: ' + result)\n      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {\n        // 处理 Base64Encode 命令返回的 base64 数据\n        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')\n        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)\n        \n        // 重置等待标志\n        this.waitingForBase64 = false\n        \n        if (base64Data && base64Data.length > 1000) { // 确保数据有效\n          // 保存 base64 数据\n          this.currentImageData = begin_data + base64Data\n          \n          // 使用高质量 base64 数据进行 OCR 识别\n          console.log('使用高质量 base64 数据进行 OCR 识别')\n          this.processOcrWithImage(this.currentImageData)\n        } else {\n          console.error('获取到的 base64 数据无效或太短')\n          \n          // 如果 base64 数据无效，尝试使用预览图或文件路径\n          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \n              this.$refs.scannerPreview.src.startsWith('data:image')) {\n            console.log('使用预览图数据进行 OCR 识别')\n            this.processOcrWithImage(this.$refs.scannerPreview.src)\n          } else {\n            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\n            this.processOcrWithImage(this.currentPhotoPath)\n          }\n        }\n      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {\n        // 处理目录检查结果\n        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')\n        console.log('目录检查结果:', result)\n        // 如果目录不存在，结果为\"0\"，存在则为\"1\"\n        if (result === \"0\") {\n          console.log('C:\\\\pic\\\\ 目录不存在，将创建')\n        }\n      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {\n        // 处理创建目录结果\n        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')\n        console.log('创建目录结果:', result)\n        // 如果创建成功，结果为\"1\"，失败则为\"0\"\n        if (result === \"1\") {\n          console.log('C:\\\\pic\\\\ 目录创建成功')\n        } else {\n          console.warn('C:\\\\pic\\\\ 目录创建失败或已存在')\n        }\n      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {\n        // 处理条码识别命令响应，不作为图像数据处理\n        console.log('收到条码识别命令响应:', event.data)\n      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {\n        // 处理图像数据 - 判断是否为base64图像数据\n        // 增加额外检查，确保不是条码数据\n        if (this.$refs.scannerPreview) {\n          try {\n            // 尝试验证是否为有效的base64图像数据\n            const testData = event.data.substring(0, 100); // 只取前100个字符测试\n            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常\n            \n            // 确保是完整的base64数据\n            const imgData = begin_data + event.data\n            this.$refs.scannerPreview.src = imgData\n            \n            // 保存当前图像数据以备后用\n            this.currentImageData = imgData\n            console.log('成功保存图像数据，长度:', event.data.length)\n          } catch (e) {\n            console.error('收到的数据不是有效的base64图像:', e)\n          }\n        }\n      } else {\n        // 其他消息，可能是普通文本或命令响应\n        console.log('收到高拍仪消息:', event.data)\n      }\n    },\n    \n    // 启动高拍仪\n    startScanner() {\n      if (!this.scannerConnected) {\n        this.initScannerWebSocket()\n        return\n      }\n      \n      try {\n        // 设置分辨率\n        this.sendScannerCommand('vSetResolution(8)')\n        \n        // 启用去黑边功能\n        this.sendScannerCommand('vSetDelHBFlag(true)')\n        \n        // 增加亮度控制\n        this.sendScannerCommand('vSetBrightness(80)') // 增加亮度\n        \n        // 增加对比度控制\n        this.sendScannerCommand('vSetContrast(70)') // 增加对比度\n        \n        // 设置曝光\n        this.sendScannerCommand('vSetExposure(60)') // 设置曝光\n        \n        // 启动主摄像头\n        this.sendScannerCommand('bStartPlay()')\n        this.$message.success('高拍仪已启动')\n      } catch (error) {\n        console.error('启动高拍仪失败', error)\n        this.$message.error('启动高拍仪失败')\n      }\n    },\n    \n    // 停止高拍仪\n    stopScanner() {\n      if (!this.scannerConnected) {\n        return\n      }\n      \n      try {\n        this.sendScannerCommand('bStopPlay()')\n        this.$message.success('高拍仪已停止')\n      } catch (error) {\n        console.error('停止高拍仪失败', error)\n        this.$message.error('停止高拍仪失败')\n      }\n    },\n    \n    // 扫描身份证\n    scanIdCard() {\n      if (!this.scannerConnected && !this.simulationMode) {\n        this.$message.warning('请先启动高拍仪')\n        return\n      }\n      \n      try {\n        if (this.simulationMode) {\n          // 模拟模式下，直接调用OCR接口处理示例图片\n          this.processOcrWithSimulationImage()\n          return\n        }\n        \n        // 确保启用去黑边功能\n        this.sendScannerCommand('vSetDelHBFlag(true)')\n        \n        // 设置身份证自动寻边模式\n        this.sendScannerCommand('bSetMode(4)')\n        \n        // 先检查目录是否存在，不存在则创建\n        this.sendScannerCommand('bDirIsExist(C:\\\\pic\\\\)')\n        \n        // 延迟一下，确保目录检查完成\n        setTimeout(() => {\n          // 创建目录（即使目录已存在，这个命令也不会报错）\n          this.sendScannerCommand('bCreateDir(C:\\\\pic\\\\)')\n          \n          // 生成唯一文件名（使用时间戳）\n          const timestamp = new Date().getTime()\n          const filename = `idcard_${timestamp}`\n          this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\n          console.log('当前照片路径:', this.currentPhotoPath)\n          \n          // 拍照并保存到本地\n          this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\n          \n          // 设置一个标志，表示我们正在等待 Base64Encode 的响应\n          this.waitingForBase64 = true\n          \n          // 延迟一下，确保图片保存完成\n          setTimeout(() => {\n            // 使用 Base64Encode 命令获取高质量的 base64 图像数据\n            this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)\n            \n            // 识别条码\n            this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)\n            \n            // 清除之前的图像数据，确保不会使用旧数据\n            this.currentImageData = null\n            \n            // 设置超时，确保即使没有收到 Base64Encode 的响应，也会调用 OCR 接口\n            setTimeout(() => {\n              if (this.waitingForBase64) {\n                console.log('Base64Encode 响应超时，使用备用方法调用 OCR')\n                this.waitingForBase64 = false\n                \n                // 如果有预览图数据，使用预览图数据\n                if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \n                    this.$refs.scannerPreview.src.startsWith('data:image')) {\n                  console.log('使用预览图数据进行 OCR 识别')\n                  this.processOcrWithImage(this.$refs.scannerPreview.src)\n                } else {\n                  // 否则使用文件路径\n                  console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\n                  this.processOcrWithImage(this.currentPhotoPath)\n                }\n              }\n            }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法\n            \n          }, 1000) // 延迟1秒，确保图片保存完成\n        }, 500) // 延迟500ms，确保目录检查完成\n        \n        this.$message.info('正在识别身份证，请稍候...')\n      } catch (error) {\n        console.error('扫描身份证失败', error)\n        this.$message.error('扫描身份证失败')\n      }\n    },\n    \n         // 处理OCR识别结果\n    processOcrWithImage(imagePath) {\n      if (this.isProcessingOcr) {\n        return\n      }\n      \n      this.isProcessingOcr = true\n      this.$message.info('正在进行OCR识别...')\n      \n      // 判断是否是base64格式的图片数据\n      if (imagePath.startsWith('data:image')) {\n        // 对图片进行缩放处理，然后再进行OCR识别\n        this.scaleImageForOcr(imagePath).then(scaledImageData => {\n          console.log('图片已缩放处理，准备进行OCR识别')\n          \n          // 创建文件对象从base64数据\n          const base64Data = scaledImageData.split(',')[1]\n          \n          // 添加JSON格式的请求头\n          const config = {\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          }\n          \n          // 创建JSON数据\n          const jsonData = {\n            image: base64Data\n          }\n          \n          // 调用OCR API - 使用JSON格式发送\n          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)\n            .then(response => {\n              console.log('OCR API 响应:', response)\n              this.handleOcrResult(response.data)\n            })\n            .catch(error => {\n              console.error('OCR识别失败', error)\n              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n              \n              // 如果JSON格式失败，尝试使用表单数据\n              console.log('尝试使用表单数据格式重新发送请求')\n              \n              // 创建表单数据\n              const formData = new FormData()\n              const byteCharacters = atob(base64Data)\n              const byteArrays = []\n              \n              for (let i = 0; i < byteCharacters.length; i++) {\n                byteArrays.push(byteCharacters.charCodeAt(i))\n              }\n              \n              const byteArray = new Uint8Array(byteArrays)\n              const blob = new Blob([byteArray], { type: 'image/jpeg' })\n              \n              // 创建文件对象\n              const fileName = `idcard_${new Date().getTime()}.jpg`\n              const file = new File([blob], fileName, { type: 'image/jpeg' })\n              \n              // 添加到表单\n              formData.append('image', file)\n              this.callOcrApi(formData)\n            })\n            .finally(() => {\n              this.isProcessingOcr = false\n            })\n        }).catch(error => {\n          console.error('图像缩放处理失败:', error)\n          \n          // 如果缩放处理失败，直接使用原始图像\n          this.sendOriginalImage(imagePath)\n        })\n      } else {\n        // 如果是文件路径，尝试读取文件并上传\n        const formData = new FormData()\n        formData.append('image_path', imagePath)\n        console.log(`发送图片路径进行OCR识别: ${imagePath}`)\n        \n        // 调用OCR API\n        this.callOcrApi(formData)\n      }\n    },\n    \n    // 对图片进行缩放处理，只进行尺寸调整\n    scaleImageForOcr(imageDataUrl) {\n      return new Promise((resolve, reject) => {\n        try {\n          const img = new Image()\n          img.src = imageDataUrl\n          \n          img.onload = () => {\n            // 判断是否需要缩放\n            if (img.width <= 1000 && img.height <= 630) {\n              console.log('图片尺寸已经合适，无需缩放')\n              resolve(imageDataUrl)\n              return\n            }\n            \n            // 为OCR识别优化的尺寸，减小尺寸提高处理速度\n            // 身份证比例大约是1.58:1\n            const maxWidth = 1000  // 从1800减小到1000\n            const maxHeight = 630  // 从1140减小到630\n            \n            // 计算等比例缩放后的尺寸\n            let width = img.width\n            let height = img.height\n            \n            if (width > maxWidth) {\n              height = (height * maxWidth) / width\n              width = maxWidth\n            }\n            \n            if (height > maxHeight) {\n              width = (width * maxHeight) / height\n              height = maxHeight\n            }\n            \n            // 创建Canvas\n            const canvas = document.createElement('canvas')\n            canvas.width = width\n            canvas.height = height\n            const ctx = canvas.getContext('2d')\n            \n            // 绘制图像\n            ctx.drawImage(img, 0, 0, width, height)\n            \n            // 转换为适中质量JPEG，进一步减小文件大小\n            const scaledImageData = canvas.toDataURL('image/jpeg', 0.85)\n            \n            // 输出调试信息\n            console.log(`图片已缩放: 原始尺寸=${img.width}x${img.height}, 缩放尺寸=${width}x${height}`)\n            \n            resolve(scaledImageData)\n          }\n          \n          img.onerror = (error) => {\n            console.error('图像加载失败:', error)\n            reject(error)\n          }\n        } catch (e) {\n          console.error('图像缩放处理失败:', e)\n          reject(e)\n        }\n      })\n    },\n    \n    // 发送原始图像\n    sendOriginalImage(imagePath) {\n      try {\n        console.log('使用原始图像数据进行OCR识别')\n        \n        if (imagePath.startsWith('data:image')) {\n          // 创建文件对象从base64数据\n          const base64Data = imagePath.split(',')[1]\n          \n          // 添加JSON格式的请求头\n          const config = {\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          }\n          \n          // 创建JSON数据\n          const jsonData = {\n            image: base64Data\n          }\n          \n          // 使用JSON格式发送\n          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)\n            .then(response => {\n              console.log('OCR API 响应:', response)\n              this.handleOcrResult(response.data)\n            })\n            .catch(error => {\n              console.error('OCR识别失败', error)\n              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n            })\n            .finally(() => {\n              this.isProcessingOcr = false\n            })\n        } else {\n          // 如果是文件路径\n          const formData = new FormData()\n          formData.append('image_path', imagePath)\n          this.callOcrApi(formData)\n        }\n      } catch (e) {\n        console.error('发送原始图像失败:', e)\n        this.isProcessingOcr = false\n        this.$message.error('发送图像失败: ' + e.message)\n      }\n    },\n    \n    // 模拟模式下使用示例图片进行OCR识别\n    processOcrWithSimulationImage() {\n      this.isProcessingOcr = true\n      this.$message.info('模拟模式：正在进行OCR识别...')\n      \n      // 准备表单数据\n      const formData = new FormData()\n      formData.append('simulation', 'true')\n      \n      // 调用OCR API\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\n        .then(response => {\n          this.handleOcrResult(response.data)\n        })\n        .catch(error => {\n          console.error('模拟OCR识别失败', error)\n          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))\n        })\n        .finally(() => {\n          this.isProcessingOcr = false\n        })\n    },\n    \n    // 处理OCR识别结果\n    handleOcrResult(result) {\n      if (!result || !result.success) {\n        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))\n        return\n      }\n      \n      this.$message.success('OCR识别成功')\n      console.log('OCR识别结果:', result)\n      \n      // 更新表单数据\n      const ocrData = result.data || {}\n      \n      // 保存OCR识别的地址，以便在API查询后仍能使用\n      const ocrAddress = ocrData.address || ''\n      \n      // 更新身份证号\n      if (ocrData.id_number) {\n        this.workerForm.idCardNumber = ocrData.id_number\n        // 添加更明显的提示\n        this.$notify({\n          title: '证件号码识别成功',\n          type: 'success',\n          duration: 5000\n        })\n      }\n      \n      // 更新姓名\n      if (ocrData.name) {\n        this.workerForm.workerName = ocrData.name\n      }\n      \n      // 更新性别\n      if (ocrData.gender) {\n        this.workerForm.gender = ocrData.gender\n      }\n      \n      // 更新地址\n      if (ocrData.address) {\n        this.workerForm.homeAddress = ocrData.address\n        // 添加地址识别成功的提示\n        this.$notify({\n          title: '地址识别成功',\n          message: `识别到地址: ${ocrData.address}`,\n          type: 'success',\n          duration: 5000\n        })\n      }\n      \n      // 如果有身份证号，尝试从系统中查询更多信息\n      if (ocrData.id_number) {\n        // 传递OCR识别的地址作为参数\n        this.searchWorkerByIdCard(ocrData.id_number, ocrAddress)\n      }\n    },\n    \n    // 发送高拍仪命令\n    sendScannerCommand(command) {\n      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {\n        this.webSocket.send(command)\n      } else {\n        throw new Error('WebSocket未连接')\n      }\n    },\n    \n    // 关闭WebSocket连接\n    closeWebSocket() {\n      if (this.webSocket) {\n        // 先停止高拍仪\n        if (this.scannerConnected) {\n          try {\n            this.webSocket.send('bStopPlay()')\n          } catch (e) {\n            console.error('停止高拍仪失败', e)\n          }\n        }\n        \n        // 关闭连接\n        this.webSocket.close()\n        this.webSocket = null\n        this.scannerConnected = false\n      }\n    },\n    \n    // 验证是否为身份证号\n    isIdCardNumber(str) {\n      // 简单验证18位或15位身份证号\n      const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\n      return reg.test(str)\n    },\n    \n    // 根据身份证号查询工人信息\n    searchWorkerByIdCard(idCardNumber, addressFromOcr = '') {\n      if (!idCardNumber) {\n        this.$message.warning('证件号码不能为空')\n        return\n      }\n      \n      this.$message.info('正在查询工人信息...')\n      \n      // 调用API根据身份证号查询工人信息\n      getWorkerByIdCard(idCardNumber)\n        .then(response => {\n          if (response.code === 0 && response.data) {\n            const workerData = response.data\n            \n            // 更新表单数据\n            this.workerForm.workerId = workerData.id || ''\n            this.workerForm.workerName = workerData.workerName || ''\n            this.workerForm.gender = workerData.gender || '男'\n            this.workerForm.homeAddress = workerData.homeAddress || ''\n            this.workerForm.teamName = workerData.teamSysName || ''\n            this.workerForm.teamCode = workerData.teamSysNo || ''\n            this.workerForm.jobPosition = workerData.workerType || ''\n            this.workerForm.participantName = workerData.projectSubContractorName || workerData.corpName || ''\n            this.workerForm.participantCode = workerData.corpCode || ''\n            this.workerForm.projectSubContractorId = workerData.projectSubContractorId || workerData.corpId || ''\n            this.workerForm.projectCode = workerData.projectCode || this.workerForm.projectCode\n            this.workerForm.projectName = workerData.projectName || ''\n            \n            // 如果API没有返回地址，但OCR识别到了地址，则使用OCR识别的地址\n            if ((!workerData.homeAddress || workerData.homeAddress === '') && addressFromOcr) {\n              console.log('使用OCR识别的地址:', addressFromOcr)\n              this.workerForm.homeAddress = addressFromOcr\n            }\n            \n            // 保存到本地存储\n            this.saveToLocalStorage()\n        \n            this.$message.success('工人信息查询成功')\n            \n            // 添加更详细的通知\n            this.$notify({\n              title: '工人信息查询成功',\n              message: `已找到工人: ${workerData.workerName}，所属班组: ${workerData.teamSysName || '未知'}`,\n              type: 'success',\n              duration: 5000\n            })\n          } else {\n            this.$message.warning(response.msg || '未找到工人信息')\n            \n            // 如果没有找到，可以保留一些基本信息\n            if (this.workerForm.idCardNumber && !this.workerForm.workerName) {\n              // 从身份证号提取出生日期和性别信息\n              this.extractInfoFromIdCard(idCardNumber)\n            }\n            \n            // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址\n            if (addressFromOcr) {\n              console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)\n              this.workerForm.homeAddress = addressFromOcr\n            }\n          }\n        })\n        .catch(error => {\n          console.error('查询工人信息失败:', error)\n          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))\n          \n          // 如果API调用失败，可以尝试从身份证号提取一些基本信息\n          if (this.workerForm.idCardNumber) {\n            this.extractInfoFromIdCard(idCardNumber)\n          }\n          \n          // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址\n          if (addressFromOcr) {\n            console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)\n            this.workerForm.homeAddress = addressFromOcr\n          }\n        })\n    },\n    \n    // 从身份证号提取信息\n    extractInfoFromIdCard(idCardNumber) {\n      if (!idCardNumber || idCardNumber.length < 18) return\n      \n      try {\n        // 提取性别 (第17位，奇数为男，偶数为女)\n        const genderCode = parseInt(idCardNumber.charAt(16))\n        this.workerForm.gender = genderCode % 2 === 1 ? '男' : '女'\n        \n        // 可以添加更多提取逻辑，如出生日期等\n        console.log('从身份证号提取的性别:', this.workerForm.gender)\n      } catch (e) {\n        console.error('从身份证号提取信息失败:', e)\n      }\n    },\n    \n    // 查找工人\n    searchWorker() {\n      if (!this.workerForm.idCardNumber) {\n        this.$message.warning('请先输入证件号码')\n        return\n      }\n      \n      // 根据证件号码查询工人信息\n      this.searchWorkerByIdCard(this.workerForm.idCardNumber)\n    },\n    \n    // 刷新工人信息\n    refreshWorkerInfo() {\n      if (this.workerForm.idCardNumber) {\n        this.$message.info('正在重新获取工人信息...')\n        this.searchWorkerByIdCard(this.workerForm.idCardNumber)\n      } else {\n        this.$message.warning('请先输入证件号码')\n      }\n    },\n    \n    // 保存数据到本地存储\n    saveToLocalStorage() {\n      try {\n        // 合并表单数据和已有数据\n        let existingData = {}\n        try {\n          const savedData = localStorage.getItem('contractWorkerInfo')\n          if (savedData) {\n            existingData = JSON.parse(savedData)\n          }\n        } catch (e) {}\n        \n        const dataToSave = { \n          ...existingData,\n          ...this.workerForm \n        }\n        \n        console.log('ContractStep1 保存数据到本地存储:', JSON.stringify(dataToSave))\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave))\n        return dataToSave\n      } catch (error) {\n        console.error('保存到本地存储失败:', error)\n        return this.workerForm\n      }\n    },\n    \n    // 下一步\n    nextStep() {\n      this.$refs.workerForm.validate(valid => {\n        if (valid) {\n          // 关闭高拍仪\n          if (this.scannerConnected) {\n            this.stopScanner();\n            this.closeWebSocket();\n            console.log('下一步操作：已自动关闭高拍仪');\n          }\n          \n          // 添加详细的调试日志\n          console.log('=== ContractStep1 nextStep 开始 ===')\n          console.log('ContractStep1 当前表单数据:', JSON.stringify(this.workerForm))\n\n          // 保存到本地存储并获取完整数据\n          const completeData = this.saveToLocalStorage()\n          console.log('ContractStep1 保存到localStorage的完整数据:', JSON.stringify(completeData))\n\n          // 准备一个包含所有必要字段的数据对象\n          const dataToUpdate = {\n            ...completeData,\n            // 确保这些重要字段一定会被传递\n            workerName: this.workerForm.workerName,\n            idCardNumber: this.workerForm.idCardNumber,\n            gender: this.workerForm.gender,\n            homeAddress: this.workerForm.homeAddress,\n            teamName: this.workerForm.teamName,\n            jobPosition: this.workerForm.jobPosition,\n            participantName: this.workerForm.participantName\n          }\n\n          console.log('ContractStep1 准备发送给父组件的数据:', JSON.stringify(dataToUpdate))\n          console.log('ContractStep1 关键字段检查:')\n          console.log('- workerName:', dataToUpdate.workerName)\n          console.log('- idCardNumber:', dataToUpdate.idCardNumber)\n\n          // 先更新父组件中的工人信息\n          this.$emit('update-worker-info', dataToUpdate)\n          console.log('ContractStep1 已发送update-worker-info事件')\n          \n          // 等待数据更新后再触发导航\n          this.$nextTick(() => {\n            // 发送下一步事件，让父组件处理导航\n            this.$emit('next-step')\n          })\n        } else {\n          this.$message.warning('请完善工人信息')\n        }\n      })\n    },\n    \n    // 切换到模拟模式\n    switchToSimulationMode() {\n      this.simulationMode = true;\n      this.scannerConnected = false;\n      \n      if (this.webSocket) {\n        this.manuallyDisconnected = true;\n        this.webSocket.close();\n        this.webSocket = null;\n      }\n      \n      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动输入信息或使用模拟识别功能。');\n      \n      // 清除连接超时\n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n      }\n    },\n\n    // 上传图片处理函数\n    handleUploadImage(file) {\n      if (file) {\n        // 验证文件类型\n        const isImage = file.type.indexOf('image/') !== -1;\n        if (!isImage) {\n          this.$message.error('请上传图片文件!');\n          return false;\n        }\n        \n        // 验证文件大小 (限制为10MB)\n        const isLt10M = file.size / 1024 / 1024 < 10;\n        if (!isLt10M) {\n          this.$message.error('图片大小不能超过10MB!');\n          return false;\n        }\n        \n        this.$message.info('正在处理图片，请稍候...');\n        \n        // 更新预览图并压缩图片\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = (e) => {\n          // 压缩图片\n          this.compressImage(e.target.result, (compressedDataUrl) => {\n            // 更新预览图\n            if (this.$refs.scannerPreview) {\n              this.$refs.scannerPreview.src = compressedDataUrl;\n            }\n            \n            // 保存图片路径并处理OCR\n            this.currentPhotoPath = compressedDataUrl;\n            this.$message.info('正在识别上传的图片，请稍候...');\n            \n            // 调用OCR识别\n            setTimeout(() => {\n              this.processOcrWithImage(this.currentPhotoPath);\n            }, 300); // 短暂延迟，确保UI更新\n          });\n        };\n        \n        reader.onerror = (error) => {\n          console.error('读取图片文件失败', error);\n          this.$message.error('读取图片文件失败');\n        };\n      }\n      return false; // 阻止默认的上传行为\n    },\n    \n    // 压缩图片函数\n    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {\n      const img = new Image();\n      img.src = dataUrl;\n      \n      img.onload = () => {\n        // 创建Canvas\n        const canvas = document.createElement('canvas');\n        let width = img.width;\n        let height = img.height;\n        \n        // 计算缩放比例\n        if (width > height) {\n          if (width > maxWidth) {\n            height *= maxWidth / width;\n            width = maxWidth;\n          }\n        } else {\n          if (height > maxHeight) {\n            width *= maxHeight / height;\n            height = maxHeight;\n          }\n        }\n        \n        // 设置Canvas大小\n        canvas.width = width;\n        canvas.height = height;\n        \n        // 绘制图像\n        const ctx = canvas.getContext('2d');\n        ctx.drawImage(img, 0, 0, width, height);\n        \n        // 转换为压缩后的DataURL\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\n        \n        // 计算压缩率\n        const originalSize = dataUrl.length;\n        const compressedSize = compressedDataUrl.length;\n        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);\n        \n        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);\n        \n        callback(compressedDataUrl);\n      };\n      \n      img.onerror = () => {\n        console.error('图片压缩失败');\n        callback(dataUrl); // 失败时使用原始图片\n      };\n    },\n\n    // 增强图像质量\n    enhanceImage(imageDataUrl) {\n      return new Promise((resolve, reject) => {\n        try {\n          const img = new Image();\n          img.src = imageDataUrl;\n          \n          img.onload = () => {\n            // 创建Canvas\n            const canvas = document.createElement('canvas');\n            canvas.width = img.width;\n            canvas.height = img.height;\n            const ctx = canvas.getContext('2d');\n            \n            // 绘制原始图像\n            ctx.drawImage(img, 0, 0, img.width, img.height);\n            \n            // 1. 自动裁剪 - 找到身份证区域\n            this.cropIdCard(canvas, ctx);\n            \n            // 2. 亮度和对比度增强\n            this.adjustBrightnessContrast(canvas, ctx, 50, 80); // 增加亮度和对比度\n            \n            // 3. 锐化处理\n            this.sharpenImage(canvas, ctx);\n            \n            // 返回处理后的图像\n            resolve(canvas.toDataURL('image/jpeg', 0.95)); // 使用高质量\n          };\n          \n          img.onerror = (error) => {\n            console.error('图像加载失败:', error);\n            reject(error);\n          };\n        } catch (e) {\n          console.error('图像增强处理失败:', e);\n          reject(e);\n        }\n      });\n    },\n    \n    // 裁剪身份证区域\n    cropIdCard(canvas, ctx) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        \n        // 查找非黑色区域边界\n        let minX = canvas.width, minY = canvas.height, maxX = 0, maxY = 0;\n        const threshold = 40; // 亮度阈值\n        \n        for (let y = 0; y < canvas.height; y++) {\n          for (let x = 0; x < canvas.width; x++) {\n            const idx = (y * canvas.width + x) * 4;\n            const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;\n            \n            if (brightness > threshold) {\n              minX = Math.min(minX, x);\n              minY = Math.min(minY, y);\n              maxX = Math.max(maxX, x);\n              maxY = Math.max(maxY, y);\n            }\n          }\n        }\n        \n        // 添加边距\n        const padding = 10;\n        minX = Math.max(0, minX - padding);\n        minY = Math.max(0, minY - padding);\n        maxX = Math.min(canvas.width, maxX + padding);\n        maxY = Math.min(canvas.height, maxY + padding);\n        \n        // 检查是否找到了有效区域\n        const width = maxX - minX;\n        const height = maxY - minY;\n        \n        if (width > 50 && height > 50 && width < canvas.width && height < canvas.height) {\n          // 裁剪图像\n          const croppedData = ctx.getImageData(minX, minY, width, height);\n          canvas.width = width;\n          canvas.height = height;\n          ctx.putImageData(croppedData, 0, 0);\n          console.log('成功裁剪身份证区域:', width, 'x', height);\n        } else {\n          console.log('未找到明确的身份证区域，跳过裁剪');\n        }\n      } catch (e) {\n        console.error('裁剪身份证区域失败:', e);\n      }\n    },\n    \n    // 调整亮度和对比度\n    adjustBrightnessContrast(canvas, ctx, brightness = 0, contrast = 0) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        \n        // 计算对比度因子\n        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));\n        \n        for (let i = 0; i < data.length; i += 4) {\n          // 亮度调整\n          data[i] += brightness;     // R\n          data[i + 1] += brightness; // G\n          data[i + 2] += brightness; // B\n          \n          // 对比度调整\n          data[i] = factor * (data[i] - 128) + 128;\n          data[i + 1] = factor * (data[i + 1] - 128) + 128;\n          data[i + 2] = factor * (data[i + 2] - 128) + 128;\n          \n          // 确保值在0-255范围内\n          data[i] = Math.min(255, Math.max(0, data[i]));\n          data[i + 1] = Math.min(255, Math.max(0, data[i + 1]));\n          data[i + 2] = Math.min(255, Math.max(0, data[i + 2]));\n        }\n        \n        ctx.putImageData(imageData, 0, 0);\n        console.log('成功调整亮度和对比度');\n      } catch (e) {\n        console.error('调整亮度和对比度失败:', e);\n      }\n    },\n    \n    // 锐化图像\n    sharpenImage(canvas, ctx) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n        const sharpenData = new Uint8ClampedArray(data);\n        \n        // 锐化卷积核\n        const kernel = [\n          0, -1, 0,\n          -1, 5, -1,\n          0, -1, 0\n        ];\n        \n        // 应用卷积\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            for (let c = 0; c < 3; c++) {\n              let sum = 0;\n              for (let ky = -1; ky <= 1; ky++) {\n                for (let kx = -1; kx <= 1; kx++) {\n                  const idx = ((y + ky) * width + (x + kx)) * 4 + c;\n                  sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n                }\n              }\n              sharpenData[(y * width + x) * 4 + c] = Math.min(255, Math.max(0, sum));\n            }\n          }\n        }\n        \n        // 更新图像数据\n        const enhancedImageData = new ImageData(sharpenData, width, height);\n        ctx.putImageData(enhancedImageData, 0, 0);\n        console.log('成功锐化图像');\n      } catch (e) {\n        console.error('锐化图像失败:', e);\n      }\n    },\n\n    // 调用OCR API\n    callOcrApi(formData) {\n      console.log('调用OCR API:', this.scannerConfig.ocrApiUrl)\n      \n      // 添加跨域请求头\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Access-Control-Allow-Origin': '*'\n        }\n      }\n      \n      axios.post(this.scannerConfig.ocrApiUrl, formData, config)\n        .then(response => {\n          console.log('OCR API 响应:', response)\n          this.handleOcrResult(response.data)\n        })\n        .catch(error => {\n          console.error('OCR识别失败', error)\n          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n        })\n        .finally(() => {\n          this.isProcessingOcr = false\n        })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.contract-step1-container {\n  padding: 20px;\n}\n\n.contract-step1-container h3 {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n  color: #303133;\n}\n\n.scanner-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  background-color: #f9fafc;\n}\n\n.scanner-preview {\n  width: 600px;\n  height: 400px;\n  margin-bottom: 20px;\n  border: 1px solid #dcdfe6;\n  background-color: #ebeef5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n}\n\n.scanner-preview img {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n.scanner-controls {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.upload-button {\n  display: inline-block;\n  margin-left: 10px;\n}\n\n.worker-info-form {\n  padding: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  background-color: #fff;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.input-with-button {\n  display: flex;\n  align-items: center;\n}\n\n.input-with-button .el-input {\n  flex: 1;\n  margin-right: 8px;\n}\n\n@media (max-width: 768px) {\n  .scanner-preview {\n    width: 100%;\n    height: 300px;\n  }\n  \n  .scanner-controls {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .upload-button {\n    margin-left: 0;\n    margin-top: 10px;\n  }\n  \n  .input-with-button {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .input-with-button .el-input {\n    margin-right: 0;\n    margin-bottom: 8px;\n  }\n}\n</style> "]}]}