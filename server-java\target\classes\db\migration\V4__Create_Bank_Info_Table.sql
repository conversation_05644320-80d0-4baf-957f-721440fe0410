-- 创建银行信息表
CREATE TABLE IF NOT EXISTS bank_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bank_name VARCHAR(255) NOT NULL COMMENT '银行名称',
    bank_code VARCHAR(20) NOT NULL COMMENT '银行代码',
    UNIQUE KEY `uk_bank_code` (`bank_code`)
) COMMENT='银行信息表';

-- 初始化数据，导入一些常用银行
INSERT INTO bank_info (bank_name, bank_code) VALUES
('中国邮政储蓄银行', '100'),
('中国工商银行', '102'),
('中国农业银行', '103'),
('中国银行', '104'),
('中国建设银行', '105'); 