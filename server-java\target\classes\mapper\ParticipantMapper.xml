<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daka.pro.mapper.ParticipantMapper">

    <!-- 参建单位VO结果映射 -->
    <resultMap id="ParticipantVOMap" type="com.daka.pro.model.vo.ParticipantVO">
        <id column="id" property="id"/>
        <result column="participant_name" property="participantName"/>
        <result column="participant_code" property="participantCode"/>
        <result column="address" property="address"/>
        <result column="legal_representative" property="legalRepresentative"/>
        <result column="entrusted_agent" property="entrustedAgent"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询参建单位列表 -->
    <select id="selectParticipantPage" resultMap="ParticipantVOMap">
        SELECT
            id,
            participant_name,
            participant_code,
            address,
            legal_representative,
            entrusted_agent,
            contact_phone,
            create_time,
            update_time
        FROM participant
        <where>
            <if test="req.participantName != null and req.participantName != ''">
                AND participant_name LIKE CONCAT('%', #{req.participantName}, '%')
            </if>
            <if test="req.participantCode != null and req.participantCode != ''">
                AND participant_code LIKE CONCAT('%', #{req.participantCode}, '%')
            </if>
            <if test="req.legalRepresentative != null and req.legalRepresentative != ''">
                AND legal_representative LIKE CONCAT('%', #{req.legalRepresentative}, '%')
            </if>
            <if test="req.contactPhone != null and req.contactPhone != ''">
                AND contact_phone LIKE CONCAT('%', #{req.contactPhone}, '%')
            </if>
            <if test="req.address != null and req.address != ''">
                AND address LIKE CONCAT('%', #{req.address}, '%')
            </if>
            <if test="req.entrustedAgent != null and req.entrustedAgent != ''">
                AND entrusted_agent LIKE CONCAT('%', #{req.entrustedAgent}, '%')
            </if>
            <if test="req.createTimeStart != null and req.createTimeStart != ''">
                AND create_time >= #{req.createTimeStart}
            </if>
            <if test="req.createTimeEnd != null and req.createTimeEnd != ''">
                AND create_time &lt;= #{req.createTimeEnd}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询参建单位详情 -->
    <select id="selectParticipantById" resultMap="ParticipantVOMap">
        SELECT
            id,
            participant_name,
            participant_code,
            address,
            legal_representative,
            entrusted_agent,
            contact_phone,
            create_time,
            update_time
        FROM participant
        WHERE id = #{id}
    </select>



</mapper>
