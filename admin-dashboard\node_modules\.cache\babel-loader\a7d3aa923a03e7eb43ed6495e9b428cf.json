{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue", "mtime": 1753349360417}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\babel.config.js", "mtime": 1746865124045}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getWorkerDetail", "getWorkerByIdCard", "axios", "name", "props", "workerInfo", "type", "Object", "required", "data", "webSocket", "scannerConnected", "workerForm", "workerId", "worker<PERSON>ame", "idCardNumber", "gender", "home<PERSON>dd<PERSON>", "teamName", "teamCode", "participantName", "participantCode", "projectSubContractorId", "projectCode", "projectName", "jobPosition", "rules", "message", "trigger", "simulationMode", "connectionTimeout", "manuallyDisconnected", "scannerConfig", "wsUrl", "timeout", "autoSimulate", "ocrApiUrl", "configDialogVisible", "tempScannerConfig", "currentPhotoPath", "isProcessingOcr", "currentImageData", "waitingForBase64", "created", "initFormFromProps", "$route", "query", "tryRestoreFromLocalStorage", "console", "log", "JSON", "stringify", "mounted", "initScannerWebSocket", "watch", "handler", "newVal", "oldVal", "deep", "immediate", "<PERSON><PERSON><PERSON><PERSON>", "_this$$route", "closeWebSocket", "path", "includes", "clearContractData", "saveToLocalStorage", "methods", "localStorage", "removeItem", "shouldEmit", "savedWorkerInfo", "getItem", "parsedData", "parse", "keys", "for<PERSON>ach", "key", "undefined", "$emit", "error", "WebSocket", "setTimeout", "warn", "switchToSimulationMode", "$message", "onopen", "event", "success", "clearTimeout", "onclose", "onerror", "onmessage", "handleScannerMessage", "begin_data", "indexOf", "barcode", "replace", "isIdCardNumber", "searchWorkerByIdCard", "result", "base64Data", "length", "processOcrWithImage", "$refs", "scannerPreview", "src", "startsWith", "testData", "substring", "window", "atob", "imgData", "e", "startScanner", "sendScannerCommand", "stopScanner", "scanIdCard", "warning", "processOcrWithSimulationImage", "timestamp", "Date", "getTime", "filename", "info", "imagePath", "scaleImageForOcr", "then", "scaledImageData", "split", "config", "headers", "jsonData", "image", "post", "response", "handleOcrResult", "catch", "_error$response", "formData", "FormData", "byteCharacters", "byteArrays", "i", "push", "charCodeAt", "byteArray", "Uint8Array", "blob", "Blob", "fileName", "file", "File", "append", "callOcrApi", "finally", "sendOriginalImage", "imageDataUrl", "Promise", "resolve", "reject", "img", "Image", "onload", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "canvas", "document", "createElement", "ctx", "getContext", "drawImage", "toDataURL", "_error$response2", "_error$response3", "ocrData", "ocr<PERSON><PERSON><PERSON>", "address", "id_number", "$notify", "title", "duration", "command", "readyState", "OPEN", "send", "Error", "close", "str", "reg", "test", "addressFromOcr", "code", "workerData", "id", "teamSysName", "teamSysNo", "workerType", "projectSubContractorName", "corpName", "corpCode", "corpId", "msg", "extractInfoFromIdCard", "genderCode", "parseInt", "char<PERSON>t", "searchWorker", "refreshWorkerInfo", "existingData", "savedData", "dataToSave", "setItem", "nextStep", "validate", "valid", "completeData", "dataToUpdate", "$nextTick", "handleUploadImage", "isImage", "isLt10M", "size", "reader", "FileReader", "readAsDataURL", "compressImage", "target", "compressedDataUrl", "dataUrl", "callback", "quality", "originalSize", "compressedSize", "compressionRatio", "toFixed", "enhanceImage", "cropIdCard", "adjustBrightnessContrast", "sharpenImage", "imageData", "getImageData", "minX", "minY", "maxX", "maxY", "threshold", "y", "x", "idx", "brightness", "Math", "min", "max", "padding", "croppedData", "putImageData", "contrast", "factor", "sharpenData", "Uint8ClampedArray", "kernel", "c", "sum", "ky", "kx", "enhancedImageData", "ImageData", "_error$response4"], "sources": ["src/views/contract/ContractStep1.vue"], "sourcesContent": ["<template>\n  <div class=\"contract-step1-container\">\n    <h3>步骤1：工人身份信息识别</h3>\n    \n    <div class=\"scanner-container\">\n      <div class=\"scanner-preview\">\n        <img id=\"photo\" src=\"\" width=\"600\" height=\"400\" ref=\"scannerPreview\">\n      </div>\n      \n      <div class=\"scanner-controls\">\n        <el-button type=\"primary\" @click=\"startScanner\">启动高拍仪</el-button>\n        <el-button type=\"success\" @click=\"scanIdCard\">识别身份证</el-button>\n        <el-button type=\"warning\" @click=\"stopScanner\">停止高拍仪</el-button>\n        <el-upload\n          class=\"upload-button\"\n          action=\"#\"\n          :show-file-list=\"false\"\n          :before-upload=\"handleUploadImage\">\n          <el-button type=\"primary\" icon=\"el-icon-upload\">上传图片</el-button>\n        </el-upload>\n      </div>\n    </div>\n    \n    <div class=\"worker-info-form\">\n      <el-form :model=\"workerForm\" label-width=\"120px\" ref=\"workerForm\" :rules=\"rules\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\n              <el-input v-model=\"workerForm.workerName\" placeholder=\"请输入工人姓名\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\n              <div class=\"input-with-button\">\n              <el-input v-model=\"workerForm.idCardNumber\" placeholder=\"请输入证件号码\"></el-input>\n                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refreshWorkerInfo\" :disabled=\"!workerForm.idCardNumber\" title=\"重新获取信息\">刷新</el-button>\n              </div>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"性别\" prop=\"gender\">\n              <el-select v-model=\"workerForm.gender\" placeholder=\"请选择性别\" style=\"width: 100%\">\n                <el-option label=\"男\" value=\"男\"></el-option>\n                <el-option label=\"女\" value=\"女\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"家庭住址\" prop=\"homeAddress\">\n              <el-input v-model=\"workerForm.homeAddress\" placeholder=\"请输入家庭住址\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"班组\" prop=\"teamName\">\n              <el-input v-model=\"workerForm.teamName\" placeholder=\"请输入班组名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"工种\" prop=\"jobPosition\">\n              <el-input v-model=\"workerForm.jobPosition\" placeholder=\"请输入工种\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"参建单位\" prop=\"participantName\">\n              <el-input v-model=\"workerForm.participantName\" placeholder=\"请输入参建单位\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      \n      <div class=\"form-actions\">\n        <el-button type=\"primary\" @click=\"searchWorker\">查找工人</el-button>\n        <el-button type=\"success\" @click=\"nextStep\">下一步</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getWorkerDetail, getWorkerByIdCard } from '@/api/roster'\nimport axios from 'axios'\n\nexport default {\n  name: 'ContractStep1',\n  props: {\n    workerInfo: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      webSocket: null,\n      scannerConnected: false,\n      workerForm: {\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        teamName: '',\n        teamCode: '',\n        participantName: '',\n        participantCode: '',\n        projectSubContractorId: '',\n        projectCode: '',\n        projectName: '',\n        jobPosition: ''\n      },\n      rules: {\n        workerName: [\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\n        ],\n        idCardNumber: [\n          { required: true, message: '请输入证件号码', trigger: 'blur' }\n        ],\n        homeAddress: [\n          { required: true, message: '请输入家庭住址', trigger: 'blur' }\n        ],\n        teamName: [\n          { required: true, message: '请输入班组名称', trigger: 'blur' }\n        ],\n        participantName: [\n          { required: true, message: '请输入参建单位', trigger: 'blur' }\n        ]\n      },\n      simulationMode: false, // 模拟模式标志\n      connectionTimeout: null, // 连接超时\n      manuallyDisconnected: false, // 手动断开标志\n      scannerConfig: {\n        wsUrl: 'ws://localhost:1818', // WebSocket连接地址\n        timeout: 3000, // 连接超时时间(毫秒)\n        autoSimulate: true, // 连接失败时是否自动切换到模拟模式\n        ocrApiUrl: '/ocr' // OCR API地址\n      },\n      configDialogVisible: false, // 高拍仪配置对话框可见性\n      tempScannerConfig: { // 临时存储的高拍仪配置\n        wsUrl: 'ws://localhost:1818',\n        timeout: 3000,\n        autoSimulate: true,\n        ocrApiUrl: '/ocr'\n      },\n      currentPhotoPath: '', // 当前拍摄的照片路径\n      isProcessingOcr: false, // 是否正在处理OCR\n      currentImageData: '', // 当前接收到的图像数据\n      waitingForBase64: false // 是否正在等待 Base64Encode 的响应\n    }\n  },\n  created() {\n    // 从props初始化表单\n    this.initFormFromProps()\n    \n    // 从URL获取项目编码\n    const { projectCode } = this.$route.query\n    if (projectCode) {\n      this.workerForm.projectCode = projectCode\n    }\n    \n    // 如果props中没有必要的数据，尝试从本地存储恢复\n    if (!this.workerForm.workerName && !this.workerForm.idCardNumber) {\n      this.tryRestoreFromLocalStorage(false) // 传false参数表示不要emit到父组件\n    }\n    \n    // 添加调试日志\n    console.log('ContractStep1 created - 初始化后的表单数据:', JSON.stringify(this.workerForm))\n  },\n  mounted() {\n    // 初始化高拍仪WebSocket连接\n    this.initScannerWebSocket()\n    \n    console.log('ContractStep1 mounted, 当前表单数据:', JSON.stringify(this.workerForm))\n  },\n  watch: {\n    // 监听workerInfo变化\n    workerInfo: {\n      handler(newVal, oldVal) {\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          console.log('ContractStep1 - workerInfo变化:', JSON.stringify(newVal))\n          \n          // 更新表单数据\n          this.initFormFromProps()\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  beforeDestroy() {\n    // 组件销毁前关闭WebSocket连接\n    this.closeWebSocket()\n\n    // 只有当完全离开合同流程时才清空数据\n    // 不要在步骤之间导航时清空\n    console.log('ContractStep1 beforeDestroy - 当前路径:', this.$route?.path)\n    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {\n      console.log('ContractStep1 - 离开合同流程，清空数据')\n      this.clearContractData()\n    } else {\n      console.log('ContractStep1 - 在合同流程内导航，保留数据')\n      // 确保数据已保存到localStorage\n      this.saveToLocalStorage()\n    }\n  },\n  methods: {\n    // 清空合同数据\n    clearContractData() {\n      console.log('ContractStep1 - 清空合同数据')\n      localStorage.removeItem('contractWorkerInfo')\n    },\n    \n    // 尝试从本地存储恢复数据\n    tryRestoreFromLocalStorage(shouldEmit = true) {\n      try {\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\n        if (savedWorkerInfo) {\n          const parsedData = JSON.parse(savedWorkerInfo)\n          console.log('ContractStep1 - 从本地存储恢复数据:', JSON.stringify(parsedData))\n          \n          // 更新表单数据\n          Object.keys(this.workerForm).forEach(key => {\n            if (parsedData[key] !== undefined && parsedData[key] !== null) {\n              this.workerForm[key] = parsedData[key]\n            }\n          })\n          \n          // 只在需要时更新父组件数据\n          if (shouldEmit) {\n            this.$emit('update-worker-info', { ...parsedData })\n          }\n          \n          return true\n        }\n      } catch (error) {\n        console.error('ContractStep1 - 从本地存储恢复数据失败:', error)\n      }\n      return false\n    },\n    \n    // 从props初始化表单\n    initFormFromProps() {\n      if (this.workerInfo) {\n        console.log('ContractStep1 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))\n        \n        // 复制属性到表单，只复制有值的字段\n        Object.keys(this.workerForm).forEach(key => {\n          if (this.workerInfo[key] !== undefined && this.workerInfo[key] !== null && this.workerInfo[key] !== '') {\n            this.workerForm[key] = this.workerInfo[key]\n          }\n        })\n        \n        console.log('ContractStep1 - 从props初始化数据后:', JSON.stringify(this.workerForm))\n      }\n    },\n    \n    // 初始化高拍仪WebSocket连接\n    initScannerWebSocket() {\n      try {\n        // 添加模拟模式标志\n        this.simulationMode = false;\n        \n        // 尝试连接WebSocket\n        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)\n        \n        // 设置连接超时\n        this.connectionTimeout = setTimeout(() => {\n          if (!this.scannerConnected) {\n            console.warn('高拍仪连接超时，切换到模拟模式');\n            if (this.scannerConfig.autoSimulate) {\n              this.switchToSimulationMode();\n            } else {\n              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\n            }\n          }\n        }, this.scannerConfig.timeout);\n        \n        this.webSocket.onopen = (event) => {\n          console.log('高拍仪WebSocket连接成功')\n          this.scannerConnected = true\n          this.$message.success('高拍仪连接成功')\n          clearTimeout(this.connectionTimeout);\n        }\n        \n        this.webSocket.onclose = (event) => {\n          console.log('高拍仪WebSocket连接关闭')\n          this.scannerConnected = false\n          // 如果不是主动关闭，尝试切换到模拟模式\n          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {\n            this.switchToSimulationMode();\n          }\n        }\n        \n        this.webSocket.onerror = (event) => {\n          console.error('高拍仪WebSocket连接错误', event)\n          this.scannerConnected = false\n          if (this.scannerConfig.autoSimulate) {\n            this.switchToSimulationMode();\n          } else {\n            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\n          }\n        }\n        \n        this.webSocket.onmessage = (event) => {\n          this.handleScannerMessage(event)\n        }\n      } catch (error) {\n        console.error('初始化高拍仪WebSocket失败', error)\n        if (this.scannerConfig.autoSimulate) {\n          this.switchToSimulationMode();\n        } else {\n          this.$message.error(`初始化高拍仪失败: ${error.message}`);\n        }\n      }\n    },\n    \n    // 处理高拍仪消息\n    handleScannerMessage(event) {\n      const begin_data = \"data:image/jpeg;base64,\"\n      \n      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {\n        // 处理条码识别结果\n        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')\n        this.$message.success('识别到条码: ' + barcode)\n        \n        // 如果是身份证号格式，填入表单\n        if (this.isIdCardNumber(barcode)) {\n          this.workerForm.idCardNumber = barcode\n          this.searchWorkerByIdCard(barcode)\n        }\n      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {\n        // 处理保存图片结果\n        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')\n        this.$message.success('图片保存成功: ' + result)\n      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {\n        // 处理 Base64Encode 命令返回的 base64 数据\n        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')\n        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)\n        \n        // 重置等待标志\n        this.waitingForBase64 = false\n        \n        if (base64Data && base64Data.length > 1000) { // 确保数据有效\n          // 保存 base64 数据\n          this.currentImageData = begin_data + base64Data\n          \n          // 使用高质量 base64 数据进行 OCR 识别\n          console.log('使用高质量 base64 数据进行 OCR 识别')\n          this.processOcrWithImage(this.currentImageData)\n        } else {\n          console.error('获取到的 base64 数据无效或太短')\n          \n          // 如果 base64 数据无效，尝试使用预览图或文件路径\n          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \n              this.$refs.scannerPreview.src.startsWith('data:image')) {\n            console.log('使用预览图数据进行 OCR 识别')\n            this.processOcrWithImage(this.$refs.scannerPreview.src)\n          } else {\n            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\n            this.processOcrWithImage(this.currentPhotoPath)\n          }\n        }\n      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {\n        // 处理目录检查结果\n        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')\n        console.log('目录检查结果:', result)\n        // 如果目录不存在，结果为\"0\"，存在则为\"1\"\n        if (result === \"0\") {\n          console.log('C:\\\\pic\\\\ 目录不存在，将创建')\n        }\n      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {\n        // 处理创建目录结果\n        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')\n        console.log('创建目录结果:', result)\n        // 如果创建成功，结果为\"1\"，失败则为\"0\"\n        if (result === \"1\") {\n          console.log('C:\\\\pic\\\\ 目录创建成功')\n        } else {\n          console.warn('C:\\\\pic\\\\ 目录创建失败或已存在')\n        }\n      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {\n        // 处理条码识别命令响应，不作为图像数据处理\n        console.log('收到条码识别命令响应:', event.data)\n      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {\n        // 处理图像数据 - 判断是否为base64图像数据\n        // 增加额外检查，确保不是条码数据\n        if (this.$refs.scannerPreview) {\n          try {\n            // 尝试验证是否为有效的base64图像数据\n            const testData = event.data.substring(0, 100); // 只取前100个字符测试\n            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常\n            \n            // 确保是完整的base64数据\n            const imgData = begin_data + event.data\n            this.$refs.scannerPreview.src = imgData\n            \n            // 保存当前图像数据以备后用\n            this.currentImageData = imgData\n            console.log('成功保存图像数据，长度:', event.data.length)\n          } catch (e) {\n            console.error('收到的数据不是有效的base64图像:', e)\n          }\n        }\n      } else {\n        // 其他消息，可能是普通文本或命令响应\n        console.log('收到高拍仪消息:', event.data)\n      }\n    },\n    \n    // 启动高拍仪\n    startScanner() {\n      if (!this.scannerConnected) {\n        this.initScannerWebSocket()\n        return\n      }\n      \n      try {\n        // 设置分辨率\n        this.sendScannerCommand('vSetResolution(8)')\n        \n        // 启用去黑边功能\n        this.sendScannerCommand('vSetDelHBFlag(true)')\n        \n        // 增加亮度控制\n        this.sendScannerCommand('vSetBrightness(80)') // 增加亮度\n        \n        // 增加对比度控制\n        this.sendScannerCommand('vSetContrast(70)') // 增加对比度\n        \n        // 设置曝光\n        this.sendScannerCommand('vSetExposure(60)') // 设置曝光\n        \n        // 启动主摄像头\n        this.sendScannerCommand('bStartPlay()')\n        this.$message.success('高拍仪已启动')\n      } catch (error) {\n        console.error('启动高拍仪失败', error)\n        this.$message.error('启动高拍仪失败')\n      }\n    },\n    \n    // 停止高拍仪\n    stopScanner() {\n      if (!this.scannerConnected) {\n        return\n      }\n      \n      try {\n        this.sendScannerCommand('bStopPlay()')\n        this.$message.success('高拍仪已停止')\n      } catch (error) {\n        console.error('停止高拍仪失败', error)\n        this.$message.error('停止高拍仪失败')\n      }\n    },\n    \n    // 扫描身份证\n    scanIdCard() {\n      if (!this.scannerConnected && !this.simulationMode) {\n        this.$message.warning('请先启动高拍仪')\n        return\n      }\n      \n      try {\n        if (this.simulationMode) {\n          // 模拟模式下，直接调用OCR接口处理示例图片\n          this.processOcrWithSimulationImage()\n          return\n        }\n        \n        // 确保启用去黑边功能\n        this.sendScannerCommand('vSetDelHBFlag(true)')\n        \n        // 设置身份证自动寻边模式\n        this.sendScannerCommand('bSetMode(4)')\n        \n        // 先检查目录是否存在，不存在则创建\n        this.sendScannerCommand('bDirIsExist(C:\\\\pic\\\\)')\n        \n        // 延迟一下，确保目录检查完成\n        setTimeout(() => {\n          // 创建目录（即使目录已存在，这个命令也不会报错）\n          this.sendScannerCommand('bCreateDir(C:\\\\pic\\\\)')\n          \n          // 生成唯一文件名（使用时间戳）\n          const timestamp = new Date().getTime()\n          const filename = `idcard_${timestamp}`\n          this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\n          console.log('当前照片路径:', this.currentPhotoPath)\n          \n          // 拍照并保存到本地\n          this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\n          \n          // 设置一个标志，表示我们正在等待 Base64Encode 的响应\n          this.waitingForBase64 = true\n          \n          // 延迟一下，确保图片保存完成\n          setTimeout(() => {\n            // 使用 Base64Encode 命令获取高质量的 base64 图像数据\n            this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)\n            \n            // 识别条码\n            this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)\n            \n            // 清除之前的图像数据，确保不会使用旧数据\n            this.currentImageData = null\n            \n            // 设置超时，确保即使没有收到 Base64Encode 的响应，也会调用 OCR 接口\n            setTimeout(() => {\n              if (this.waitingForBase64) {\n                console.log('Base64Encode 响应超时，使用备用方法调用 OCR')\n                this.waitingForBase64 = false\n                \n                // 如果有预览图数据，使用预览图数据\n                if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \n                    this.$refs.scannerPreview.src.startsWith('data:image')) {\n                  console.log('使用预览图数据进行 OCR 识别')\n                  this.processOcrWithImage(this.$refs.scannerPreview.src)\n                } else {\n                  // 否则使用文件路径\n                  console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\n                  this.processOcrWithImage(this.currentPhotoPath)\n                }\n              }\n            }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法\n            \n          }, 1000) // 延迟1秒，确保图片保存完成\n        }, 500) // 延迟500ms，确保目录检查完成\n        \n        this.$message.info('正在识别身份证，请稍候...')\n      } catch (error) {\n        console.error('扫描身份证失败', error)\n        this.$message.error('扫描身份证失败')\n      }\n    },\n    \n         // 处理OCR识别结果\n    processOcrWithImage(imagePath) {\n      if (this.isProcessingOcr) {\n        return\n      }\n      \n      this.isProcessingOcr = true\n      this.$message.info('正在进行OCR识别...')\n      \n      // 判断是否是base64格式的图片数据\n      if (imagePath.startsWith('data:image')) {\n        // 对图片进行缩放处理，然后再进行OCR识别\n        this.scaleImageForOcr(imagePath).then(scaledImageData => {\n          console.log('图片已缩放处理，准备进行OCR识别')\n          \n          // 创建文件对象从base64数据\n          const base64Data = scaledImageData.split(',')[1]\n          \n          // 添加JSON格式的请求头\n          const config = {\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          }\n          \n          // 创建JSON数据\n          const jsonData = {\n            image: base64Data\n          }\n          \n          // 调用OCR API - 使用JSON格式发送\n          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)\n            .then(response => {\n              console.log('OCR API 响应:', response)\n              this.handleOcrResult(response.data)\n            })\n            .catch(error => {\n              console.error('OCR识别失败', error)\n              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n              \n              // 如果JSON格式失败，尝试使用表单数据\n              console.log('尝试使用表单数据格式重新发送请求')\n              \n              // 创建表单数据\n              const formData = new FormData()\n              const byteCharacters = atob(base64Data)\n              const byteArrays = []\n              \n              for (let i = 0; i < byteCharacters.length; i++) {\n                byteArrays.push(byteCharacters.charCodeAt(i))\n              }\n              \n              const byteArray = new Uint8Array(byteArrays)\n              const blob = new Blob([byteArray], { type: 'image/jpeg' })\n              \n              // 创建文件对象\n              const fileName = `idcard_${new Date().getTime()}.jpg`\n              const file = new File([blob], fileName, { type: 'image/jpeg' })\n              \n              // 添加到表单\n              formData.append('image', file)\n              this.callOcrApi(formData)\n            })\n            .finally(() => {\n              this.isProcessingOcr = false\n            })\n        }).catch(error => {\n          console.error('图像缩放处理失败:', error)\n          \n          // 如果缩放处理失败，直接使用原始图像\n          this.sendOriginalImage(imagePath)\n        })\n      } else {\n        // 如果是文件路径，尝试读取文件并上传\n        const formData = new FormData()\n        formData.append('image_path', imagePath)\n        console.log(`发送图片路径进行OCR识别: ${imagePath}`)\n        \n        // 调用OCR API\n        this.callOcrApi(formData)\n      }\n    },\n    \n    // 对图片进行缩放处理，只进行尺寸调整\n    scaleImageForOcr(imageDataUrl) {\n      return new Promise((resolve, reject) => {\n        try {\n          const img = new Image()\n          img.src = imageDataUrl\n          \n          img.onload = () => {\n            // 判断是否需要缩放\n            if (img.width <= 1000 && img.height <= 630) {\n              console.log('图片尺寸已经合适，无需缩放')\n              resolve(imageDataUrl)\n              return\n            }\n            \n            // 为OCR识别优化的尺寸，减小尺寸提高处理速度\n            // 身份证比例大约是1.58:1\n            const maxWidth = 1000  // 从1800减小到1000\n            const maxHeight = 630  // 从1140减小到630\n            \n            // 计算等比例缩放后的尺寸\n            let width = img.width\n            let height = img.height\n            \n            if (width > maxWidth) {\n              height = (height * maxWidth) / width\n              width = maxWidth\n            }\n            \n            if (height > maxHeight) {\n              width = (width * maxHeight) / height\n              height = maxHeight\n            }\n            \n            // 创建Canvas\n            const canvas = document.createElement('canvas')\n            canvas.width = width\n            canvas.height = height\n            const ctx = canvas.getContext('2d')\n            \n            // 绘制图像\n            ctx.drawImage(img, 0, 0, width, height)\n            \n            // 转换为适中质量JPEG，进一步减小文件大小\n            const scaledImageData = canvas.toDataURL('image/jpeg', 0.85)\n            \n            // 输出调试信息\n            console.log(`图片已缩放: 原始尺寸=${img.width}x${img.height}, 缩放尺寸=${width}x${height}`)\n            \n            resolve(scaledImageData)\n          }\n          \n          img.onerror = (error) => {\n            console.error('图像加载失败:', error)\n            reject(error)\n          }\n        } catch (e) {\n          console.error('图像缩放处理失败:', e)\n          reject(e)\n        }\n      })\n    },\n    \n    // 发送原始图像\n    sendOriginalImage(imagePath) {\n      try {\n        console.log('使用原始图像数据进行OCR识别')\n        \n        if (imagePath.startsWith('data:image')) {\n          // 创建文件对象从base64数据\n          const base64Data = imagePath.split(',')[1]\n          \n          // 添加JSON格式的请求头\n          const config = {\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          }\n          \n          // 创建JSON数据\n          const jsonData = {\n            image: base64Data\n          }\n          \n          // 使用JSON格式发送\n          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)\n            .then(response => {\n              console.log('OCR API 响应:', response)\n              this.handleOcrResult(response.data)\n            })\n            .catch(error => {\n              console.error('OCR识别失败', error)\n              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n            })\n            .finally(() => {\n              this.isProcessingOcr = false\n            })\n        } else {\n          // 如果是文件路径\n          const formData = new FormData()\n          formData.append('image_path', imagePath)\n          this.callOcrApi(formData)\n        }\n      } catch (e) {\n        console.error('发送原始图像失败:', e)\n        this.isProcessingOcr = false\n        this.$message.error('发送图像失败: ' + e.message)\n      }\n    },\n    \n    // 模拟模式下使用示例图片进行OCR识别\n    processOcrWithSimulationImage() {\n      this.isProcessingOcr = true\n      this.$message.info('模拟模式：正在进行OCR识别...')\n      \n      // 准备表单数据\n      const formData = new FormData()\n      formData.append('simulation', 'true')\n      \n      // 调用OCR API\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\n        .then(response => {\n          this.handleOcrResult(response.data)\n        })\n        .catch(error => {\n          console.error('模拟OCR识别失败', error)\n          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))\n        })\n        .finally(() => {\n          this.isProcessingOcr = false\n        })\n    },\n    \n    // 处理OCR识别结果\n    handleOcrResult(result) {\n      if (!result || !result.success) {\n        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))\n        return\n      }\n      \n      this.$message.success('OCR识别成功')\n      console.log('OCR识别结果:', result)\n      \n      // 更新表单数据\n      const ocrData = result.data || {}\n      \n      // 保存OCR识别的地址，以便在API查询后仍能使用\n      const ocrAddress = ocrData.address || ''\n      \n      // 更新身份证号\n      if (ocrData.id_number) {\n        this.workerForm.idCardNumber = ocrData.id_number\n        // 添加更明显的提示\n        this.$notify({\n          title: '证件号码识别成功',\n          type: 'success',\n          duration: 5000\n        })\n      }\n      \n      // 更新姓名\n      if (ocrData.name) {\n        this.workerForm.workerName = ocrData.name\n      }\n      \n      // 更新性别\n      if (ocrData.gender) {\n        this.workerForm.gender = ocrData.gender\n      }\n      \n      // 更新地址\n      if (ocrData.address) {\n        this.workerForm.homeAddress = ocrData.address\n        // 添加地址识别成功的提示\n        this.$notify({\n          title: '地址识别成功',\n          message: `识别到地址: ${ocrData.address}`,\n          type: 'success',\n          duration: 5000\n        })\n      }\n      \n      // 如果有身份证号，尝试从系统中查询更多信息\n      if (ocrData.id_number) {\n        // 传递OCR识别的地址作为参数\n        this.searchWorkerByIdCard(ocrData.id_number, ocrAddress)\n      }\n    },\n    \n    // 发送高拍仪命令\n    sendScannerCommand(command) {\n      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {\n        this.webSocket.send(command)\n      } else {\n        throw new Error('WebSocket未连接')\n      }\n    },\n    \n    // 关闭WebSocket连接\n    closeWebSocket() {\n      if (this.webSocket) {\n        // 先停止高拍仪\n        if (this.scannerConnected) {\n          try {\n            this.webSocket.send('bStopPlay()')\n          } catch (e) {\n            console.error('停止高拍仪失败', e)\n          }\n        }\n        \n        // 关闭连接\n        this.webSocket.close()\n        this.webSocket = null\n        this.scannerConnected = false\n      }\n    },\n    \n    // 验证是否为身份证号\n    isIdCardNumber(str) {\n      // 简单验证18位或15位身份证号\n      const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\n      return reg.test(str)\n    },\n    \n    // 根据身份证号查询工人信息\n    searchWorkerByIdCard(idCardNumber, addressFromOcr = '') {\n      if (!idCardNumber) {\n        this.$message.warning('证件号码不能为空')\n        return\n      }\n      \n      this.$message.info('正在查询工人信息...')\n      \n      // 调用API根据身份证号查询工人信息\n      getWorkerByIdCard(idCardNumber)\n        .then(response => {\n          if (response.code === 0 && response.data) {\n            const workerData = response.data\n            \n            // 更新表单数据\n            this.workerForm.workerId = workerData.id || ''\n            this.workerForm.workerName = workerData.workerName || ''\n            this.workerForm.gender = workerData.gender || '男'\n            this.workerForm.homeAddress = workerData.homeAddress || ''\n            this.workerForm.teamName = workerData.teamSysName || ''\n            this.workerForm.teamCode = workerData.teamSysNo || ''\n            this.workerForm.jobPosition = workerData.workerType || ''\n            this.workerForm.participantName = workerData.projectSubContractorName || workerData.corpName || ''\n            this.workerForm.participantCode = workerData.corpCode || ''\n            this.workerForm.projectSubContractorId = workerData.projectSubContractorId || workerData.corpId || ''\n            this.workerForm.projectCode = workerData.projectCode || this.workerForm.projectCode\n            this.workerForm.projectName = workerData.projectName || ''\n            \n            // 如果API没有返回地址，但OCR识别到了地址，则使用OCR识别的地址\n            if ((!workerData.homeAddress || workerData.homeAddress === '') && addressFromOcr) {\n              console.log('使用OCR识别的地址:', addressFromOcr)\n              this.workerForm.homeAddress = addressFromOcr\n            }\n            \n            // 保存到本地存储\n            this.saveToLocalStorage()\n        \n            this.$message.success('工人信息查询成功')\n            \n            // 添加更详细的通知\n            this.$notify({\n              title: '工人信息查询成功',\n              message: `已找到工人: ${workerData.workerName}，所属班组: ${workerData.teamSysName || '未知'}`,\n              type: 'success',\n              duration: 5000\n            })\n          } else {\n            this.$message.warning(response.msg || '未找到工人信息')\n            \n            // 如果没有找到，可以保留一些基本信息\n            if (this.workerForm.idCardNumber && !this.workerForm.workerName) {\n              // 从身份证号提取出生日期和性别信息\n              this.extractInfoFromIdCard(idCardNumber)\n            }\n            \n            // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址\n            if (addressFromOcr) {\n              console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)\n              this.workerForm.homeAddress = addressFromOcr\n            }\n          }\n        })\n        .catch(error => {\n          console.error('查询工人信息失败:', error)\n          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))\n          \n          // 如果API调用失败，可以尝试从身份证号提取一些基本信息\n          if (this.workerForm.idCardNumber) {\n            this.extractInfoFromIdCard(idCardNumber)\n          }\n          \n          // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址\n          if (addressFromOcr) {\n            console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)\n            this.workerForm.homeAddress = addressFromOcr\n          }\n        })\n    },\n    \n    // 从身份证号提取信息\n    extractInfoFromIdCard(idCardNumber) {\n      if (!idCardNumber || idCardNumber.length < 18) return\n      \n      try {\n        // 提取性别 (第17位，奇数为男，偶数为女)\n        const genderCode = parseInt(idCardNumber.charAt(16))\n        this.workerForm.gender = genderCode % 2 === 1 ? '男' : '女'\n        \n        // 可以添加更多提取逻辑，如出生日期等\n        console.log('从身份证号提取的性别:', this.workerForm.gender)\n      } catch (e) {\n        console.error('从身份证号提取信息失败:', e)\n      }\n    },\n    \n    // 查找工人\n    searchWorker() {\n      if (!this.workerForm.idCardNumber) {\n        this.$message.warning('请先输入证件号码')\n        return\n      }\n      \n      // 根据证件号码查询工人信息\n      this.searchWorkerByIdCard(this.workerForm.idCardNumber)\n    },\n    \n    // 刷新工人信息\n    refreshWorkerInfo() {\n      if (this.workerForm.idCardNumber) {\n        this.$message.info('正在重新获取工人信息...')\n        this.searchWorkerByIdCard(this.workerForm.idCardNumber)\n      } else {\n        this.$message.warning('请先输入证件号码')\n      }\n    },\n    \n    // 保存数据到本地存储\n    saveToLocalStorage() {\n      try {\n        // 合并表单数据和已有数据\n        let existingData = {}\n        try {\n          const savedData = localStorage.getItem('contractWorkerInfo')\n          if (savedData) {\n            existingData = JSON.parse(savedData)\n          }\n        } catch (e) {}\n        \n        const dataToSave = { \n          ...existingData,\n          ...this.workerForm \n        }\n        \n        console.log('ContractStep1 保存数据到本地存储:', JSON.stringify(dataToSave))\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave))\n        return dataToSave\n      } catch (error) {\n        console.error('保存到本地存储失败:', error)\n        return this.workerForm\n      }\n    },\n    \n    // 下一步\n    nextStep() {\n      this.$refs.workerForm.validate(valid => {\n        if (valid) {\n          // 关闭高拍仪\n          if (this.scannerConnected) {\n            this.stopScanner();\n            this.closeWebSocket();\n            console.log('下一步操作：已自动关闭高拍仪');\n          }\n          \n          // 添加详细的调试日志\n          console.log('=== ContractStep1 nextStep 开始 ===')\n          console.log('ContractStep1 当前表单数据:', JSON.stringify(this.workerForm))\n\n          // 保存到本地存储并获取完整数据\n          const completeData = this.saveToLocalStorage()\n          console.log('ContractStep1 保存到localStorage的完整数据:', JSON.stringify(completeData))\n\n          // 准备一个包含所有必要字段的数据对象\n          const dataToUpdate = {\n            ...completeData,\n            // 确保这些重要字段一定会被传递\n            workerName: this.workerForm.workerName,\n            idCardNumber: this.workerForm.idCardNumber,\n            gender: this.workerForm.gender,\n            homeAddress: this.workerForm.homeAddress,\n            teamName: this.workerForm.teamName,\n            jobPosition: this.workerForm.jobPosition,\n            participantName: this.workerForm.participantName\n          }\n\n          console.log('ContractStep1 准备发送给父组件的数据:', JSON.stringify(dataToUpdate))\n          console.log('ContractStep1 关键字段检查:')\n          console.log('- workerName:', dataToUpdate.workerName)\n          console.log('- idCardNumber:', dataToUpdate.idCardNumber)\n\n          // 先更新父组件中的工人信息\n          this.$emit('update-worker-info', dataToUpdate)\n          console.log('ContractStep1 已发送update-worker-info事件')\n          \n          // 等待数据更新后再触发导航\n          this.$nextTick(() => {\n            // 发送下一步事件，让父组件处理导航\n            this.$emit('next-step')\n          })\n        } else {\n          this.$message.warning('请完善工人信息')\n        }\n      })\n    },\n    \n    // 切换到模拟模式\n    switchToSimulationMode() {\n      this.simulationMode = true;\n      this.scannerConnected = false;\n      \n      if (this.webSocket) {\n        this.manuallyDisconnected = true;\n        this.webSocket.close();\n        this.webSocket = null;\n      }\n      \n      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动输入信息或使用模拟识别功能。');\n      \n      // 清除连接超时\n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n      }\n    },\n\n    // 上传图片处理函数\n    handleUploadImage(file) {\n      if (file) {\n        // 验证文件类型\n        const isImage = file.type.indexOf('image/') !== -1;\n        if (!isImage) {\n          this.$message.error('请上传图片文件!');\n          return false;\n        }\n        \n        // 验证文件大小 (限制为10MB)\n        const isLt10M = file.size / 1024 / 1024 < 10;\n        if (!isLt10M) {\n          this.$message.error('图片大小不能超过10MB!');\n          return false;\n        }\n        \n        this.$message.info('正在处理图片，请稍候...');\n        \n        // 更新预览图并压缩图片\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = (e) => {\n          // 压缩图片\n          this.compressImage(e.target.result, (compressedDataUrl) => {\n            // 更新预览图\n            if (this.$refs.scannerPreview) {\n              this.$refs.scannerPreview.src = compressedDataUrl;\n            }\n            \n            // 保存图片路径并处理OCR\n            this.currentPhotoPath = compressedDataUrl;\n            this.$message.info('正在识别上传的图片，请稍候...');\n            \n            // 调用OCR识别\n            setTimeout(() => {\n              this.processOcrWithImage(this.currentPhotoPath);\n            }, 300); // 短暂延迟，确保UI更新\n          });\n        };\n        \n        reader.onerror = (error) => {\n          console.error('读取图片文件失败', error);\n          this.$message.error('读取图片文件失败');\n        };\n      }\n      return false; // 阻止默认的上传行为\n    },\n    \n    // 压缩图片函数\n    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {\n      const img = new Image();\n      img.src = dataUrl;\n      \n      img.onload = () => {\n        // 创建Canvas\n        const canvas = document.createElement('canvas');\n        let width = img.width;\n        let height = img.height;\n        \n        // 计算缩放比例\n        if (width > height) {\n          if (width > maxWidth) {\n            height *= maxWidth / width;\n            width = maxWidth;\n          }\n        } else {\n          if (height > maxHeight) {\n            width *= maxHeight / height;\n            height = maxHeight;\n          }\n        }\n        \n        // 设置Canvas大小\n        canvas.width = width;\n        canvas.height = height;\n        \n        // 绘制图像\n        const ctx = canvas.getContext('2d');\n        ctx.drawImage(img, 0, 0, width, height);\n        \n        // 转换为压缩后的DataURL\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\n        \n        // 计算压缩率\n        const originalSize = dataUrl.length;\n        const compressedSize = compressedDataUrl.length;\n        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);\n        \n        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);\n        \n        callback(compressedDataUrl);\n      };\n      \n      img.onerror = () => {\n        console.error('图片压缩失败');\n        callback(dataUrl); // 失败时使用原始图片\n      };\n    },\n\n    // 增强图像质量\n    enhanceImage(imageDataUrl) {\n      return new Promise((resolve, reject) => {\n        try {\n          const img = new Image();\n          img.src = imageDataUrl;\n          \n          img.onload = () => {\n            // 创建Canvas\n            const canvas = document.createElement('canvas');\n            canvas.width = img.width;\n            canvas.height = img.height;\n            const ctx = canvas.getContext('2d');\n            \n            // 绘制原始图像\n            ctx.drawImage(img, 0, 0, img.width, img.height);\n            \n            // 1. 自动裁剪 - 找到身份证区域\n            this.cropIdCard(canvas, ctx);\n            \n            // 2. 亮度和对比度增强\n            this.adjustBrightnessContrast(canvas, ctx, 50, 80); // 增加亮度和对比度\n            \n            // 3. 锐化处理\n            this.sharpenImage(canvas, ctx);\n            \n            // 返回处理后的图像\n            resolve(canvas.toDataURL('image/jpeg', 0.95)); // 使用高质量\n          };\n          \n          img.onerror = (error) => {\n            console.error('图像加载失败:', error);\n            reject(error);\n          };\n        } catch (e) {\n          console.error('图像增强处理失败:', e);\n          reject(e);\n        }\n      });\n    },\n    \n    // 裁剪身份证区域\n    cropIdCard(canvas, ctx) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        \n        // 查找非黑色区域边界\n        let minX = canvas.width, minY = canvas.height, maxX = 0, maxY = 0;\n        const threshold = 40; // 亮度阈值\n        \n        for (let y = 0; y < canvas.height; y++) {\n          for (let x = 0; x < canvas.width; x++) {\n            const idx = (y * canvas.width + x) * 4;\n            const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;\n            \n            if (brightness > threshold) {\n              minX = Math.min(minX, x);\n              minY = Math.min(minY, y);\n              maxX = Math.max(maxX, x);\n              maxY = Math.max(maxY, y);\n            }\n          }\n        }\n        \n        // 添加边距\n        const padding = 10;\n        minX = Math.max(0, minX - padding);\n        minY = Math.max(0, minY - padding);\n        maxX = Math.min(canvas.width, maxX + padding);\n        maxY = Math.min(canvas.height, maxY + padding);\n        \n        // 检查是否找到了有效区域\n        const width = maxX - minX;\n        const height = maxY - minY;\n        \n        if (width > 50 && height > 50 && width < canvas.width && height < canvas.height) {\n          // 裁剪图像\n          const croppedData = ctx.getImageData(minX, minY, width, height);\n          canvas.width = width;\n          canvas.height = height;\n          ctx.putImageData(croppedData, 0, 0);\n          console.log('成功裁剪身份证区域:', width, 'x', height);\n        } else {\n          console.log('未找到明确的身份证区域，跳过裁剪');\n        }\n      } catch (e) {\n        console.error('裁剪身份证区域失败:', e);\n      }\n    },\n    \n    // 调整亮度和对比度\n    adjustBrightnessContrast(canvas, ctx, brightness = 0, contrast = 0) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        \n        // 计算对比度因子\n        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));\n        \n        for (let i = 0; i < data.length; i += 4) {\n          // 亮度调整\n          data[i] += brightness;     // R\n          data[i + 1] += brightness; // G\n          data[i + 2] += brightness; // B\n          \n          // 对比度调整\n          data[i] = factor * (data[i] - 128) + 128;\n          data[i + 1] = factor * (data[i + 1] - 128) + 128;\n          data[i + 2] = factor * (data[i + 2] - 128) + 128;\n          \n          // 确保值在0-255范围内\n          data[i] = Math.min(255, Math.max(0, data[i]));\n          data[i + 1] = Math.min(255, Math.max(0, data[i + 1]));\n          data[i + 2] = Math.min(255, Math.max(0, data[i + 2]));\n        }\n        \n        ctx.putImageData(imageData, 0, 0);\n        console.log('成功调整亮度和对比度');\n      } catch (e) {\n        console.error('调整亮度和对比度失败:', e);\n      }\n    },\n    \n    // 锐化图像\n    sharpenImage(canvas, ctx) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n        const sharpenData = new Uint8ClampedArray(data);\n        \n        // 锐化卷积核\n        const kernel = [\n          0, -1, 0,\n          -1, 5, -1,\n          0, -1, 0\n        ];\n        \n        // 应用卷积\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            for (let c = 0; c < 3; c++) {\n              let sum = 0;\n              for (let ky = -1; ky <= 1; ky++) {\n                for (let kx = -1; kx <= 1; kx++) {\n                  const idx = ((y + ky) * width + (x + kx)) * 4 + c;\n                  sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n                }\n              }\n              sharpenData[(y * width + x) * 4 + c] = Math.min(255, Math.max(0, sum));\n            }\n          }\n        }\n        \n        // 更新图像数据\n        const enhancedImageData = new ImageData(sharpenData, width, height);\n        ctx.putImageData(enhancedImageData, 0, 0);\n        console.log('成功锐化图像');\n      } catch (e) {\n        console.error('锐化图像失败:', e);\n      }\n    },\n\n    // 调用OCR API\n    callOcrApi(formData) {\n      console.log('调用OCR API:', this.scannerConfig.ocrApiUrl)\n      \n      // 添加跨域请求头\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Access-Control-Allow-Origin': '*'\n        }\n      }\n      \n      axios.post(this.scannerConfig.ocrApiUrl, formData, config)\n        .then(response => {\n          console.log('OCR API 响应:', response)\n          this.handleOcrResult(response.data)\n        })\n        .catch(error => {\n          console.error('OCR识别失败', error)\n          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n        })\n        .finally(() => {\n          this.isProcessingOcr = false\n        })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.contract-step1-container {\n  padding: 20px;\n}\n\n.contract-step1-container h3 {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n  color: #303133;\n}\n\n.scanner-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  background-color: #f9fafc;\n}\n\n.scanner-preview {\n  width: 600px;\n  height: 400px;\n  margin-bottom: 20px;\n  border: 1px solid #dcdfe6;\n  background-color: #ebeef5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n}\n\n.scanner-preview img {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n.scanner-controls {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.upload-button {\n  display: inline-block;\n  margin-left: 10px;\n}\n\n.worker-info-form {\n  padding: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  background-color: #fff;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.input-with-button {\n  display: flex;\n  align-items: center;\n}\n\n.input-with-button .el-input {\n  flex: 1;\n  margin-right: 8px;\n}\n\n@media (max-width: 768px) {\n  .scanner-preview {\n    width: 100%;\n    height: 300px;\n  }\n  \n  .scanner-controls {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .upload-button {\n    margin-left: 0;\n    margin-top: 10px;\n  }\n  \n  .input-with-button {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .input-with-button .el-input {\n    margin-right: 0;\n    margin-bottom: 8px;\n  }\n}\n</style> "], "mappings": ";;;;;;;;;;;;;;;AAwFA,SAAAA,eAAA,EAAAC,iBAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,gBAAA;MACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,MAAA;QACAC,WAAA;QACAC,QAAA;QACAC,QAAA;QACAC,eAAA;QACAC,eAAA;QACAC,sBAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;MACA;MACAC,KAAA;QACAZ,UAAA,GACA;UAAAN,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,YAAA,GACA;UAAAP,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,WAAA,GACA;UAAAT,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,QAAA,GACA;UAAAV,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,eAAA,GACA;UAAAZ,QAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,aAAA;QACAC,KAAA;QAAA;QACAC,OAAA;QAAA;QACAC,YAAA;QAAA;QACAC,SAAA;MACA;MACAC,mBAAA;MAAA;MACAC,iBAAA;QAAA;QACAL,KAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAG,gBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAC,iBAAA;;IAEA;IACA;MAAArB;IAAA,SAAAsB,MAAA,CAAAC,KAAA;IACA,IAAAvB,WAAA;MACA,KAAAX,UAAA,CAAAW,WAAA,GAAAA,WAAA;IACA;;IAEA;IACA,UAAAX,UAAA,CAAAE,UAAA,UAAAF,UAAA,CAAAG,YAAA;MACA,KAAAgC,0BAAA;IACA;;IAEA;IACAC,OAAA,CAAAC,GAAA,uCAAAC,IAAA,CAAAC,SAAA,MAAAvC,UAAA;EACA;EACAwC,QAAA;IACA;IACA,KAAAC,oBAAA;IAEAL,OAAA,CAAAC,GAAA,mCAAAC,IAAA,CAAAC,SAAA,MAAAvC,UAAA;EACA;EACA0C,KAAA;IACA;IACAjD,UAAA;MACAkD,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAP,IAAA,CAAAC,SAAA,CAAAK,MAAA,MAAAN,IAAA,CAAAC,SAAA,CAAAM,MAAA;UACAT,OAAA,CAAAC,GAAA,kCAAAC,IAAA,CAAAC,SAAA,CAAAK,MAAA;;UAEA;UACA,KAAAZ,iBAAA;QACA;MACA;MACAc,IAAA;MACAC,SAAA;IACA;EACA;EACAC,cAAA;IAAA,IAAAC,YAAA;IACA;IACA,KAAAC,cAAA;;IAEA;IACA;IACAd,OAAA,CAAAC,GAAA,yCAAAY,YAAA,QAAAhB,MAAA,cAAAgB,YAAA,uBAAAA,YAAA,CAAAE,IAAA;IACA,SAAAlB,MAAA,UAAAA,MAAA,CAAAkB,IAAA,CAAAC,QAAA;MACAhB,OAAA,CAAAC,GAAA;MACA,KAAAgB,iBAAA;IACA;MACAjB,OAAA,CAAAC,GAAA;MACA;MACA,KAAAiB,kBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAF,kBAAA;MACAjB,OAAA,CAAAC,GAAA;MACAmB,YAAA,CAAAC,UAAA;IACA;IAEA;IACAtB,2BAAAuB,UAAA;MACA;QACA,MAAAC,eAAA,GAAAH,YAAA,CAAAI,OAAA;QACA,IAAAD,eAAA;UACA,MAAAE,UAAA,GAAAvB,IAAA,CAAAwB,KAAA,CAAAH,eAAA;UACAvB,OAAA,CAAAC,GAAA,+BAAAC,IAAA,CAAAC,SAAA,CAAAsB,UAAA;;UAEA;UACAlE,MAAA,CAAAoE,IAAA,MAAA/D,UAAA,EAAAgE,OAAA,CAAAC,GAAA;YACA,IAAAJ,UAAA,CAAAI,GAAA,MAAAC,SAAA,IAAAL,UAAA,CAAAI,GAAA;cACA,KAAAjE,UAAA,CAAAiE,GAAA,IAAAJ,UAAA,CAAAI,GAAA;YACA;UACA;;UAEA;UACA,IAAAP,UAAA;YACA,KAAAS,KAAA;cAAA,GAAAN;YAAA;UACA;UAEA;QACA;MACA,SAAAO,KAAA;QACAhC,OAAA,CAAAgC,KAAA,iCAAAA,KAAA;MACA;MACA;IACA;IAEA;IACApC,kBAAA;MACA,SAAAvC,UAAA;QACA2C,OAAA,CAAAC,GAAA,wCAAAC,IAAA,CAAAC,SAAA,MAAA9C,UAAA;;QAEA;QACAE,MAAA,CAAAoE,IAAA,MAAA/D,UAAA,EAAAgE,OAAA,CAAAC,GAAA;UACA,SAAAxE,UAAA,CAAAwE,GAAA,MAAAC,SAAA,SAAAzE,UAAA,CAAAwE,GAAA,mBAAAxE,UAAA,CAAAwE,GAAA;YACA,KAAAjE,UAAA,CAAAiE,GAAA,SAAAxE,UAAA,CAAAwE,GAAA;UACA;QACA;QAEA7B,OAAA,CAAAC,GAAA,kCAAAC,IAAA,CAAAC,SAAA,MAAAvC,UAAA;MACA;IACA;IAEA;IACAyC,qBAAA;MACA;QACA;QACA,KAAAxB,cAAA;;QAEA;QACA,KAAAnB,SAAA,OAAAuE,SAAA,MAAAjD,aAAA,CAAAC,KAAA;;QAEA;QACA,KAAAH,iBAAA,GAAAoD,UAAA;UACA,UAAAvE,gBAAA;YACAqC,OAAA,CAAAmC,IAAA;YACA,SAAAnD,aAAA,CAAAG,YAAA;cACA,KAAAiD,sBAAA;YACA;cACA,KAAAC,QAAA,CAAAL,KAAA,mCAAAhD,aAAA,CAAAC,KAAA;YACA;UACA;QACA,QAAAD,aAAA,CAAAE,OAAA;QAEA,KAAAxB,SAAA,CAAA4E,MAAA,GAAAC,KAAA;UACAvC,OAAA,CAAAC,GAAA;UACA,KAAAtC,gBAAA;UACA,KAAA0E,QAAA,CAAAG,OAAA;UACAC,YAAA,MAAA3D,iBAAA;QACA;QAEA,KAAApB,SAAA,CAAAgF,OAAA,GAAAH,KAAA;UACAvC,OAAA,CAAAC,GAAA;UACA,KAAAtC,gBAAA;UACA;UACA,UAAAkB,cAAA,UAAAE,oBAAA,SAAAC,aAAA,CAAAG,YAAA;YACA,KAAAiD,sBAAA;UACA;QACA;QAEA,KAAA1E,SAAA,CAAAiF,OAAA,GAAAJ,KAAA;UACAvC,OAAA,CAAAgC,KAAA,qBAAAO,KAAA;UACA,KAAA5E,gBAAA;UACA,SAAAqB,aAAA,CAAAG,YAAA;YACA,KAAAiD,sBAAA;UACA;YACA,KAAAC,QAAA,CAAAL,KAAA,mCAAAhD,aAAA,CAAAC,KAAA;UACA;QACA;QAEA,KAAAvB,SAAA,CAAAkF,SAAA,GAAAL,KAAA;UACA,KAAAM,oBAAA,CAAAN,KAAA;QACA;MACA,SAAAP,KAAA;QACAhC,OAAA,CAAAgC,KAAA,sBAAAA,KAAA;QACA,SAAAhD,aAAA,CAAAG,YAAA;UACA,KAAAiD,sBAAA;QACA;UACA,KAAAC,QAAA,CAAAL,KAAA,cAAAA,KAAA,CAAArD,OAAA;QACA;MACA;IACA;IAEA;IACAkE,qBAAAN,KAAA;MACA,MAAAO,UAAA;MAEA,IAAAP,KAAA,CAAA9E,IAAA,CAAAsF,OAAA;QACA;QACA,MAAAC,OAAA,GAAAT,KAAA,CAAA9E,IAAA,CAAAwF,OAAA,6BAAAA,OAAA;QACA,KAAAZ,QAAA,CAAAG,OAAA,aAAAQ,OAAA;;QAEA;QACA,SAAAE,cAAA,CAAAF,OAAA;UACA,KAAApF,UAAA,CAAAG,YAAA,GAAAiF,OAAA;UACA,KAAAG,oBAAA,CAAAH,OAAA;QACA;MACA,WAAAT,KAAA,CAAA9E,IAAA,CAAAsF,OAAA;QACA;QACA,MAAAK,MAAA,GAAAb,KAAA,CAAA9E,IAAA,CAAAwF,OAAA,sBAAAA,OAAA;QACA,KAAAZ,QAAA,CAAAG,OAAA,cAAAY,MAAA;MACA,WAAAb,KAAA,CAAA9E,IAAA,CAAAsF,OAAA;QACA;QACA,MAAAM,UAAA,GAAAd,KAAA,CAAA9E,IAAA,CAAAwF,OAAA,0BAAAA,OAAA;QACAjD,OAAA,CAAAC,GAAA,2BAAAoD,UAAA,CAAAC,MAAA;;QAEA;QACA,KAAA5D,gBAAA;QAEA,IAAA2D,UAAA,IAAAA,UAAA,CAAAC,MAAA;UAAA;UACA;UACA,KAAA7D,gBAAA,GAAAqD,UAAA,GAAAO,UAAA;;UAEA;UACArD,OAAA,CAAAC,GAAA;UACA,KAAAsD,mBAAA,MAAA9D,gBAAA;QACA;UACAO,OAAA,CAAAgC,KAAA;;UAEA;UACA,SAAAwB,KAAA,CAAAC,cAAA,SAAAD,KAAA,CAAAC,cAAA,CAAAC,GAAA,IACA,KAAAF,KAAA,CAAAC,cAAA,CAAAC,GAAA,CAAAC,UAAA;YACA3D,OAAA,CAAAC,GAAA;YACA,KAAAsD,mBAAA,MAAAC,KAAA,CAAAC,cAAA,CAAAC,GAAA;UACA;YACA1D,OAAA,CAAAC,GAAA,0BAAAV,gBAAA;YACA,KAAAgE,mBAAA,MAAAhE,gBAAA;UACA;QACA;MACA,WAAAgD,KAAA,CAAA9E,IAAA,CAAAsF,OAAA;QACA;QACA,MAAAK,MAAA,GAAAb,KAAA,CAAA9E,IAAA,CAAAwF,OAAA,yBAAAA,OAAA;QACAjD,OAAA,CAAAC,GAAA,YAAAmD,MAAA;QACA;QACA,IAAAA,MAAA;UACApD,OAAA,CAAAC,GAAA;QACA;MACA,WAAAsC,KAAA,CAAA9E,IAAA,CAAAsF,OAAA;QACA;QACA,MAAAK,MAAA,GAAAb,KAAA,CAAA9E,IAAA,CAAAwF,OAAA,wBAAAA,OAAA;QACAjD,OAAA,CAAAC,GAAA,YAAAmD,MAAA;QACA;QACA,IAAAA,MAAA;UACApD,OAAA,CAAAC,GAAA;QACA;UACAD,OAAA,CAAAmC,IAAA;QACA;MACA,WAAAI,KAAA,CAAA9E,IAAA,CAAAsF,OAAA,8BAAAR,KAAA,CAAA9E,IAAA,CAAAsF,OAAA;QACA;QACA/C,OAAA,CAAAC,GAAA,gBAAAsC,KAAA,CAAA9E,IAAA;MACA,WAAA8E,KAAA,CAAA9E,IAAA,CAAAkG,UAAA,YAAApB,KAAA,CAAA9E,IAAA,CAAA6F,MAAA,WAAAf,KAAA,CAAA9E,IAAA,CAAAuD,QAAA;QACA;QACA;QACA,SAAAwC,KAAA,CAAAC,cAAA;UACA;YACA;YACA,MAAAG,QAAA,GAAArB,KAAA,CAAA9E,IAAA,CAAAoG,SAAA;YACAC,MAAA,CAAAC,IAAA,CAAAH,QAAA;;YAEA;YACA,MAAAI,OAAA,GAAAlB,UAAA,GAAAP,KAAA,CAAA9E,IAAA;YACA,KAAA+F,KAAA,CAAAC,cAAA,CAAAC,GAAA,GAAAM,OAAA;;YAEA;YACA,KAAAvE,gBAAA,GAAAuE,OAAA;YACAhE,OAAA,CAAAC,GAAA,iBAAAsC,KAAA,CAAA9E,IAAA,CAAA6F,MAAA;UACA,SAAAW,CAAA;YACAjE,OAAA,CAAAgC,KAAA,wBAAAiC,CAAA;UACA;QACA;MACA;QACA;QACAjE,OAAA,CAAAC,GAAA,aAAAsC,KAAA,CAAA9E,IAAA;MACA;IACA;IAEA;IACAyG,aAAA;MACA,UAAAvG,gBAAA;QACA,KAAA0C,oBAAA;QACA;MACA;MAEA;QACA;QACA,KAAA8D,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;QACA,KAAA9B,QAAA,CAAAG,OAAA;MACA,SAAAR,KAAA;QACAhC,OAAA,CAAAgC,KAAA,YAAAA,KAAA;QACA,KAAAK,QAAA,CAAAL,KAAA;MACA;IACA;IAEA;IACAoC,YAAA;MACA,UAAAzG,gBAAA;QACA;MACA;MAEA;QACA,KAAAwG,kBAAA;QACA,KAAA9B,QAAA,CAAAG,OAAA;MACA,SAAAR,KAAA;QACAhC,OAAA,CAAAgC,KAAA,YAAAA,KAAA;QACA,KAAAK,QAAA,CAAAL,KAAA;MACA;IACA;IAEA;IACAqC,WAAA;MACA,UAAA1G,gBAAA,UAAAkB,cAAA;QACA,KAAAwD,QAAA,CAAAiC,OAAA;QACA;MACA;MAEA;QACA,SAAAzF,cAAA;UACA;UACA,KAAA0F,6BAAA;UACA;QACA;;QAEA;QACA,KAAAJ,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACAjC,UAAA;UACA;UACA,KAAAiC,kBAAA;;UAEA;UACA,MAAAK,SAAA,OAAAC,IAAA,GAAAC,OAAA;UACA,MAAAC,QAAA,aAAAH,SAAA;UACA,KAAAjF,gBAAA,eAAAoF,QAAA;UACA3E,OAAA,CAAAC,GAAA,iBAAAV,gBAAA;;UAEA;UACA,KAAA4E,kBAAA,uBAAAQ,QAAA;;UAEA;UACA,KAAAjF,gBAAA;;UAEA;UACAwC,UAAA;YACA;YACA,KAAAiC,kBAAA,sBAAA5E,gBAAA;;YAEA;YACA,KAAA4E,kBAAA,8BAAA5E,gBAAA;;YAEA;YACA,KAAAE,gBAAA;;YAEA;YACAyC,UAAA;cACA,SAAAxC,gBAAA;gBACAM,OAAA,CAAAC,GAAA;gBACA,KAAAP,gBAAA;;gBAEA;gBACA,SAAA8D,KAAA,CAAAC,cAAA,SAAAD,KAAA,CAAAC,cAAA,CAAAC,GAAA,IACA,KAAAF,KAAA,CAAAC,cAAA,CAAAC,GAAA,CAAAC,UAAA;kBACA3D,OAAA,CAAAC,GAAA;kBACA,KAAAsD,mBAAA,MAAAC,KAAA,CAAAC,cAAA,CAAAC,GAAA;gBACA;kBACA;kBACA1D,OAAA,CAAAC,GAAA,0BAAAV,gBAAA;kBACA,KAAAgE,mBAAA,MAAAhE,gBAAA;gBACA;cACA;YACA;UAEA;QACA;;QAEA,KAAA8C,QAAA,CAAAuC,IAAA;MACA,SAAA5C,KAAA;QACAhC,OAAA,CAAAgC,KAAA,YAAAA,KAAA;QACA,KAAAK,QAAA,CAAAL,KAAA;MACA;IACA;IAEA;IACAuB,oBAAAsB,SAAA;MACA,SAAArF,eAAA;QACA;MACA;MAEA,KAAAA,eAAA;MACA,KAAA6C,QAAA,CAAAuC,IAAA;;MAEA;MACA,IAAAC,SAAA,CAAAlB,UAAA;QACA;QACA,KAAAmB,gBAAA,CAAAD,SAAA,EAAAE,IAAA,CAAAC,eAAA;UACAhF,OAAA,CAAAC,GAAA;;UAEA;UACA,MAAAoD,UAAA,GAAA2B,eAAA,CAAAC,KAAA;;UAEA;UACA,MAAAC,MAAA;YACAC,OAAA;cACA;YACA;UACA;;UAEA;UACA,MAAAC,QAAA;YACAC,KAAA,EAAAhC;UACA;;UAEA;UACAnG,KAAA,CAAAoI,IAAA,MAAAtG,aAAA,CAAAI,SAAA,EAAAgG,QAAA,EAAAF,MAAA,EACAH,IAAA,CAAAQ,QAAA;YACAvF,OAAA,CAAAC,GAAA,gBAAAsF,QAAA;YACA,KAAAC,eAAA,CAAAD,QAAA,CAAA9H,IAAA;UACA,GACAgI,KAAA,CAAAzD,KAAA;YAAA,IAAA0D,eAAA;YACA1F,OAAA,CAAAgC,KAAA,YAAAA,KAAA;YACA,KAAAK,QAAA,CAAAL,KAAA,kBAAA0D,eAAA,GAAA1D,KAAA,CAAAuD,QAAA,cAAAG,eAAA,gBAAAA,eAAA,GAAAA,eAAA,CAAAjI,IAAA,cAAAiI,eAAA,uBAAAA,eAAA,CAAA/G,OAAA,KAAAqD,KAAA,CAAArD,OAAA;;YAEA;YACAqB,OAAA,CAAAC,GAAA;;YAEA;YACA,MAAA0F,QAAA,OAAAC,QAAA;YACA,MAAAC,cAAA,GAAA9B,IAAA,CAAAV,UAAA;YACA,MAAAyC,UAAA;YAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,cAAA,CAAAvC,MAAA,EAAAyC,CAAA;cACAD,UAAA,CAAAE,IAAA,CAAAH,cAAA,CAAAI,UAAA,CAAAF,CAAA;YACA;YAEA,MAAAG,SAAA,OAAAC,UAAA,CAAAL,UAAA;YACA,MAAAM,IAAA,OAAAC,IAAA,EAAAH,SAAA;cAAA5I,IAAA;YAAA;;YAEA;YACA,MAAAgJ,QAAA,iBAAA7B,IAAA,GAAAC,OAAA;YACA,MAAA6B,IAAA,OAAAC,IAAA,EAAAJ,IAAA,GAAAE,QAAA;cAAAhJ,IAAA;YAAA;;YAEA;YACAqI,QAAA,CAAAc,MAAA,UAAAF,IAAA;YACA,KAAAG,UAAA,CAAAf,QAAA;UACA,GACAgB,OAAA;YACA,KAAAnH,eAAA;UACA;QACA,GAAAiG,KAAA,CAAAzD,KAAA;UACAhC,OAAA,CAAAgC,KAAA,cAAAA,KAAA;;UAEA;UACA,KAAA4E,iBAAA,CAAA/B,SAAA;QACA;MACA;QACA;QACA,MAAAc,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAc,MAAA,eAAA5B,SAAA;QACA7E,OAAA,CAAAC,GAAA,mBAAA4E,SAAA;;QAEA;QACA,KAAA6B,UAAA,CAAAf,QAAA;MACA;IACA;IAEA;IACAb,iBAAA+B,YAAA;MACA,WAAAC,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA;UACA,MAAAC,GAAA,OAAAC,KAAA;UACAD,GAAA,CAAAvD,GAAA,GAAAmD,YAAA;UAEAI,GAAA,CAAAE,MAAA;YACA;YACA,IAAAF,GAAA,CAAAG,KAAA,YAAAH,GAAA,CAAAI,MAAA;cACArH,OAAA,CAAAC,GAAA;cACA8G,OAAA,CAAAF,YAAA;cACA;YACA;;YAEA;YACA;YACA,MAAAS,QAAA;YACA,MAAAC,SAAA;;YAEA;YACA,IAAAH,KAAA,GAAAH,GAAA,CAAAG,KAAA;YACA,IAAAC,MAAA,GAAAJ,GAAA,CAAAI,MAAA;YAEA,IAAAD,KAAA,GAAAE,QAAA;cACAD,MAAA,GAAAA,MAAA,GAAAC,QAAA,GAAAF,KAAA;cACAA,KAAA,GAAAE,QAAA;YACA;YAEA,IAAAD,MAAA,GAAAE,SAAA;cACAH,KAAA,GAAAA,KAAA,GAAAG,SAAA,GAAAF,MAAA;cACAA,MAAA,GAAAE,SAAA;YACA;;YAEA;YACA,MAAAC,MAAA,GAAAC,QAAA,CAAAC,aAAA;YACAF,MAAA,CAAAJ,KAAA,GAAAA,KAAA;YACAI,MAAA,CAAAH,MAAA,GAAAA,MAAA;YACA,MAAAM,GAAA,GAAAH,MAAA,CAAAI,UAAA;;YAEA;YACAD,GAAA,CAAAE,SAAA,CAAAZ,GAAA,QAAAG,KAAA,EAAAC,MAAA;;YAEA;YACA,MAAArC,eAAA,GAAAwC,MAAA,CAAAM,SAAA;;YAEA;YACA9H,OAAA,CAAAC,GAAA,gBAAAgH,GAAA,CAAAG,KAAA,IAAAH,GAAA,CAAAI,MAAA,UAAAD,KAAA,IAAAC,MAAA;YAEAN,OAAA,CAAA/B,eAAA;UACA;UAEAiC,GAAA,CAAAtE,OAAA,GAAAX,KAAA;YACAhC,OAAA,CAAAgC,KAAA,YAAAA,KAAA;YACAgF,MAAA,CAAAhF,KAAA;UACA;QACA,SAAAiC,CAAA;UACAjE,OAAA,CAAAgC,KAAA,cAAAiC,CAAA;UACA+C,MAAA,CAAA/C,CAAA;QACA;MACA;IACA;IAEA;IACA2C,kBAAA/B,SAAA;MACA;QACA7E,OAAA,CAAAC,GAAA;QAEA,IAAA4E,SAAA,CAAAlB,UAAA;UACA;UACA,MAAAN,UAAA,GAAAwB,SAAA,CAAAI,KAAA;;UAEA;UACA,MAAAC,MAAA;YACAC,OAAA;cACA;YACA;UACA;;UAEA;UACA,MAAAC,QAAA;YACAC,KAAA,EAAAhC;UACA;;UAEA;UACAnG,KAAA,CAAAoI,IAAA,MAAAtG,aAAA,CAAAI,SAAA,EAAAgG,QAAA,EAAAF,MAAA,EACAH,IAAA,CAAAQ,QAAA;YACAvF,OAAA,CAAAC,GAAA,gBAAAsF,QAAA;YACA,KAAAC,eAAA,CAAAD,QAAA,CAAA9H,IAAA;UACA,GACAgI,KAAA,CAAAzD,KAAA;YAAA,IAAA+F,gBAAA;YACA/H,OAAA,CAAAgC,KAAA,YAAAA,KAAA;YACA,KAAAK,QAAA,CAAAL,KAAA,kBAAA+F,gBAAA,GAAA/F,KAAA,CAAAuD,QAAA,cAAAwC,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAtK,IAAA,cAAAsK,gBAAA,uBAAAA,gBAAA,CAAApJ,OAAA,KAAAqD,KAAA,CAAArD,OAAA;UACA,GACAgI,OAAA;YACA,KAAAnH,eAAA;UACA;QACA;UACA;UACA,MAAAmG,QAAA,OAAAC,QAAA;UACAD,QAAA,CAAAc,MAAA,eAAA5B,SAAA;UACA,KAAA6B,UAAA,CAAAf,QAAA;QACA;MACA,SAAA1B,CAAA;QACAjE,OAAA,CAAAgC,KAAA,cAAAiC,CAAA;QACA,KAAAzE,eAAA;QACA,KAAA6C,QAAA,CAAAL,KAAA,cAAAiC,CAAA,CAAAtF,OAAA;MACA;IACA;IAEA;IACA4F,8BAAA;MACA,KAAA/E,eAAA;MACA,KAAA6C,QAAA,CAAAuC,IAAA;;MAEA;MACA,MAAAe,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAc,MAAA;;MAEA;MACAvJ,KAAA,CAAAoI,IAAA,MAAAtG,aAAA,CAAAI,SAAA,EAAAuG,QAAA,EACAZ,IAAA,CAAAQ,QAAA;QACA,KAAAC,eAAA,CAAAD,QAAA,CAAA9H,IAAA;MACA,GACAgI,KAAA,CAAAzD,KAAA;QAAA,IAAAgG,gBAAA;QACAhI,OAAA,CAAAgC,KAAA,cAAAA,KAAA;QACA,KAAAK,QAAA,CAAAL,KAAA,oBAAAgG,gBAAA,GAAAhG,KAAA,CAAAuD,QAAA,cAAAyC,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAvK,IAAA,cAAAuK,gBAAA,uBAAAA,gBAAA,CAAArJ,OAAA,KAAAqD,KAAA,CAAArD,OAAA;MACA,GACAgI,OAAA;QACA,KAAAnH,eAAA;MACA;IACA;IAEA;IACAgG,gBAAApC,MAAA;MACA,KAAAA,MAAA,KAAAA,MAAA,CAAAZ,OAAA;QACA,KAAAH,QAAA,CAAAL,KAAA,iBAAAoB,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAzE,OAAA;QACA;MACA;MAEA,KAAA0D,QAAA,CAAAG,OAAA;MACAxC,OAAA,CAAAC,GAAA,aAAAmD,MAAA;;MAEA;MACA,MAAA6E,OAAA,GAAA7E,MAAA,CAAA3F,IAAA;;MAEA;MACA,MAAAyK,UAAA,GAAAD,OAAA,CAAAE,OAAA;;MAEA;MACA,IAAAF,OAAA,CAAAG,SAAA;QACA,KAAAxK,UAAA,CAAAG,YAAA,GAAAkK,OAAA,CAAAG,SAAA;QACA;QACA,KAAAC,OAAA;UACAC,KAAA;UACAhL,IAAA;UACAiL,QAAA;QACA;MACA;;MAEA;MACA,IAAAN,OAAA,CAAA9K,IAAA;QACA,KAAAS,UAAA,CAAAE,UAAA,GAAAmK,OAAA,CAAA9K,IAAA;MACA;;MAEA;MACA,IAAA8K,OAAA,CAAAjK,MAAA;QACA,KAAAJ,UAAA,CAAAI,MAAA,GAAAiK,OAAA,CAAAjK,MAAA;MACA;;MAEA;MACA,IAAAiK,OAAA,CAAAE,OAAA;QACA,KAAAvK,UAAA,CAAAK,WAAA,GAAAgK,OAAA,CAAAE,OAAA;QACA;QACA,KAAAE,OAAA;UACAC,KAAA;UACA3J,OAAA,YAAAsJ,OAAA,CAAAE,OAAA;UACA7K,IAAA;UACAiL,QAAA;QACA;MACA;;MAEA;MACA,IAAAN,OAAA,CAAAG,SAAA;QACA;QACA,KAAAjF,oBAAA,CAAA8E,OAAA,CAAAG,SAAA,EAAAF,UAAA;MACA;IACA;IAEA;IACA/D,mBAAAqE,OAAA;MACA,SAAA9K,SAAA,SAAAA,SAAA,CAAA+K,UAAA,KAAAxG,SAAA,CAAAyG,IAAA;QACA,KAAAhL,SAAA,CAAAiL,IAAA,CAAAH,OAAA;MACA;QACA,UAAAI,KAAA;MACA;IACA;IAEA;IACA9H,eAAA;MACA,SAAApD,SAAA;QACA;QACA,SAAAC,gBAAA;UACA;YACA,KAAAD,SAAA,CAAAiL,IAAA;UACA,SAAA1E,CAAA;YACAjE,OAAA,CAAAgC,KAAA,YAAAiC,CAAA;UACA;QACA;;QAEA;QACA,KAAAvG,SAAA,CAAAmL,KAAA;QACA,KAAAnL,SAAA;QACA,KAAAC,gBAAA;MACA;IACA;IAEA;IACAuF,eAAA4F,GAAA;MACA;MACA,MAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;IAEA;IACA3F,qBAAApF,YAAA,EAAAkL,cAAA;MACA,KAAAlL,YAAA;QACA,KAAAsE,QAAA,CAAAiC,OAAA;QACA;MACA;MAEA,KAAAjC,QAAA,CAAAuC,IAAA;;MAEA;MACA3H,iBAAA,CAAAc,YAAA,EACAgH,IAAA,CAAAQ,QAAA;QACA,IAAAA,QAAA,CAAA2D,IAAA,UAAA3D,QAAA,CAAA9H,IAAA;UACA,MAAA0L,UAAA,GAAA5D,QAAA,CAAA9H,IAAA;;UAEA;UACA,KAAAG,UAAA,CAAAC,QAAA,GAAAsL,UAAA,CAAAC,EAAA;UACA,KAAAxL,UAAA,CAAAE,UAAA,GAAAqL,UAAA,CAAArL,UAAA;UACA,KAAAF,UAAA,CAAAI,MAAA,GAAAmL,UAAA,CAAAnL,MAAA;UACA,KAAAJ,UAAA,CAAAK,WAAA,GAAAkL,UAAA,CAAAlL,WAAA;UACA,KAAAL,UAAA,CAAAM,QAAA,GAAAiL,UAAA,CAAAE,WAAA;UACA,KAAAzL,UAAA,CAAAO,QAAA,GAAAgL,UAAA,CAAAG,SAAA;UACA,KAAA1L,UAAA,CAAAa,WAAA,GAAA0K,UAAA,CAAAI,UAAA;UACA,KAAA3L,UAAA,CAAAQ,eAAA,GAAA+K,UAAA,CAAAK,wBAAA,IAAAL,UAAA,CAAAM,QAAA;UACA,KAAA7L,UAAA,CAAAS,eAAA,GAAA8K,UAAA,CAAAO,QAAA;UACA,KAAA9L,UAAA,CAAAU,sBAAA,GAAA6K,UAAA,CAAA7K,sBAAA,IAAA6K,UAAA,CAAAQ,MAAA;UACA,KAAA/L,UAAA,CAAAW,WAAA,GAAA4K,UAAA,CAAA5K,WAAA,SAAAX,UAAA,CAAAW,WAAA;UACA,KAAAX,UAAA,CAAAY,WAAA,GAAA2K,UAAA,CAAA3K,WAAA;;UAEA;UACA,MAAA2K,UAAA,CAAAlL,WAAA,IAAAkL,UAAA,CAAAlL,WAAA,YAAAgL,cAAA;YACAjJ,OAAA,CAAAC,GAAA,gBAAAgJ,cAAA;YACA,KAAArL,UAAA,CAAAK,WAAA,GAAAgL,cAAA;UACA;;UAEA;UACA,KAAA/H,kBAAA;UAEA,KAAAmB,QAAA,CAAAG,OAAA;;UAEA;UACA,KAAA6F,OAAA;YACAC,KAAA;YACA3J,OAAA,YAAAwK,UAAA,CAAArL,UAAA,UAAAqL,UAAA,CAAAE,WAAA;YACA/L,IAAA;YACAiL,QAAA;UACA;QACA;UACA,KAAAlG,QAAA,CAAAiC,OAAA,CAAAiB,QAAA,CAAAqE,GAAA;;UAEA;UACA,SAAAhM,UAAA,CAAAG,YAAA,UAAAH,UAAA,CAAAE,UAAA;YACA;YACA,KAAA+L,qBAAA,CAAA9L,YAAA;UACA;;UAEA;UACA,IAAAkL,cAAA;YACAjJ,OAAA,CAAAC,GAAA,wBAAAgJ,cAAA;YACA,KAAArL,UAAA,CAAAK,WAAA,GAAAgL,cAAA;UACA;QACA;MACA,GACAxD,KAAA,CAAAzD,KAAA;QACAhC,OAAA,CAAAgC,KAAA,cAAAA,KAAA;QACA,KAAAK,QAAA,CAAAL,KAAA,iBAAAA,KAAA,CAAArD,OAAA;;QAEA;QACA,SAAAf,UAAA,CAAAG,YAAA;UACA,KAAA8L,qBAAA,CAAA9L,YAAA;QACA;;QAEA;QACA,IAAAkL,cAAA;UACAjJ,OAAA,CAAAC,GAAA,wBAAAgJ,cAAA;UACA,KAAArL,UAAA,CAAAK,WAAA,GAAAgL,cAAA;QACA;MACA;IACA;IAEA;IACAY,sBAAA9L,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAAuF,MAAA;MAEA;QACA;QACA,MAAAwG,UAAA,GAAAC,QAAA,CAAAhM,YAAA,CAAAiM,MAAA;QACA,KAAApM,UAAA,CAAAI,MAAA,GAAA8L,UAAA;;QAEA;QACA9J,OAAA,CAAAC,GAAA,qBAAArC,UAAA,CAAAI,MAAA;MACA,SAAAiG,CAAA;QACAjE,OAAA,CAAAgC,KAAA,iBAAAiC,CAAA;MACA;IACA;IAEA;IACAgG,aAAA;MACA,UAAArM,UAAA,CAAAG,YAAA;QACA,KAAAsE,QAAA,CAAAiC,OAAA;QACA;MACA;;MAEA;MACA,KAAAnB,oBAAA,MAAAvF,UAAA,CAAAG,YAAA;IACA;IAEA;IACAmM,kBAAA;MACA,SAAAtM,UAAA,CAAAG,YAAA;QACA,KAAAsE,QAAA,CAAAuC,IAAA;QACA,KAAAzB,oBAAA,MAAAvF,UAAA,CAAAG,YAAA;MACA;QACA,KAAAsE,QAAA,CAAAiC,OAAA;MACA;IACA;IAEA;IACApD,mBAAA;MACA;QACA;QACA,IAAAiJ,YAAA;QACA;UACA,MAAAC,SAAA,GAAAhJ,YAAA,CAAAI,OAAA;UACA,IAAA4I,SAAA;YACAD,YAAA,GAAAjK,IAAA,CAAAwB,KAAA,CAAA0I,SAAA;UACA;QACA,SAAAnG,CAAA;QAEA,MAAAoG,UAAA;UACA,GAAAF,YAAA;UACA,QAAAvM;QACA;QAEAoC,OAAA,CAAAC,GAAA,6BAAAC,IAAA,CAAAC,SAAA,CAAAkK,UAAA;QACAjJ,YAAA,CAAAkJ,OAAA,uBAAApK,IAAA,CAAAC,SAAA,CAAAkK,UAAA;QACA,OAAAA,UAAA;MACA,SAAArI,KAAA;QACAhC,OAAA,CAAAgC,KAAA,eAAAA,KAAA;QACA,YAAApE,UAAA;MACA;IACA;IAEA;IACA2M,SAAA;MACA,KAAA/G,KAAA,CAAA5F,UAAA,CAAA4M,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,SAAA9M,gBAAA;YACA,KAAAyG,WAAA;YACA,KAAAtD,cAAA;YACAd,OAAA,CAAAC,GAAA;UACA;;UAEA;UACAD,OAAA,CAAAC,GAAA;UACAD,OAAA,CAAAC,GAAA,0BAAAC,IAAA,CAAAC,SAAA,MAAAvC,UAAA;;UAEA;UACA,MAAA8M,YAAA,QAAAxJ,kBAAA;UACAlB,OAAA,CAAAC,GAAA,wCAAAC,IAAA,CAAAC,SAAA,CAAAuK,YAAA;;UAEA;UACA,MAAAC,YAAA;YACA,GAAAD,YAAA;YACA;YACA5M,UAAA,OAAAF,UAAA,CAAAE,UAAA;YACAC,YAAA,OAAAH,UAAA,CAAAG,YAAA;YACAC,MAAA,OAAAJ,UAAA,CAAAI,MAAA;YACAC,WAAA,OAAAL,UAAA,CAAAK,WAAA;YACAC,QAAA,OAAAN,UAAA,CAAAM,QAAA;YACAO,WAAA,OAAAb,UAAA,CAAAa,WAAA;YACAL,eAAA,OAAAR,UAAA,CAAAQ;UACA;UAEA4B,OAAA,CAAAC,GAAA,+BAAAC,IAAA,CAAAC,SAAA,CAAAwK,YAAA;UACA3K,OAAA,CAAAC,GAAA;UACAD,OAAA,CAAAC,GAAA,kBAAA0K,YAAA,CAAA7M,UAAA;UACAkC,OAAA,CAAAC,GAAA,oBAAA0K,YAAA,CAAA5M,YAAA;;UAEA;UACA,KAAAgE,KAAA,uBAAA4I,YAAA;UACA3K,OAAA,CAAAC,GAAA;;UAEA;UACA,KAAA2K,SAAA;YACA;YACA,KAAA7I,KAAA;UACA;QACA;UACA,KAAAM,QAAA,CAAAiC,OAAA;QACA;MACA;IACA;IAEA;IACAlC,uBAAA;MACA,KAAAvD,cAAA;MACA,KAAAlB,gBAAA;MAEA,SAAAD,SAAA;QACA,KAAAqB,oBAAA;QACA,KAAArB,SAAA,CAAAmL,KAAA;QACA,KAAAnL,SAAA;MACA;MAEA,KAAA2E,QAAA,CAAAiC,OAAA;;MAEA;MACA,SAAAxF,iBAAA;QACA2D,YAAA,MAAA3D,iBAAA;MACA;IACA;IAEA;IACA+L,kBAAAtE,IAAA;MACA,IAAAA,IAAA;QACA;QACA,MAAAuE,OAAA,GAAAvE,IAAA,CAAAjJ,IAAA,CAAAyF,OAAA;QACA,KAAA+H,OAAA;UACA,KAAAzI,QAAA,CAAAL,KAAA;UACA;QACA;;QAEA;QACA,MAAA+I,OAAA,GAAAxE,IAAA,CAAAyE,IAAA;QACA,KAAAD,OAAA;UACA,KAAA1I,QAAA,CAAAL,KAAA;UACA;QACA;QAEA,KAAAK,QAAA,CAAAuC,IAAA;;QAEA;QACA,MAAAqG,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,aAAA,CAAA5E,IAAA;QACA0E,MAAA,CAAA9D,MAAA,GAAAlD,CAAA;UACA;UACA,KAAAmH,aAAA,CAAAnH,CAAA,CAAAoH,MAAA,CAAAjI,MAAA,EAAAkI,iBAAA;YACA;YACA,SAAA9H,KAAA,CAAAC,cAAA;cACA,KAAAD,KAAA,CAAAC,cAAA,CAAAC,GAAA,GAAA4H,iBAAA;YACA;;YAEA;YACA,KAAA/L,gBAAA,GAAA+L,iBAAA;YACA,KAAAjJ,QAAA,CAAAuC,IAAA;;YAEA;YACA1C,UAAA;cACA,KAAAqB,mBAAA,MAAAhE,gBAAA;YACA;UACA;QACA;QAEA0L,MAAA,CAAAtI,OAAA,GAAAX,KAAA;UACAhC,OAAA,CAAAgC,KAAA,aAAAA,KAAA;UACA,KAAAK,QAAA,CAAAL,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAoJ,cAAAG,OAAA,EAAAC,QAAA,EAAAlE,QAAA,SAAAC,SAAA,SAAAkE,OAAA;MACA,MAAAxE,GAAA,OAAAC,KAAA;MACAD,GAAA,CAAAvD,GAAA,GAAA6H,OAAA;MAEAtE,GAAA,CAAAE,MAAA;QACA;QACA,MAAAK,MAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAN,KAAA,GAAAH,GAAA,CAAAG,KAAA;QACA,IAAAC,MAAA,GAAAJ,GAAA,CAAAI,MAAA;;QAEA;QACA,IAAAD,KAAA,GAAAC,MAAA;UACA,IAAAD,KAAA,GAAAE,QAAA;YACAD,MAAA,IAAAC,QAAA,GAAAF,KAAA;YACAA,KAAA,GAAAE,QAAA;UACA;QACA;UACA,IAAAD,MAAA,GAAAE,SAAA;YACAH,KAAA,IAAAG,SAAA,GAAAF,MAAA;YACAA,MAAA,GAAAE,SAAA;UACA;QACA;;QAEA;QACAC,MAAA,CAAAJ,KAAA,GAAAA,KAAA;QACAI,MAAA,CAAAH,MAAA,GAAAA,MAAA;;QAEA;QACA,MAAAM,GAAA,GAAAH,MAAA,CAAAI,UAAA;QACAD,GAAA,CAAAE,SAAA,CAAAZ,GAAA,QAAAG,KAAA,EAAAC,MAAA;;QAEA;QACA,MAAAiE,iBAAA,GAAA9D,MAAA,CAAAM,SAAA,eAAA2D,OAAA;;QAEA;QACA,MAAAC,YAAA,GAAAH,OAAA,CAAAjI,MAAA;QACA,MAAAqI,cAAA,GAAAL,iBAAA,CAAAhI,MAAA;QACA,MAAAsI,gBAAA,KAAAF,YAAA,GAAAC,cAAA,IAAAD,YAAA,QAAAG,OAAA;QAEA7L,OAAA,CAAAC,GAAA,iBAAAyL,YAAA,gBAAAG,OAAA,iBAAAF,cAAA,gBAAAE,OAAA,cAAAD,gBAAA;QAEAJ,QAAA,CAAAF,iBAAA;MACA;MAEArE,GAAA,CAAAtE,OAAA;QACA3C,OAAA,CAAAgC,KAAA;QACAwJ,QAAA,CAAAD,OAAA;MACA;IACA;IAEA;IACAO,aAAAjF,YAAA;MACA,WAAAC,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA;UACA,MAAAC,GAAA,OAAAC,KAAA;UACAD,GAAA,CAAAvD,GAAA,GAAAmD,YAAA;UAEAI,GAAA,CAAAE,MAAA;YACA;YACA,MAAAK,MAAA,GAAAC,QAAA,CAAAC,aAAA;YACAF,MAAA,CAAAJ,KAAA,GAAAH,GAAA,CAAAG,KAAA;YACAI,MAAA,CAAAH,MAAA,GAAAJ,GAAA,CAAAI,MAAA;YACA,MAAAM,GAAA,GAAAH,MAAA,CAAAI,UAAA;;YAEA;YACAD,GAAA,CAAAE,SAAA,CAAAZ,GAAA,QAAAA,GAAA,CAAAG,KAAA,EAAAH,GAAA,CAAAI,MAAA;;YAEA;YACA,KAAA0E,UAAA,CAAAvE,MAAA,EAAAG,GAAA;;YAEA;YACA,KAAAqE,wBAAA,CAAAxE,MAAA,EAAAG,GAAA;;YAEA;YACA,KAAAsE,YAAA,CAAAzE,MAAA,EAAAG,GAAA;;YAEA;YACAZ,OAAA,CAAAS,MAAA,CAAAM,SAAA;UACA;UAEAb,GAAA,CAAAtE,OAAA,GAAAX,KAAA;YACAhC,OAAA,CAAAgC,KAAA,YAAAA,KAAA;YACAgF,MAAA,CAAAhF,KAAA;UACA;QACA,SAAAiC,CAAA;UACAjE,OAAA,CAAAgC,KAAA,cAAAiC,CAAA;UACA+C,MAAA,CAAA/C,CAAA;QACA;MACA;IACA;IAEA;IACA8H,WAAAvE,MAAA,EAAAG,GAAA;MACA;QACA,MAAAuE,SAAA,GAAAvE,GAAA,CAAAwE,YAAA,OAAA3E,MAAA,CAAAJ,KAAA,EAAAI,MAAA,CAAAH,MAAA;QACA,MAAA5J,IAAA,GAAAyO,SAAA,CAAAzO,IAAA;;QAEA;QACA,IAAA2O,IAAA,GAAA5E,MAAA,CAAAJ,KAAA;UAAAiF,IAAA,GAAA7E,MAAA,CAAAH,MAAA;UAAAiF,IAAA;UAAAC,IAAA;QACA,MAAAC,SAAA;;QAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAjF,MAAA,CAAAH,MAAA,EAAAoF,CAAA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAlF,MAAA,CAAAJ,KAAA,EAAAsF,CAAA;YACA,MAAAC,GAAA,IAAAF,CAAA,GAAAjF,MAAA,CAAAJ,KAAA,GAAAsF,CAAA;YACA,MAAAE,UAAA,IAAAnP,IAAA,CAAAkP,GAAA,IAAAlP,IAAA,CAAAkP,GAAA,QAAAlP,IAAA,CAAAkP,GAAA;YAEA,IAAAC,UAAA,GAAAJ,SAAA;cACAJ,IAAA,GAAAS,IAAA,CAAAC,GAAA,CAAAV,IAAA,EAAAM,CAAA;cACAL,IAAA,GAAAQ,IAAA,CAAAC,GAAA,CAAAT,IAAA,EAAAI,CAAA;cACAH,IAAA,GAAAO,IAAA,CAAAE,GAAA,CAAAT,IAAA,EAAAI,CAAA;cACAH,IAAA,GAAAM,IAAA,CAAAE,GAAA,CAAAR,IAAA,EAAAE,CAAA;YACA;UACA;QACA;;QAEA;QACA,MAAAO,OAAA;QACAZ,IAAA,GAAAS,IAAA,CAAAE,GAAA,IAAAX,IAAA,GAAAY,OAAA;QACAX,IAAA,GAAAQ,IAAA,CAAAE,GAAA,IAAAV,IAAA,GAAAW,OAAA;QACAV,IAAA,GAAAO,IAAA,CAAAC,GAAA,CAAAtF,MAAA,CAAAJ,KAAA,EAAAkF,IAAA,GAAAU,OAAA;QACAT,IAAA,GAAAM,IAAA,CAAAC,GAAA,CAAAtF,MAAA,CAAAH,MAAA,EAAAkF,IAAA,GAAAS,OAAA;;QAEA;QACA,MAAA5F,KAAA,GAAAkF,IAAA,GAAAF,IAAA;QACA,MAAA/E,MAAA,GAAAkF,IAAA,GAAAF,IAAA;QAEA,IAAAjF,KAAA,SAAAC,MAAA,SAAAD,KAAA,GAAAI,MAAA,CAAAJ,KAAA,IAAAC,MAAA,GAAAG,MAAA,CAAAH,MAAA;UACA;UACA,MAAA4F,WAAA,GAAAtF,GAAA,CAAAwE,YAAA,CAAAC,IAAA,EAAAC,IAAA,EAAAjF,KAAA,EAAAC,MAAA;UACAG,MAAA,CAAAJ,KAAA,GAAAA,KAAA;UACAI,MAAA,CAAAH,MAAA,GAAAA,MAAA;UACAM,GAAA,CAAAuF,YAAA,CAAAD,WAAA;UACAjN,OAAA,CAAAC,GAAA,eAAAmH,KAAA,OAAAC,MAAA;QACA;UACArH,OAAA,CAAAC,GAAA;QACA;MACA,SAAAgE,CAAA;QACAjE,OAAA,CAAAgC,KAAA,eAAAiC,CAAA;MACA;IACA;IAEA;IACA+H,yBAAAxE,MAAA,EAAAG,GAAA,EAAAiF,UAAA,MAAAO,QAAA;MACA;QACA,MAAAjB,SAAA,GAAAvE,GAAA,CAAAwE,YAAA,OAAA3E,MAAA,CAAAJ,KAAA,EAAAI,MAAA,CAAAH,MAAA;QACA,MAAA5J,IAAA,GAAAyO,SAAA,CAAAzO,IAAA;;QAEA;QACA,MAAA2P,MAAA,UAAAD,QAAA,wBAAAA,QAAA;QAEA,SAAApH,CAAA,MAAAA,CAAA,GAAAtI,IAAA,CAAA6F,MAAA,EAAAyC,CAAA;UACA;UACAtI,IAAA,CAAAsI,CAAA,KAAA6G,UAAA;UACAnP,IAAA,CAAAsI,CAAA,SAAA6G,UAAA;UACAnP,IAAA,CAAAsI,CAAA,SAAA6G,UAAA;;UAEA;UACAnP,IAAA,CAAAsI,CAAA,IAAAqH,MAAA,IAAA3P,IAAA,CAAAsI,CAAA;UACAtI,IAAA,CAAAsI,CAAA,QAAAqH,MAAA,IAAA3P,IAAA,CAAAsI,CAAA;UACAtI,IAAA,CAAAsI,CAAA,QAAAqH,MAAA,IAAA3P,IAAA,CAAAsI,CAAA;;UAEA;UACAtI,IAAA,CAAAsI,CAAA,IAAA8G,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAAtP,IAAA,CAAAsI,CAAA;UACAtI,IAAA,CAAAsI,CAAA,QAAA8G,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAAtP,IAAA,CAAAsI,CAAA;UACAtI,IAAA,CAAAsI,CAAA,QAAA8G,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAAtP,IAAA,CAAAsI,CAAA;QACA;QAEA4B,GAAA,CAAAuF,YAAA,CAAAhB,SAAA;QACAlM,OAAA,CAAAC,GAAA;MACA,SAAAgE,CAAA;QACAjE,OAAA,CAAAgC,KAAA,gBAAAiC,CAAA;MACA;IACA;IAEA;IACAgI,aAAAzE,MAAA,EAAAG,GAAA;MACA;QACA,MAAAuE,SAAA,GAAAvE,GAAA,CAAAwE,YAAA,OAAA3E,MAAA,CAAAJ,KAAA,EAAAI,MAAA,CAAAH,MAAA;QACA,MAAA5J,IAAA,GAAAyO,SAAA,CAAAzO,IAAA;QACA,MAAA2J,KAAA,GAAAI,MAAA,CAAAJ,KAAA;QACA,MAAAC,MAAA,GAAAG,MAAA,CAAAH,MAAA;QACA,MAAAgG,WAAA,OAAAC,iBAAA,CAAA7P,IAAA;;QAEA;QACA,MAAA8P,MAAA,IACA,UACA,WACA,SACA;;QAEA;QACA,SAAAd,CAAA,MAAAA,CAAA,GAAApF,MAAA,MAAAoF,CAAA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAtF,KAAA,MAAAsF,CAAA;YACA,SAAAc,CAAA,MAAAA,CAAA,MAAAA,CAAA;cACA,IAAAC,GAAA;cACA,SAAAC,EAAA,OAAAA,EAAA,OAAAA,EAAA;gBACA,SAAAC,EAAA,OAAAA,EAAA,OAAAA,EAAA;kBACA,MAAAhB,GAAA,KAAAF,CAAA,GAAAiB,EAAA,IAAAtG,KAAA,IAAAsF,CAAA,GAAAiB,EAAA,SAAAH,CAAA;kBACAC,GAAA,IAAAhQ,IAAA,CAAAkP,GAAA,IAAAY,MAAA,EAAAG,EAAA,aAAAC,EAAA;gBACA;cACA;cACAN,WAAA,EAAAZ,CAAA,GAAArF,KAAA,GAAAsF,CAAA,QAAAc,CAAA,IAAAX,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAAU,GAAA;YACA;UACA;QACA;;QAEA;QACA,MAAAG,iBAAA,OAAAC,SAAA,CAAAR,WAAA,EAAAjG,KAAA,EAAAC,MAAA;QACAM,GAAA,CAAAuF,YAAA,CAAAU,iBAAA;QACA5N,OAAA,CAAAC,GAAA;MACA,SAAAgE,CAAA;QACAjE,OAAA,CAAAgC,KAAA,YAAAiC,CAAA;MACA;IACA;IAEA;IACAyC,WAAAf,QAAA;MACA3F,OAAA,CAAAC,GAAA,oBAAAjB,aAAA,CAAAI,SAAA;;MAEA;MACA,MAAA8F,MAAA;QACAC,OAAA;UACA;UACA;QACA;MACA;MAEAjI,KAAA,CAAAoI,IAAA,MAAAtG,aAAA,CAAAI,SAAA,EAAAuG,QAAA,EAAAT,MAAA,EACAH,IAAA,CAAAQ,QAAA;QACAvF,OAAA,CAAAC,GAAA,gBAAAsF,QAAA;QACA,KAAAC,eAAA,CAAAD,QAAA,CAAA9H,IAAA;MACA,GACAgI,KAAA,CAAAzD,KAAA;QAAA,IAAA8L,gBAAA;QACA9N,OAAA,CAAAgC,KAAA,YAAAA,KAAA;QACA,KAAAK,QAAA,CAAAL,KAAA,kBAAA8L,gBAAA,GAAA9L,KAAA,CAAAuD,QAAA,cAAAuI,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAArQ,IAAA,cAAAqQ,gBAAA,uBAAAA,gBAAA,CAAAnP,OAAA,KAAAqD,KAAA,CAAArD,OAAA;MACA,GACAgI,OAAA;QACA,KAAAnH,eAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}