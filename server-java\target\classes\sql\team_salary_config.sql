-- 创建班组工资计算方式配置表
CREATE TABLE IF NOT EXISTS `team_salary_config` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `project_code` varchar(100) NOT NULL COMMENT '项目编号',
  `team_no` varchar(100) NOT NULL COMMENT '班组编号',
  `team_name` varchar(200) NOT NULL COMMENT '班组名称',
  `salary_calc_method` varchar(20) NOT NULL COMMENT '工资计算方式：1-按天, 2-按月, 3-按工程量',
  `unit_price` decimal(10,2) NOT NULL COMMENT '计量单价(元)',
  `per_unit` decimal(10,2) DEFAULT NULL COMMENT '每(工程量数量)，按工程量计算时使用',
  `unit_type` int(11) DEFAULT NULL COMMENT '计量单位类型：80-米, 81-平方米, 82-立方米',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_project_team` (`project_code`,`team_name`) COMMENT '项目编号和班组名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班组工资计算方式配置表'; 