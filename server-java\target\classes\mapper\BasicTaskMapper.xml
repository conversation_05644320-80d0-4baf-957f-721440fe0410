<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daka.pro.mapper.BasicTaskMapper">

    <!-- 基础任务结果映射 -->
    <resultMap id="BaseResultMap" type="com.daka.pro.domain.BasicTask">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="handler_class" property="handlerClass"/>
    </resultMap>

    <!-- 分页查询基础任务列表 -->
    <select id="getBasicTaskList" resultMap="BaseResultMap">
        SELECT *
        FROM sys_basic_task
        <where>
            <if test="req.name != null and req.name != ''">
                AND name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.type != null and req.type != ''">
                AND type = #{req.type}
            </if>
            <if test="req.status != null and req.status != ''">
                AND status = #{req.status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 获取所有启用状态的基础任务 -->
    <select id="getAllEnabledBasicTasks" resultMap="BaseResultMap">
        SELECT *
        FROM sys_basic_task
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

</mapper> 