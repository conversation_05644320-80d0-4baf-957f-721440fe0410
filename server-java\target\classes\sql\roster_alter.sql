-- 花名册表添加字段脚本
-- 作者：Claude
-- 创建时间：2023-08-22
-- 描述：为roster表添加工人ID和参建单位ID字段

-- 检查字段是否已存在，如果不存在则添加
ALTER TABLE `roster` ADD COLUMN `work_id` VARCHAR(64) COMMENT '工人ID' AFTER `project_code`;
ALTER TABLE `roster` ADD COLUMN `project_sub_contractor_id` VARCHAR(64) COMMENT '参建单位ID' AFTER `work_id`;

-- 添加缺失字段到数据库表
ALTER TABLE roster
ADD COLUMN contract_type INT COMMENT '合同期限类型',
ADD COLUMN contract_start_date VARCHAR(100) COMMENT '合同期限开始日期',
ADD COLUMN contract_end_date VARCHAR(100) COMMENT '合同期限结束日期',
ADD COLUMN contract_sign_date VARCHAR(100) COMMENT '合同签订日期',
ADD COLUMN salary_type VARCHAR(10) COMMENT '工资支付方式',
ADD COLUMN salary_unit_price DOUBLE COMMENT '单位工资',
ADD COLUMN salary_per_unit DOUBLE COMMENT '每(工程量数量)',
ADD COLUMN salary_unit_type INT COMMENT '计量单位';

-- 如果数据库不支持IF NOT EXISTS语法，可以使用以下方式：
-- 
-- 添加工人ID字段
-- SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'roster' AND COLUMN_NAME = 'work_id');
-- SET @sqlstmt := IF(@exist > 0, 'SELECT "work_id字段已存在，无需添加"', 'ALTER TABLE `roster` ADD COLUMN `work_id` VARCHAR(64) COMMENT "工人ID" AFTER `project_code`');
-- PREPARE stmt FROM @sqlstmt;
-- EXECUTE stmt;
-- DEALLOCATE PREPARE stmt;
-- 
-- 添加参建单位ID字段
-- SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'roster' AND COLUMN_NAME = 'project_sub_contractor_id');
-- SET @sqlstmt := IF(@exist > 0, 'SELECT "project_sub_contractor_id字段已存在，无需添加"', 'ALTER TABLE `roster` ADD COLUMN `project_sub_contractor_id` VARCHAR(64) COMMENT "参建单位ID" AFTER `work_id`');
-- PREPARE stmt FROM @sqlstmt;
-- EXECUTE stmt;
-- DEALLOCATE PREPARE stmt; 