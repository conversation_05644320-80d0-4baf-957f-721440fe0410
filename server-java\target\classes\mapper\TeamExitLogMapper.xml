<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daka.pro.mapper.TeamExitLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.daka.pro.domain.TeamExitLog">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="project_code" property="projectCode" />
        <result column="execute_type" property="executeType" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="duration" property="duration" />
        <result column="total_count" property="totalCount" />
        <result column="success_count" property="successCount" />
        <result column="fail_count" property="failCount" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="fail_details" property="failDetails" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, project_code, execute_type, start_time, end_time, duration, 
        total_count, success_count, fail_count, status, error_msg, fail_details, create_time
    </sql>

    <!-- 分页查询暂退场日志 -->
    <select id="getTeamExitLogList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sys_team_exit_log
        WHERE 1 = 1
        <if test="projectCode != null and projectCode != ''">
            AND project_code = #{projectCode}
        </if>
        <if test="executeType != null">
            AND execute_type = #{executeType}
        </if>
        ORDER BY start_time DESC
    </select>

    <!-- 获取项目最后一次暂退场日志 -->
    <select id="getLastLogByProjectId" resultType="com.daka.pro.domain.TeamExitLog">
        SELECT *
        FROM sys_team_exit_log
        WHERE project_code = #{projectId}
        ORDER BY start_time DESC
        LIMIT 1
    </select>

</mapper> 