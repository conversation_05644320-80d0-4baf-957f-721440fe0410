-- 创建基础任务表
CREATE TABLE `sys_basic_task` (
  `id` varchar(64) NOT NULL COMMENT '任务ID',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `code` varchar(50) NOT NULL COMMENT '任务标识码',
  `type` varchar(50) NOT NULL COMMENT '任务类型',
  `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `handler_class` varchar(255) DEFAULT NULL COMMENT '任务处理器类名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基础任务表';

-- 修改任务表，添加基础任务关联字段
ALTER TABLE `sys_task` 
ADD COLUMN `basic_task_id` varchar(64) DEFAULT NULL COMMENT '基础任务ID' AFTER `project`,
ADD COLUMN `basic_task_name` varchar(100) DEFAULT NULL COMMENT '基础任务名称' AFTER `basic_task_id`;

-- 添加初始基础任务数据
INSERT INTO `sys_basic_task` (`id`, `name`, `code`, `type`, `description`, `status`, `create_time`, `update_time`, `handler_class`) VALUES
('1', '同步花名册', 'SYNC_ROSTER', 'SYNC_ROSTER', '同步项目花名册数据', 1, NOW(), NOW(), 'com.daka.pro.task.handler.SyncRosterHandler'),
('2', '同步考勤数据', 'SYNC_ATTENDANCE', 'SYNC_ATTENDANCE', '同步项目考勤数据', 1, NOW(), NOW(), 'com.daka.pro.task.handler.SyncAttendanceHandler'),
('3', '执行暂退场操作', 'EXECUTE_TEMPORARY_EXIT', 'EXECUTE_TEMPORARY_EXIT', '执行项目暂退场操作', 1, NOW(), NOW(), 'com.daka.pro.task.handler.ExecuteTemporaryExitHandler'); 