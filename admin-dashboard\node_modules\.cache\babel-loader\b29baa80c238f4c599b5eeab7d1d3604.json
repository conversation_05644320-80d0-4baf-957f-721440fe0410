{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep2.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep2.vue", "mtime": 1753347546119}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\babel.config.js", "mtime": 1746865124045}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getBankInfoList", "getBankInfoByBin", "name", "props", "workerInfo", "type", "Object", "required", "data", "validateBankCard", "rule", "value", "callback", "Error", "cardNumber", "replace", "test", "validateIdCard", "bankForm", "worker<PERSON>ame", "idCardNumber", "bankCardNumber", "bankName", "bankCode", "rules", "message", "trigger", "validator", "bankSelectorVisible", "bankListLoading", "bankList", "bankSearchKeyword", "selectedBank", "selectedBankId", "bankListQuery", "page", "limit", "totalBankCount", "computed", "filteredBankList", "pagedBankList", "created", "console", "log", "JSON", "stringify", "initFormFromProps", "tryRestoreFromLocalStorage", "mounted", "$nextTick", "$forceUpdate", "<PERSON><PERSON><PERSON><PERSON>", "$route", "path", "includes", "localStorage", "removeItem", "saveToLocalStorage", "watch", "handler", "newVal", "oldVal", "deep", "immediate", "methods", "showBankSelector", "loadBankList", "then", "response", "code", "count", "$message", "error", "msg", "catch", "searchBanks", "handleSizeChange", "val", "handleCurrentChange", "rowSelectable", "row", "index", "handleRadioChange", "selectBank", "id", "$refs", "bankTable", "setCurrentRow", "handleRowClick", "handleRowDblClick", "confirmBankSelection", "warning", "queryBankInfo", "length", "bank", "getBankCardClass", "bankNameMap", "getBankLogo", "char<PERSON>t", "clearContractData", "shouldEmit", "savedWorkerInfo", "getItem", "parsedData", "parse", "$emit", "existingData", "saved", "assign", "e", "dataToSave", "setItem", "formatBankCardNumber", "substring", "formattedNumber", "i", "formatDisplayCardNumber", "prevStep", "bankInfo", "nextStep", "validate", "valid", "gender", "home<PERSON>dd<PERSON>", "teamName", "jobPosition", "participantName"], "sources": ["src/views/contract/ContractStep2.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contract-step2-container\">\r\n    <h3>步骤2：银行卡信息</h3>\r\n    \r\n    <div class=\"bank-info-form\">\r\n      <el-form :model=\"bankForm\" label-width=\"120px\" ref=\"bankForm\" :rules=\"rules\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\r\n              <el-input v-model=\"bankForm.workerName\" placeholder=\"请输入工人姓名\" disabled></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\r\n              <el-input v-model=\"bankForm.idCardNumber\" placeholder=\"请输入证件号码\" disabled></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行卡号\" prop=\"bankCardNumber\">\r\n              <el-input v-model=\"bankForm.bankCardNumber\" placeholder=\"请输入银行卡号\" maxlength=\"23\" @input=\"formatBankCardNumber\" @blur=\"queryBankInfo\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行名称\" prop=\"bankName\">\r\n              <el-input v-model=\"bankForm.bankName\" placeholder=\"银行名称将自动识别\" readonly></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 删除调试信息显示区域 -->\r\n      \r\n      <div class=\"bank-card-preview\">\r\n        <div class=\"card-container\" :class=\"getBankCardClass()\">\r\n          <div class=\"card-header\">\r\n            <div class=\"bank-logo\">{{ getBankLogo() }}</div>\r\n            <div class=\"bank-name\">{{ bankForm.bankName || '银行卡' }}</div>\r\n          </div>\r\n          <div class=\"card-number\">{{ formatDisplayCardNumber() }}</div>\r\n          <div class=\"card-footer\">\r\n            <div class=\"card-holder\">持卡人：{{ bankForm.workerName || '未填写' }}</div>\r\n            <div class=\"card-valid\">有效期：长期</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-actions\">\r\n        <el-button @click=\"prevStep\">上一步</el-button>\r\n        <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 银行选择对话框 -->\r\n    <el-dialog title=\"选择开户银行\" :visible.sync=\"bankSelectorVisible\" width=\"50%\">\r\n      <div class=\"bank-search\">\r\n        <el-input\r\n          placeholder=\"请输入银行名称或代码\"\r\n          v-model=\"bankSearchKeyword\"\r\n          clearable\r\n          @keyup.enter.native=\"searchBanks\"\r\n        >\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchBanks\"></el-button>\r\n        </el-input>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bankListLoading\"\r\n        :data=\"pagedBankList\"\r\n        border\r\n        style=\"width: 100%; margin-top: 15px;\"\r\n        height=\"400px\"\r\n        @row-click=\"handleRowClick\"\r\n        @row-dblclick=\"handleRowDblClick\"\r\n        highlight-current-row\r\n        ref=\"bankTable\"\r\n      >\r\n        <el-table-column width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio :label=\"scope.row.id\" v-model=\"selectedBankId\" @change.native=\"handleRadioChange(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bankName\" label=\"银行名称\" min-width=\"200\"></el-table-column>\r\n        <el-table-column prop=\"bankCode\" label=\"银行代码\" width=\"150\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"bankListQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"bankListQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"totalBankCount\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bankSelectorVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBankSelection\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getBankInfoList } from '@/api/bankInfo'\r\nimport { getBankInfoByBin } from '@/api/bankCardBin'\r\n\r\nexport default {\r\n  name: 'ContractStep2',\r\n  props: {\r\n    workerInfo: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    // 银行卡号验证\r\n    const validateBankCard = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请输入银行卡号'))\r\n      }\r\n      \r\n      // 移除空格后验证\r\n      const cardNumber = value.replace(/\\s/g, '')\r\n      if (!/^\\d{16,19}$/.test(cardNumber)) {\r\n        return callback(new Error('银行卡号格式不正确，应为16-19位数字'))\r\n      }\r\n      \r\n      callback()\r\n    }\r\n    \r\n    // 证件号码验证\r\n    const validateIdCard = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请输入证件号码'))\r\n      }\r\n      \r\n      // 简单验证18位或15位身份证号\r\n      // const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\r\n      // if (!reg.test(value)) {\r\n      //   return callback(new Error('证件号码格式不正确'))\r\n      // }\r\n      \r\n      callback()\r\n    }\r\n    \r\n    return {\r\n      bankForm: {\r\n        workerName: '',\r\n        idCardNumber: '',\r\n        bankCardNumber: '',\r\n        bankName: '',\r\n        bankCode: ''\r\n      },\r\n      rules: {\r\n        workerName: [\r\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\r\n        ],\r\n        idCardNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ],\r\n        bankCardNumber: [\r\n          { required: true, message: '请输入银行卡号', trigger: 'blur' },\r\n          { validator: validateBankCard, trigger: 'blur' }\r\n        ],\r\n        bankName: [\r\n          { required: true, message: '银行名称不能为空', trigger: 'change' }\r\n        ]\r\n      },\r\n      // 银行选择相关数据\r\n      bankSelectorVisible: false,\r\n      bankListLoading: false,\r\n      bankList: [],\r\n      bankSearchKeyword: '',\r\n      selectedBank: null,\r\n      selectedBankId: null, // 添加selectedBankId用于单选按钮\r\n      bankListQuery: {\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      totalBankCount: 0 // 添加总银行数量字段\r\n    }\r\n  },\r\n  computed: {\r\n    filteredBankList() {\r\n      return this.bankList;\r\n    },\r\n    pagedBankList() {\r\n      return this.bankList;\r\n    }\r\n  },\r\n  created() {\r\n    // 添加详细的调试日志\r\n    console.log('=== ContractStep2 created 开始 ===')\r\n    console.log('ContractStep2 created - 接收到的props:', JSON.stringify(this.workerInfo))\r\n    console.log('ContractStep2 created - 初始表单数据:', JSON.stringify(this.bankForm))\r\n\r\n    // 首先尝试从props初始化表单\r\n    this.initFormFromProps()\r\n\r\n    // 如果props中没有姓名和证件号码，尝试从localStorage恢复\r\n    if (!this.bankForm.workerName || !this.bankForm.idCardNumber) {\r\n      console.log('ContractStep2 created - props中缺少数据，尝试从localStorage恢复')\r\n      // false表示不要emit更新父组件\r\n      this.tryRestoreFromLocalStorage(false)\r\n    }\r\n\r\n    // 添加调试日志\r\n    console.log('ContractStep2 created - 最终表单数据:', JSON.stringify(this.bankForm))\r\n    console.log('=== ContractStep2 created 结束 ===')\r\n  },\r\n  mounted() {\r\n    // 在组件挂载后，强制刷新表单数据\r\n    this.$nextTick(() => {\r\n      // 确保数据已经正确初始化\r\n      if ((!this.bankForm.workerName || !this.bankForm.idCardNumber) && this.workerInfo) {\r\n        console.log('ContractStep2 mounted - 重新从props初始化数据')\r\n        this.initFormFromProps()\r\n      }\r\n      \r\n      // 如果还是没有数据，尝试从localStorage恢复\r\n      if (!this.bankForm.workerName || !this.bankForm.idCardNumber) {\r\n        console.log('ContractStep2 mounted - 尝试从localStorage恢复数据')\r\n        this.tryRestoreFromLocalStorage(true)\r\n      }\r\n      \r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n      \r\n      // 添加调试日志\r\n      console.log('ContractStep2 mounted - 刷新后的表单数据:', JSON.stringify(this.bankForm))\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 只有当完全离开合同流程时才清空数据\r\n    // 不要在步骤之间导航时清空\r\n    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {\r\n      console.log('ContractStep2 - 离开合同流程，清空数据')\r\n      localStorage.removeItem('contractWorkerInfo')\r\n    } else {\r\n      console.log('ContractStep2 - 在合同流程内导航，保留数据')\r\n      // 确保数据已保存到localStorage\r\n      this.saveToLocalStorage()\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听 workerInfo 变化\r\n    workerInfo: {\r\n      handler(newVal, oldVal) {\r\n        // 只在有实质变化时处理\r\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\r\n          console.log('ContractStep2 - workerInfo变化:', JSON.stringify(newVal))\r\n          \r\n          // 如果新的 workerInfo 包含有效的工人信息，初始化表单\r\n          if (newVal?.workerName || newVal?.idCardNumber) {\r\n            this.$nextTick(() => {\r\n              this.initFormFromProps();\r\n              console.log('ContractStep2 - 从props更新后的表单数据:', JSON.stringify(this.bankForm))\r\n            });\r\n          }\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true // 立即触发，确保组件创建时就处理props\r\n    }\r\n  },\r\n  methods: {\r\n    // 显示银行选择器\r\n    showBankSelector() {\r\n      this.bankSelectorVisible = true;\r\n      this.bankSearchKeyword = '';\r\n      this.loadBankList();\r\n    },\r\n    \r\n    // 加载银行列表\r\n    loadBankList() {\r\n      this.bankListLoading = true;\r\n      getBankInfoList({\r\n        page: this.bankListQuery.page,\r\n        limit: this.bankListQuery.limit,\r\n        bankName: this.bankSearchKeyword,  // 添加搜索关键词\r\n        bankCode: this.bankSearchKeyword   // 添加搜索关键词\r\n      }).then(response => {\r\n        if (response.code === 0) {\r\n          this.bankList = response.data || [];\r\n          this.totalBankCount = response.count || 0; // 设置总记录数\r\n        } else {\r\n          this.$message.error(response.msg || '获取银行列表失败');\r\n          this.bankList = [];\r\n          this.totalBankCount = 0;\r\n        }\r\n        this.bankListLoading = false;\r\n      }).catch(() => {\r\n        this.bankListLoading = false;\r\n        this.$message.error('获取银行列表失败');\r\n      });\r\n    },\r\n    \r\n    // 搜索银行\r\n    searchBanks() {\r\n      // 重置到第一页\r\n      this.bankListQuery.page = 1;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 处理每页条数变化\r\n    handleSizeChange(val) {\r\n      this.bankListQuery.limit = val;\r\n      this.bankListQuery.page = 1;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.bankListQuery.page = val;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 控制行是否可选\r\n    rowSelectable(row, index) {\r\n      return true; // 所有行都可选\r\n    },\r\n    \r\n    // 处理单选按钮变化\r\n    handleRadioChange(row) {\r\n      this.selectBank(row);\r\n    },\r\n    \r\n    // 选择银行\r\n    selectBank(row) {\r\n      this.selectedBank = row;\r\n      this.selectedBankId = row.id;\r\n      // 设置当前行高亮\r\n      this.$refs.bankTable.setCurrentRow(row);\r\n    },\r\n    \r\n    // 处理行点击事件\r\n    handleRowClick(row) {\r\n      this.selectBank(row);\r\n    },\r\n    \r\n    // 处理行双击事件\r\n    handleRowDblClick(row) {\r\n      this.selectBank(row);\r\n      this.confirmBankSelection();\r\n    },\r\n    \r\n    // 确认银行选择\r\n    confirmBankSelection() {\r\n      if (this.selectedBank) {\r\n        this.bankForm.bankName = this.selectedBank.bankName;\r\n        this.bankForm.bankCode = this.selectedBank.bankCode;\r\n        this.bankSelectorVisible = false;\r\n      } else {\r\n        this.$message.warning('请选择一家银行');\r\n      }\r\n    },\r\n    \r\n    // 根据银行卡号查询银行信息\r\n    queryBankInfo() {\r\n      const cardNumber = this.bankForm.bankCardNumber.replace(/\\s/g, '');\r\n      if (cardNumber.length >= 3) {  // 改为3位就开始查询\r\n        getBankInfoByBin(cardNumber).then(response => {  // 传入完整卡号，让后端处理\r\n          if (response.code === 0 && response.data) {\r\n            this.bankForm.bankName = response.data.bank;\r\n            this.bankForm.bankCode = response.data.bankCode;\r\n          } else {\r\n            this.bankForm.bankName = '';\r\n            this.bankForm.bankCode = '';\r\n            // 只有在输入足够位数时才提示未找到\r\n            if (cardNumber.length >= 6) {\r\n              this.$message.warning('未找到对应的银行信息');\r\n            }\r\n          }\r\n        }).catch(() => {\r\n          this.bankForm.bankName = '';\r\n          this.bankForm.bankCode = '';\r\n          if (cardNumber.length >= 6) {\r\n            this.$message.error('查询银行信息失败');\r\n          }\r\n        });\r\n      } else {\r\n        this.bankForm.bankName = '';\r\n        this.bankForm.bankCode = '';\r\n      }\r\n    },\r\n    \r\n    // 获取银行卡样式类\r\n    getBankCardClass() {\r\n      if (!this.bankForm.bankName) return 'default-card';\r\n      \r\n      // 根据银行名称返回对应的样式类\r\n      const bankNameMap = {\r\n        '中国工商银行': 'icbc-card',\r\n        '中国农业银行': 'abc-card',\r\n        '中国建设银行': 'ccb-card',\r\n        '中国银行': 'boc-card',\r\n        '交通银行': 'bocom-card',\r\n        '中国邮政储蓄银行': 'psbc-card',\r\n        '招商银行': 'cmb-card',\r\n        '中信银行': 'citic-card',\r\n        '上海浦东发展银行': 'spdb-card',\r\n        '兴业银行': 'cib-card',\r\n        '中国光大银行': 'ceb-card',\r\n        '中国民生银行': 'cmbc-card',\r\n        '华夏银行': 'hxb-card',\r\n        '广发银行': 'cgb-card',\r\n        '平安银行': 'pab-card'\r\n      };\r\n      \r\n      return bankNameMap[this.bankForm.bankName] || 'default-card';\r\n    },\r\n    \r\n    // 获取银行Logo\r\n    getBankLogo() {\r\n      if (!this.bankForm.bankName) return '银';\r\n      \r\n      // 返回银行名称的第一个字\r\n      return this.bankForm.bankName.charAt(0);\r\n    },\r\n    \r\n    // 清空合同数据\r\n    clearContractData() {\r\n      console.log('ContractStep2 - 清空合同数据')\r\n      localStorage.removeItem('contractWorkerInfo')\r\n    },\r\n    \r\n    // 尝试从localStorage恢复数据\r\n    tryRestoreFromLocalStorage(shouldEmit = true) {\r\n      try {\r\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\r\n        if (savedWorkerInfo) {\r\n          const parsedData = JSON.parse(savedWorkerInfo)\r\n          console.log('ContractStep2 - 从本地存储恢复数据:', JSON.stringify(parsedData))\r\n          \r\n          // 更新表单数据\r\n          this.bankForm.workerName = parsedData.workerName || this.bankForm.workerName || '';\r\n          this.bankForm.idCardNumber = parsedData.idCardNumber || this.bankForm.idCardNumber || '';\r\n          this.bankForm.bankCardNumber = parsedData.bankCardNumber || this.bankForm.bankCardNumber || '';\r\n          this.bankForm.bankName = parsedData.bankName || this.bankForm.bankName || '';\r\n          this.bankForm.bankCode = parsedData.bankCode || this.bankForm.bankCode || '';\r\n          \r\n          // 只在需要时更新父组件数据\r\n          if (shouldEmit && (parsedData.workerName || parsedData.idCardNumber)) {\r\n            this.$emit('update-worker-info', { ...parsedData })\r\n          }\r\n          \r\n          return true;\r\n        }\r\n      } catch (error) {\r\n        console.error('ContractStep2 - 从本地存储恢复数据失败:', error)\r\n      }\r\n      return false;\r\n    },\r\n    \r\n    // 保存数据到localStorage\r\n    saveToLocalStorage() {\r\n      try {\r\n        // 确保保存的数据包含所有必要字段\r\n        const existingData = {};\r\n        try {\r\n          const saved = localStorage.getItem('contractWorkerInfo');\r\n          if (saved) {\r\n            Object.assign(existingData, JSON.parse(saved));\r\n          }\r\n        } catch (e) {\r\n          console.error('解析已存储数据失败:', e)\r\n        }\r\n        \r\n        // 合并现有数据和当前表单数据\r\n        const dataToSave = { \r\n          ...existingData,\r\n          // 确保工人和银行卡信息都被保存\r\n          workerName: this.bankForm.workerName || existingData.workerName || '',\r\n          idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n          bankCardNumber: this.bankForm.bankCardNumber || '',\r\n          bankName: this.bankForm.bankName || '',\r\n          bankCode: this.bankForm.bankCode || ''\r\n        };\r\n        \r\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave));\r\n        console.log('ContractStep2 - 已保存数据到localStorage:', JSON.stringify(dataToSave))\r\n        return dataToSave;\r\n      } catch (error) {\r\n        console.error('ContractStep2 - 保存到本地存储失败:', error);\r\n        return this.bankForm;\r\n      }\r\n    },\r\n    \r\n    // 从props初始化表单\r\n    initFormFromProps() {\r\n      // 直接从props中获取数据\r\n      if (this.workerInfo) {\r\n        console.log('ContractStep2 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))\r\n        \r\n        // 防止覆盖已有的有效数据\r\n        if (this.workerInfo.workerName) this.bankForm.workerName = this.workerInfo.workerName;\r\n        if (this.workerInfo.idCardNumber) this.bankForm.idCardNumber = this.workerInfo.idCardNumber;\r\n        if (this.workerInfo.bankCardNumber) this.bankForm.bankCardNumber = this.workerInfo.bankCardNumber;\r\n        if (this.workerInfo.bankName) this.bankForm.bankName = this.workerInfo.bankName;\r\n        if (this.workerInfo.bankCode) this.bankForm.bankCode = this.workerInfo.bankCode;\r\n        \r\n        // 如果从props中获取到了有效的工人信息，保存到localStorage\r\n        if (this.workerInfo.workerName || this.workerInfo.idCardNumber) {\r\n          this.saveToLocalStorage();\r\n        }\r\n        \r\n        // 强制更新视图\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 格式化银行卡号（每4位添加空格）\r\n    formatBankCardNumber(value) {\r\n      if (!value) return\r\n      \r\n      // 移除所有空格\r\n      let cardNumber = value.replace(/\\s/g, '')\r\n      \r\n      // 限制只能输入数字\r\n      cardNumber = cardNumber.replace(/[^\\d]/g, '')\r\n      \r\n      // 限制最大长度为19位\r\n      cardNumber = cardNumber.substring(0, 19)\r\n      \r\n      // 每4位添加空格\r\n      let formattedNumber = ''\r\n      for (let i = 0; i < cardNumber.length; i++) {\r\n        if (i > 0 && i % 4 === 0) {\r\n          formattedNumber += ' '\r\n        }\r\n        formattedNumber += cardNumber.charAt(i)\r\n      }\r\n      \r\n      // 更新表单值\r\n      this.bankForm.bankCardNumber = formattedNumber\r\n      \r\n      // 如果输入了足够的位数，自动查询银行信息\r\n      if (cardNumber.length >= 3) {  // 改为3位就开始查询\r\n        this.queryBankInfo();\r\n      } else {\r\n        this.bankForm.bankName = '';\r\n        this.bankForm.bankCode = '';\r\n      }\r\n    },\r\n    \r\n    // 获取银行卡预览显示的卡号\r\n    formatDisplayCardNumber() {\r\n      if (!this.bankForm.bankCardNumber) {\r\n        return '**** **** **** ****'\r\n      }\r\n      \r\n      return this.bankForm.bankCardNumber\r\n    },\r\n    \r\n    // 上一步\r\n    prevStep() {\r\n      // 准备要保存的数据\r\n      const existingData = {};\r\n      try {\r\n        const saved = localStorage.getItem('contractWorkerInfo');\r\n        if (saved) {\r\n          Object.assign(existingData, JSON.parse(saved));\r\n        }\r\n      } catch (e) {}\r\n      \r\n      // 合并现有数据和当前表单数据\r\n      const bankInfo = {\r\n        ...existingData, // 保留其他字段的数据\r\n        // 确保工人的基本信息和银行卡信息被保存\r\n        workerName: this.bankForm.workerName || existingData.workerName || '',\r\n        idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n        bankCardNumber: this.bankForm.bankCardNumber || '',\r\n        bankName: this.bankForm.bankName || '',\r\n        bankCode: this.bankForm.bankCode || ''\r\n      };\r\n      \r\n      // 保存到本地存储\r\n      localStorage.setItem('contractWorkerInfo', JSON.stringify(bankInfo));\r\n      \r\n      // 先更新数据，再通知父组件导航\r\n      this.$emit('update-worker-info', bankInfo);\r\n      \r\n      // 等待数据更新后再导航\r\n      this.$nextTick(() => {\r\n        // 发送上一步事件，由父组件处理导航\r\n        this.$emit('prev-step');\r\n      });\r\n    },\r\n    \r\n    // 下一步\r\n    nextStep() {\r\n      this.$refs.bankForm.validate(valid => {\r\n        if (valid) {\r\n          // 准备要保存的数据\r\n          const existingData = {};\r\n          try {\r\n            const saved = localStorage.getItem('contractWorkerInfo');\r\n            if (saved) {\r\n              Object.assign(existingData, JSON.parse(saved));\r\n            }\r\n          } catch (e) {}\r\n          \r\n          // 合并现有数据和当前表单数据\r\n          const bankInfo = {\r\n            ...existingData, // 保留其他字段的数据\r\n            // 确保工人的基本信息也被保存\r\n            workerName: this.bankForm.workerName || existingData.workerName || '',\r\n            idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n            gender: existingData.gender || '',\r\n            homeAddress: existingData.homeAddress || '',\r\n            teamName: existingData.teamName || '',\r\n            jobPosition: existingData.jobPosition || '',\r\n            participantName: existingData.participantName || '',\r\n            // 银行卡信息\r\n            bankCardNumber: this.bankForm.bankCardNumber || '',\r\n            bankName: this.bankForm.bankName || '',\r\n            bankCode: this.bankForm.bankCode || ''\r\n          };\r\n          \r\n          // 保存到本地存储\r\n          localStorage.setItem('contractWorkerInfo', JSON.stringify(bankInfo));\r\n          \r\n          // 更新父组件中的工人信息\r\n          this.$emit('update-worker-info', bankInfo);\r\n          \r\n          // 等待数据更新后再导航\r\n          this.$nextTick(() => {\r\n            // 发送下一步事件，由父组件处理导航\r\n            this.$emit('next-step');\r\n          });\r\n        } else {\r\n          this.$message.warning('请完善银行卡信息');\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contract-step2-container {\r\n  padding: 20px;\r\n}\r\n\r\n.contract-step2-container h3 {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  color: #303133;\r\n}\r\n\r\n.bank-info-form {\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.bank-card-preview {\r\n  margin: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.card-container {\r\n  width: 360px;\r\n  height: 220px;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  background: linear-gradient(135deg, #0a4b78, #0a78a1);\r\n  color: white;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-container::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: -50px;\r\n  right: -50px;\r\n  width: 150px;\r\n  height: 150px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  z-index: 1;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 2;\r\n}\r\n\r\n.bank-logo {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-right: 15px;\r\n}\r\n\r\n.bank-name {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.card-number {\r\n  font-size: 22px;\r\n  letter-spacing: 2px;\r\n  font-family: 'Courier New', monospace;\r\n  text-align: center;\r\n  margin: 20px 0;\r\n  z-index: 2;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  z-index: 2;\r\n}\r\n\r\n/* 银行卡样式 */\r\n.icbc-card {\r\n  background: linear-gradient(135deg, #d10b0b, #ff5252);\r\n}\r\n\r\n.abc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.ccb-card {\r\n  background: linear-gradient(135deg, #003399, #0066cc);\r\n}\r\n\r\n.boc-card {\r\n  background: linear-gradient(135deg, #8b0000, #cc0000);\r\n}\r\n\r\n.bocom-card {\r\n  background: linear-gradient(135deg, #0066cc, #3399ff);\r\n}\r\n\r\n.psbc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.cmb-card {\r\n  background: linear-gradient(135deg, #cc0000, #ff3333);\r\n}\r\n\r\n.citic-card {\r\n  background: linear-gradient(135deg, #003366, #0066cc);\r\n}\r\n\r\n.spdb-card {\r\n  background: linear-gradient(135deg, #006699, #0099cc);\r\n}\r\n\r\n.cib-card {\r\n  background: linear-gradient(135deg, #003366, #0066cc);\r\n}\r\n\r\n.ceb-card {\r\n  background: linear-gradient(135deg, #990000, #cc3333);\r\n}\r\n\r\n.cmbc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.hxb-card {\r\n  background: linear-gradient(135deg, #990000, #cc3333);\r\n}\r\n\r\n.cgb-card {\r\n  background: linear-gradient(135deg, #cc0000, #ff3333);\r\n}\r\n\r\n.pab-card {\r\n  background: linear-gradient(135deg, #cc6600, #ff9933);\r\n}\r\n\r\n.default-card {\r\n  background: linear-gradient(135deg, #0a4b78, #0a78a1);\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 银行选择对话框样式 */\r\n.bank-search {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .card-container {\r\n    width: 300px;\r\n    height: 180px;\r\n    padding: 15px;\r\n  }\r\n  \r\n  .bank-logo {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .bank-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .card-number {\r\n    font-size: 18px;\r\n    margin: 15px 0;\r\n  }\r\n  \r\n  .card-footer {\r\n    font-size: 12px;\r\n  }\r\n}\r\n</style> \r\n"], "mappings": ";AAgHA,SAAAA,eAAA;AACA,SAAAC,gBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;IACA,MAAAC,gBAAA,GAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;;MAEA;MACA,MAAAC,UAAA,GAAAH,KAAA,CAAAI,OAAA;MACA,mBAAAC,IAAA,CAAAF,UAAA;QACA,OAAAF,QAAA,KAAAC,KAAA;MACA;MAEAD,QAAA;IACA;;IAEA;IACA,MAAAK,cAAA,GAAAA,CAAAP,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEAD,QAAA;IACA;IAEA;MACAM,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,KAAA;QACAL,UAAA,GACA;UAAAZ,QAAA;UAAAkB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,YAAA,GACA;UAAAb,QAAA;UAAAkB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAV,cAAA;UAAAS,OAAA;QAAA,EACA;QACAL,cAAA,GACA;UAAAd,QAAA;UAAAkB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAlB,gBAAA;UAAAiB,OAAA;QAAA,EACA;QACAJ,QAAA,GACA;UAAAf,QAAA;UAAAkB,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,mBAAA;MACAC,eAAA;MACAC,QAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,cAAA;MAAA;MACAC,aAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACAC,iBAAA;MACA,YAAAT,QAAA;IACA;IACAU,cAAA;MACA,YAAAV,QAAA;IACA;EACA;EACAW,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;IACAD,OAAA,CAAAC,GAAA,uCAAAC,IAAA,CAAAC,SAAA,MAAAzC,UAAA;IACAsC,OAAA,CAAAC,GAAA,oCAAAC,IAAA,CAAAC,SAAA,MAAA3B,QAAA;;IAEA;IACA,KAAA4B,iBAAA;;IAEA;IACA,UAAA5B,QAAA,CAAAC,UAAA,UAAAD,QAAA,CAAAE,YAAA;MACAsB,OAAA,CAAAC,GAAA;MACA;MACA,KAAAI,0BAAA;IACA;;IAEA;IACAL,OAAA,CAAAC,GAAA,oCAAAC,IAAA,CAAAC,SAAA,MAAA3B,QAAA;IACAwB,OAAA,CAAAC,GAAA;EACA;EACAK,QAAA;IACA;IACA,KAAAC,SAAA;MACA;MACA,WAAA/B,QAAA,CAAAC,UAAA,UAAAD,QAAA,CAAAE,YAAA,UAAAhB,UAAA;QACAsC,OAAA,CAAAC,GAAA;QACA,KAAAG,iBAAA;MACA;;MAEA;MACA,UAAA5B,QAAA,CAAAC,UAAA,UAAAD,QAAA,CAAAE,YAAA;QACAsB,OAAA,CAAAC,GAAA;QACA,KAAAI,0BAAA;MACA;;MAEA;MACA,KAAAG,YAAA;;MAEA;MACAR,OAAA,CAAAC,GAAA,sCAAAC,IAAA,CAAAC,SAAA,MAAA3B,QAAA;IACA;EACA;EACAiC,cAAA;IACA;IACA;IACA,SAAAC,MAAA,UAAAA,MAAA,CAAAC,IAAA,CAAAC,QAAA;MACAZ,OAAA,CAAAC,GAAA;MACAY,YAAA,CAAAC,UAAA;IACA;MACAd,OAAA,CAAAC,GAAA;MACA;MACA,KAAAc,kBAAA;IACA;EACA;EACAC,KAAA;IACA;IACAtD,UAAA;MACAuD,QAAAC,MAAA,EAAAC,MAAA;QACA;QACA,IAAAjB,IAAA,CAAAC,SAAA,CAAAe,MAAA,MAAAhB,IAAA,CAAAC,SAAA,CAAAgB,MAAA;UACAnB,OAAA,CAAAC,GAAA,kCAAAC,IAAA,CAAAC,SAAA,CAAAe,MAAA;;UAEA;UACA,IAAAA,MAAA,aAAAA,MAAA,eAAAA,MAAA,CAAAzC,UAAA,IAAAyC,MAAA,aAAAA,MAAA,eAAAA,MAAA,CAAAxC,YAAA;YACA,KAAA6B,SAAA;cACA,KAAAH,iBAAA;cACAJ,OAAA,CAAAC,GAAA,oCAAAC,IAAA,CAAAC,SAAA,MAAA3B,QAAA;YACA;UACA;QACA;MACA;MACA4C,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,iBAAA;MACA,KAAArC,mBAAA;MACA,KAAAG,iBAAA;MACA,KAAAmC,YAAA;IACA;IAEA;IACAA,aAAA;MACA,KAAArC,eAAA;MACA7B,eAAA;QACAmC,IAAA,OAAAD,aAAA,CAAAC,IAAA;QACAC,KAAA,OAAAF,aAAA,CAAAE,KAAA;QACAd,QAAA,OAAAS,iBAAA;QAAA;QACAR,QAAA,OAAAQ,iBAAA;MACA,GAAAoC,IAAA,CAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA,KAAAvC,QAAA,GAAAsC,QAAA,CAAA5D,IAAA;UACA,KAAA6B,cAAA,GAAA+B,QAAA,CAAAE,KAAA;QACA;UACA,KAAAC,QAAA,CAAAC,KAAA,CAAAJ,QAAA,CAAAK,GAAA;UACA,KAAA3C,QAAA;UACA,KAAAO,cAAA;QACA;QACA,KAAAR,eAAA;MACA,GAAA6C,KAAA;QACA,KAAA7C,eAAA;QACA,KAAA0C,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAG,YAAA;MACA;MACA,KAAAzC,aAAA,CAAAC,IAAA;MACA,KAAA+B,YAAA;IACA;IAEA;IACAU,iBAAAC,GAAA;MACA,KAAA3C,aAAA,CAAAE,KAAA,GAAAyC,GAAA;MACA,KAAA3C,aAAA,CAAAC,IAAA;MACA,KAAA+B,YAAA;IACA;IAEA;IACAY,oBAAAD,GAAA;MACA,KAAA3C,aAAA,CAAAC,IAAA,GAAA0C,GAAA;MACA,KAAAX,YAAA;IACA;IAEA;IACAa,cAAAC,GAAA,EAAAC,KAAA;MACA;IACA;IAEA;IACAC,kBAAAF,GAAA;MACA,KAAAG,UAAA,CAAAH,GAAA;IACA;IAEA;IACAG,WAAAH,GAAA;MACA,KAAAhD,YAAA,GAAAgD,GAAA;MACA,KAAA/C,cAAA,GAAA+C,GAAA,CAAAI,EAAA;MACA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,aAAA,CAAAP,GAAA;IACA;IAEA;IACAQ,eAAAR,GAAA;MACA,KAAAG,UAAA,CAAAH,GAAA;IACA;IAEA;IACAS,kBAAAT,GAAA;MACA,KAAAG,UAAA,CAAAH,GAAA;MACA,KAAAU,oBAAA;IACA;IAEA;IACAA,qBAAA;MACA,SAAA1D,YAAA;QACA,KAAAd,QAAA,CAAAI,QAAA,QAAAU,YAAA,CAAAV,QAAA;QACA,KAAAJ,QAAA,CAAAK,QAAA,QAAAS,YAAA,CAAAT,QAAA;QACA,KAAAK,mBAAA;MACA;QACA,KAAA2C,QAAA,CAAAoB,OAAA;MACA;IACA;IAEA;IACAC,cAAA;MACA,MAAA9E,UAAA,QAAAI,QAAA,CAAAG,cAAA,CAAAN,OAAA;MACA,IAAAD,UAAA,CAAA+E,MAAA;QAAA;QACA5F,gBAAA,CAAAa,UAAA,EAAAqD,IAAA,CAAAC,QAAA;UAAA;UACA,IAAAA,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAA5D,IAAA;YACA,KAAAU,QAAA,CAAAI,QAAA,GAAA8C,QAAA,CAAA5D,IAAA,CAAAsF,IAAA;YACA,KAAA5E,QAAA,CAAAK,QAAA,GAAA6C,QAAA,CAAA5D,IAAA,CAAAe,QAAA;UACA;YACA,KAAAL,QAAA,CAAAI,QAAA;YACA,KAAAJ,QAAA,CAAAK,QAAA;YACA;YACA,IAAAT,UAAA,CAAA+E,MAAA;cACA,KAAAtB,QAAA,CAAAoB,OAAA;YACA;UACA;QACA,GAAAjB,KAAA;UACA,KAAAxD,QAAA,CAAAI,QAAA;UACA,KAAAJ,QAAA,CAAAK,QAAA;UACA,IAAAT,UAAA,CAAA+E,MAAA;YACA,KAAAtB,QAAA,CAAAC,KAAA;UACA;QACA;MACA;QACA,KAAAtD,QAAA,CAAAI,QAAA;QACA,KAAAJ,QAAA,CAAAK,QAAA;MACA;IACA;IAEA;IACAwE,iBAAA;MACA,UAAA7E,QAAA,CAAAI,QAAA;;MAEA;MACA,MAAA0E,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,OAAAA,WAAA,MAAA9E,QAAA,CAAAI,QAAA;IACA;IAEA;IACA2E,YAAA;MACA,UAAA/E,QAAA,CAAAI,QAAA;;MAEA;MACA,YAAAJ,QAAA,CAAAI,QAAA,CAAA4E,MAAA;IACA;IAEA;IACAC,kBAAA;MACAzD,OAAA,CAAAC,GAAA;MACAY,YAAA,CAAAC,UAAA;IACA;IAEA;IACAT,2BAAAqD,UAAA;MACA;QACA,MAAAC,eAAA,GAAA9C,YAAA,CAAA+C,OAAA;QACA,IAAAD,eAAA;UACA,MAAAE,UAAA,GAAA3D,IAAA,CAAA4D,KAAA,CAAAH,eAAA;UACA3D,OAAA,CAAAC,GAAA,+BAAAC,IAAA,CAAAC,SAAA,CAAA0D,UAAA;;UAEA;UACA,KAAArF,QAAA,CAAAC,UAAA,GAAAoF,UAAA,CAAApF,UAAA,SAAAD,QAAA,CAAAC,UAAA;UACA,KAAAD,QAAA,CAAAE,YAAA,GAAAmF,UAAA,CAAAnF,YAAA,SAAAF,QAAA,CAAAE,YAAA;UACA,KAAAF,QAAA,CAAAG,cAAA,GAAAkF,UAAA,CAAAlF,cAAA,SAAAH,QAAA,CAAAG,cAAA;UACA,KAAAH,QAAA,CAAAI,QAAA,GAAAiF,UAAA,CAAAjF,QAAA,SAAAJ,QAAA,CAAAI,QAAA;UACA,KAAAJ,QAAA,CAAAK,QAAA,GAAAgF,UAAA,CAAAhF,QAAA,SAAAL,QAAA,CAAAK,QAAA;;UAEA;UACA,IAAA6E,UAAA,KAAAG,UAAA,CAAApF,UAAA,IAAAoF,UAAA,CAAAnF,YAAA;YACA,KAAAqF,KAAA;cAAA,GAAAF;YAAA;UACA;UAEA;QACA;MACA,SAAA/B,KAAA;QACA9B,OAAA,CAAA8B,KAAA,iCAAAA,KAAA;MACA;MACA;IACA;IAEA;IACAf,mBAAA;MACA;QACA;QACA,MAAAiD,YAAA;QACA;UACA,MAAAC,KAAA,GAAApD,YAAA,CAAA+C,OAAA;UACA,IAAAK,KAAA;YACArG,MAAA,CAAAsG,MAAA,CAAAF,YAAA,EAAA9D,IAAA,CAAA4D,KAAA,CAAAG,KAAA;UACA;QACA,SAAAE,CAAA;UACAnE,OAAA,CAAA8B,KAAA,eAAAqC,CAAA;QACA;;QAEA;QACA,MAAAC,UAAA;UACA,GAAAJ,YAAA;UACA;UACAvF,UAAA,OAAAD,QAAA,CAAAC,UAAA,IAAAuF,YAAA,CAAAvF,UAAA;UACAC,YAAA,OAAAF,QAAA,CAAAE,YAAA,IAAAsF,YAAA,CAAAtF,YAAA;UACAC,cAAA,OAAAH,QAAA,CAAAG,cAAA;UACAC,QAAA,OAAAJ,QAAA,CAAAI,QAAA;UACAC,QAAA,OAAAL,QAAA,CAAAK,QAAA;QACA;QAEAgC,YAAA,CAAAwD,OAAA,uBAAAnE,IAAA,CAAAC,SAAA,CAAAiE,UAAA;QACApE,OAAA,CAAAC,GAAA,wCAAAC,IAAA,CAAAC,SAAA,CAAAiE,UAAA;QACA,OAAAA,UAAA;MACA,SAAAtC,KAAA;QACA9B,OAAA,CAAA8B,KAAA,+BAAAA,KAAA;QACA,YAAAtD,QAAA;MACA;IACA;IAEA;IACA4B,kBAAA;MACA;MACA,SAAA1C,UAAA;QACAsC,OAAA,CAAAC,GAAA,wCAAAC,IAAA,CAAAC,SAAA,MAAAzC,UAAA;;QAEA;QACA,SAAAA,UAAA,CAAAe,UAAA,OAAAD,QAAA,CAAAC,UAAA,QAAAf,UAAA,CAAAe,UAAA;QACA,SAAAf,UAAA,CAAAgB,YAAA,OAAAF,QAAA,CAAAE,YAAA,QAAAhB,UAAA,CAAAgB,YAAA;QACA,SAAAhB,UAAA,CAAAiB,cAAA,OAAAH,QAAA,CAAAG,cAAA,QAAAjB,UAAA,CAAAiB,cAAA;QACA,SAAAjB,UAAA,CAAAkB,QAAA,OAAAJ,QAAA,CAAAI,QAAA,QAAAlB,UAAA,CAAAkB,QAAA;QACA,SAAAlB,UAAA,CAAAmB,QAAA,OAAAL,QAAA,CAAAK,QAAA,QAAAnB,UAAA,CAAAmB,QAAA;;QAEA;QACA,SAAAnB,UAAA,CAAAe,UAAA,SAAAf,UAAA,CAAAgB,YAAA;UACA,KAAAqC,kBAAA;QACA;;QAEA;QACA,KAAAR,SAAA;UACA,KAAAC,YAAA;QACA;MACA;IACA;IAEA;IACA8D,qBAAArG,KAAA;MACA,KAAAA,KAAA;;MAEA;MACA,IAAAG,UAAA,GAAAH,KAAA,CAAAI,OAAA;;MAEA;MACAD,UAAA,GAAAA,UAAA,CAAAC,OAAA;;MAEA;MACAD,UAAA,GAAAA,UAAA,CAAAmG,SAAA;;MAEA;MACA,IAAAC,eAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAArG,UAAA,CAAA+E,MAAA,EAAAsB,CAAA;QACA,IAAAA,CAAA,QAAAA,CAAA;UACAD,eAAA;QACA;QACAA,eAAA,IAAApG,UAAA,CAAAoF,MAAA,CAAAiB,CAAA;MACA;;MAEA;MACA,KAAAjG,QAAA,CAAAG,cAAA,GAAA6F,eAAA;;MAEA;MACA,IAAApG,UAAA,CAAA+E,MAAA;QAAA;QACA,KAAAD,aAAA;MACA;QACA,KAAA1E,QAAA,CAAAI,QAAA;QACA,KAAAJ,QAAA,CAAAK,QAAA;MACA;IACA;IAEA;IACA6F,wBAAA;MACA,UAAAlG,QAAA,CAAAG,cAAA;QACA;MACA;MAEA,YAAAH,QAAA,CAAAG,cAAA;IACA;IAEA;IACAgG,SAAA;MACA;MACA,MAAAX,YAAA;MACA;QACA,MAAAC,KAAA,GAAApD,YAAA,CAAA+C,OAAA;QACA,IAAAK,KAAA;UACArG,MAAA,CAAAsG,MAAA,CAAAF,YAAA,EAAA9D,IAAA,CAAA4D,KAAA,CAAAG,KAAA;QACA;MACA,SAAAE,CAAA;;MAEA;MACA,MAAAS,QAAA;QACA,GAAAZ,YAAA;QAAA;QACA;QACAvF,UAAA,OAAAD,QAAA,CAAAC,UAAA,IAAAuF,YAAA,CAAAvF,UAAA;QACAC,YAAA,OAAAF,QAAA,CAAAE,YAAA,IAAAsF,YAAA,CAAAtF,YAAA;QACAC,cAAA,OAAAH,QAAA,CAAAG,cAAA;QACAC,QAAA,OAAAJ,QAAA,CAAAI,QAAA;QACAC,QAAA,OAAAL,QAAA,CAAAK,QAAA;MACA;;MAEA;MACAgC,YAAA,CAAAwD,OAAA,uBAAAnE,IAAA,CAAAC,SAAA,CAAAyE,QAAA;;MAEA;MACA,KAAAb,KAAA,uBAAAa,QAAA;;MAEA;MACA,KAAArE,SAAA;QACA;QACA,KAAAwD,KAAA;MACA;IACA;IAEA;IACAc,SAAA;MACA,KAAAlC,KAAA,CAAAnE,QAAA,CAAAsG,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,MAAAf,YAAA;UACA;YACA,MAAAC,KAAA,GAAApD,YAAA,CAAA+C,OAAA;YACA,IAAAK,KAAA;cACArG,MAAA,CAAAsG,MAAA,CAAAF,YAAA,EAAA9D,IAAA,CAAA4D,KAAA,CAAAG,KAAA;YACA;UACA,SAAAE,CAAA;;UAEA;UACA,MAAAS,QAAA;YACA,GAAAZ,YAAA;YAAA;YACA;YACAvF,UAAA,OAAAD,QAAA,CAAAC,UAAA,IAAAuF,YAAA,CAAAvF,UAAA;YACAC,YAAA,OAAAF,QAAA,CAAAE,YAAA,IAAAsF,YAAA,CAAAtF,YAAA;YACAsG,MAAA,EAAAhB,YAAA,CAAAgB,MAAA;YACAC,WAAA,EAAAjB,YAAA,CAAAiB,WAAA;YACAC,QAAA,EAAAlB,YAAA,CAAAkB,QAAA;YACAC,WAAA,EAAAnB,YAAA,CAAAmB,WAAA;YACAC,eAAA,EAAApB,YAAA,CAAAoB,eAAA;YACA;YACAzG,cAAA,OAAAH,QAAA,CAAAG,cAAA;YACAC,QAAA,OAAAJ,QAAA,CAAAI,QAAA;YACAC,QAAA,OAAAL,QAAA,CAAAK,QAAA;UACA;;UAEA;UACAgC,YAAA,CAAAwD,OAAA,uBAAAnE,IAAA,CAAAC,SAAA,CAAAyE,QAAA;;UAEA;UACA,KAAAb,KAAA,uBAAAa,QAAA;;UAEA;UACA,KAAArE,SAAA;YACA;YACA,KAAAwD,KAAA;UACA;QACA;UACA,KAAAlC,QAAA,CAAAoB,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}