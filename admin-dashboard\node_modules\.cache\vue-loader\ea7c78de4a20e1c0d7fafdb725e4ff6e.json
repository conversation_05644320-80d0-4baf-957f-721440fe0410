{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractSteps.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractSteps.vue", "mtime": 1753348142493}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractSteps.vue"], "names": [], "mappings": ";AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ContractSteps.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\n  <div class=\"contract-steps-container\">\n    <div class=\"steps-header\">\n      <h2>合同生成流程</h2>\n      <el-steps :active=\"activeStep\" finish-status=\"success\" align-center>\n        <el-step title=\"工人身份信息\" description=\"识别身份证信息\"></el-step>\n        <el-step title=\"银行卡信息\" description=\"输入银行卡号\"></el-step>\n        <el-step title=\"合同信息\" description=\"填写合同表单\"></el-step>\n        <el-step title=\"合同预览\" description=\"预览并打印\"></el-step>\n      </el-steps>\n    </div>\n    \n    <div class=\"steps-content\">\n      <router-view \n        @next-step=\"nextStep\" \n        @prev-step=\"prevStep\" \n        :worker-info=\"workerInfo\" \n        @update-worker-info=\"updateWorkerInfo\">\n      </router-view>\n    </div>\n    \n    <div class=\"steps-action\">\n      <el-button @click=\"prevStep\" :disabled=\"activeStep === 1\">上一步</el-button>\n      <el-button type=\"primary\" @click=\"nextStep\" :disabled=\"activeStep === 4\">下一步</el-button>\n      <el-button @click=\"goBack\">返回花名册</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ContractSteps',\n  data() {\n    return {\n      activeStep: 1,\n      workerInfo: {\n        // 工人基本信息\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        // 银行卡信息\n        bankCardNumber: '',\n        bankName: '',\n        // 工作信息\n        teamName: '',\n        teamCode: '',\n        projectCode: '',\n        projectName: '',\n        participantCode: '',\n        participantName: '',\n        jobPosition: '',\n        jobDescription: '',\n        // 合同信息\n        contractType: 0,\n        fixedStartDate: '',\n        fixedEndDate: '',\n        projectStartDate: '',\n        taskStartDate: '',\n        taskEndDate: '',\n        otherTask: '',\n        workLocation: '',\n        salaryType: 1,\n        fixedSalary: 0,\n        timeUnit: '日',\n        timeUnitSalary: 0,\n        pieceUnit: '平方',\n        pieceUnitSalary: 0,\n        otherSalaryForm: '',\n        salaryDay: 15,\n        benefits: [],\n        otherMatters: '',\n        signDate: '',\n        // 公司信息\n        companyName: '',\n        legalRepresentative: '',\n        agent: '',\n        companyAddress: '',\n        // 模板信息\n        templateId: '',\n        templateVariables: []\n      }\n    }\n  },\n  created() {\n    // 检查是否是从合同流程外部进入\n    const fromExternal = !document.referrer.includes('/contract/contract-steps/')\n\n    // 尝试从localStorage恢复数据\n    const dataRestored = this.restoreFromLocalStorage()\n\n    // 只有从外部进入且没有恢复到数据时才清空\n    if (fromExternal && !dataRestored) {\n      console.log('ContractSteps - 从外部进入合同流程，且没有已保存的数据，初始化空数据')\n      this.clearContractData()\n    } else {\n      console.log('ContractSteps - 使用已存在的表单数据')\n    }\n    \n    // 从URL获取项目编码\n    const { projectCode } = this.$route.query\n    if (projectCode) {\n      this.workerInfo.projectCode = projectCode\n      // 可以在这里加载项目信息\n      this.loadProjectInfo(projectCode)\n    }\n    \n    // 根据当前路由设置激活步骤\n    this.setActiveStepFromRoute()\n    \n    console.log('ContractSteps created - 初始化后的数据:', JSON.stringify(this.workerInfo))\n  },\n  watch: {\n    // 监听路由变化，更新激活的步骤\n    $route(to, from) {\n      console.log(`路由变化: 从 ${from.path} 到 ${to.path}`)\n      \n      // 只有在合同流程内部导航时才保留数据\n      const isWithinContractFlow = to.path.includes('/contract/contract-steps/') && from.path.includes('/contract/contract-steps/')\n\n      if (isWithinContractFlow) {\n        console.log('ContractSteps - 在合同流程内部导航，保留数据')\n        this.setActiveStepFromRoute()\n        this.ensureDataConsistency()\n      } else if (to.path.includes('/contract/contract-steps/')) {\n        console.log('ContractSteps - 从外部进入合同流程，设置步骤')\n      this.setActiveStepFromRoute()\n      }\n    }\n  },\n  methods: {\n    // 确保数据一致性\n    ensureDataConsistency() {\n      // 从localStorage恢复数据，确保数据是最新的\n      this.restoreFromLocalStorage()\n      \n      // 添加调试日志\n      console.log('ContractSteps - 确保数据一致性后的数据:', JSON.stringify(this.workerInfo))\n    },\n    \n    // 从localStorage恢复数据\n    restoreFromLocalStorage() {\n      try {\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\n        if (savedWorkerInfo) {\n          const parsedData = JSON.parse(savedWorkerInfo)\n          console.log('ContractSteps - 从localStorage恢复数据:', JSON.stringify(parsedData))\n          \n          // 更新工人信息，保留现有非空值\n          for (const key in parsedData) {\n            if (parsedData[key] !== null && parsedData[key] !== undefined && parsedData[key] !== '') {\n              this.workerInfo[key] = parsedData[key]\n            }\n          }\n          \n          console.log('ContractSteps - 恢复后的数据:', JSON.stringify(this.workerInfo))\n          return true\n        }\n      } catch (error) {\n        console.error('ContractSteps - 从localStorage恢复数据失败:', error)\n      }\n      return false\n    },\n    \n    // 导航到指定步骤\n    navigateToStep(step) {\n      // 设置活动步骤\n      this.activeStep = step\n      \n      // 使用 $nextTick 确保视图已更新\n      this.$nextTick(() => {\n        console.log(`尝试导航到步骤 ${step}`)\n        \n        // 构造目标路径\n        const path = `/contract/contract-steps/step${step}`\n        const query = { projectCode: this.workerInfo.projectCode || '' }\n        \n        // 检查当前路由是否已经是目标路由\n        if (this.$route.path === path) {\n          console.log(`已在路径 ${path}，使用 replace 而非 push`)\n          // 如果已经在相同路径，使用 replace 而非 push\n          this.$router.replace({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航替换被忽略:', err.message)\n          })\n        } else {\n          // 否则使用 push 导航\n          this.$router.push({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航推送被忽略:', err.message)\n          })\n        }\n      })\n    },\n    \n    // 根据路由设置激活步骤\n    setActiveStepFromRoute() {\n      const path = this.$route.path\n      if (path.includes('step1')) {\n        this.activeStep = 1\n      } else if (path.includes('step2')) {\n        this.activeStep = 2\n      } else if (path.includes('step3')) {\n        this.activeStep = 3\n      } else if (path.includes('step4')) {\n        this.activeStep = 4\n      }\n    },\n    \n    // 加载项目信息\n    loadProjectInfo(projectCode) {\n      // 这里可以调用API获取项目信息\n      // 示例代码，实际需要替换为真实API调用\n      console.log('加载项目信息:', projectCode)\n      // 可以设置公司信息的默认值\n      this.workerInfo.companyName = ''\n      this.workerInfo.legalRepresentative = ''\n      this.workerInfo.agent = ''\n      this.workerInfo.companyAddress = ''\n    },\n    \n    // 清空合同数据\n    clearContractData() {\n      console.log('ContractSteps - 清空所有合同相关数据')\n      \n      // 重置工人信息对象到初始状态\n      this.workerInfo = {\n        // 工人基本信息\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        // 银行卡信息\n        bankCardNumber: '',\n        bankName: '',\n        // 工作信息\n        teamName: '',\n        teamCode: '',\n        projectCode: this.$route.query.projectCode || '',\n        projectName: '',\n        participantCode: '',\n        participantName: '',\n        jobPosition: '',\n        jobDescription: '',\n        // 合同信息\n        contractType: 0,\n        fixedStartDate: '',\n        fixedEndDate: '',\n        projectStartDate: '',\n        taskStartDate: '',\n        taskEndDate: '',\n        otherTask: '',\n        workLocation: '',\n        salaryType: 1,\n        fixedSalary: 0,\n        timeUnit: '日',\n        timeUnitSalary: 0,\n        pieceUnit: '平方',\n        pieceUnitSalary: 0,\n        otherSalaryForm: '',\n        salaryDay: 15,\n        benefits: [],\n        otherMatters: '',\n        signDate: '',\n        // 公司信息\n        companyName: '',\n        legalRepresentative: '',\n        agent: '',\n        companyAddress: '',\n        // 模板信息\n        templateId: '',\n        templateVariables: []\n      }\n      \n      // 清空localStorage中的数据\n      localStorage.removeItem('contractWorkerInfo')\n      \n      // 如果有公司默认信息，重新加载\n      if (this.$route.query.projectCode) {\n        this.loadProjectInfo(this.$route.query.projectCode)\n      }\n    },\n    \n    // 下一步\n    nextStep() {\n      console.log('=== ContractSteps nextStep 开始 ===')\n      console.log('ContractSteps nextStep - 当前步骤:', this.activeStep)\n      console.log('ContractSteps nextStep - 当前workerInfo:', JSON.stringify(this.workerInfo))\n\n      if (this.activeStep < 4) {\n        const nextStep = this.activeStep + 1\n        console.log('ContractSteps nextStep - 将导航到步骤:', nextStep)\n\n        // 先确保数据已从localStorage同步\n        this.restoreFromLocalStorage()\n        console.log('ContractSteps nextStep - 从localStorage恢复后的数据:', JSON.stringify(this.workerInfo))\n\n        // 强制更新后再导航\n        this.$nextTick(() => {\n          // 确保激活步骤已更新\n          this.activeStep = nextStep\n\n          // 导航到下一步\n          this.navigateToStep(nextStep)\n\n          // 在导航后再次确保数据一致\n          this.$nextTick(() => {\n            this.ensureDataConsistency()\n            console.log('ContractSteps nextStep - 导航完成，最终数据:', JSON.stringify(this.workerInfo))\n            console.log('=== ContractSteps nextStep 结束 ===')\n          })\n        })\n      }\n    },\n    \n    // 上一步\n    prevStep(params) {\n      console.log('父组件: 触发上一步, 当前步骤:', this.activeStep, '参数:', params)\n      if (this.activeStep > 1) {\n        const prevStep = this.activeStep - 1\n        console.log('父组件: 将导航到步骤:', prevStep)\n        \n        // 先确保数据已从localStorage同步\n        this.restoreFromLocalStorage()\n        \n        // 强制更新后再导航\n        this.$nextTick(() => {\n          // 确保激活步骤已更新\n          this.activeStep = prevStep\n          \n          // 导航到上一步，传递query参数\n          const query = { \n            projectCode: this.workerInfo.projectCode || '',\n            ...(params || {}) // 合并传入的参数\n          }\n          \n          // 构造目标路径\n          const path = `/contract/contract-steps/step${prevStep}`\n          \n          // 导航到上一步\n          this.$router.push({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航推送被忽略:', err.message)\n          })\n          \n          // 在导航后再次确保数据一致\n          this.$nextTick(() => {\n            this.ensureDataConsistency()\n          })\n        })\n      }\n    },\n    \n    // 更新工人信息\n    updateWorkerInfo(info) {\n      if (!info) return;\n\n      console.log('=== ContractSteps updateWorkerInfo 开始 ===')\n      console.log('ContractSteps 接收到更新请求:', JSON.stringify(info))\n      console.log('ContractSteps 当前workerInfo:', JSON.stringify(this.workerInfo))\n\n      // 保存更新前的数据\n      const beforeUpdate = JSON.stringify(this.workerInfo)\n\n      // 防止空值覆盖已有的有效值\n      for (const key in info) {\n        // 只有当新值不为空或未定义时才更新\n        if (info[key] !== null && info[key] !== undefined && info[key] !== '') {\n          this.workerInfo[key] = info[key]\n        }\n      }\n\n      // 直接确保关键字段已正确更新\n      if (info.workerName) this.workerInfo.workerName = info.workerName;\n      if (info.idCardNumber) this.workerInfo.idCardNumber = info.idCardNumber;\n      if (info.bankCardNumber) this.workerInfo.bankCardNumber = info.bankCardNumber;\n\n      // 打印详细的数据对比\n      console.log('ContractSteps 数据更新结果:')\n      console.log('- 更新前:', beforeUpdate)\n      console.log('- 更新后:', JSON.stringify(this.workerInfo))\n      console.log('- 更新的字段:', Object.keys(info).join(', '))\n      console.log('- 关键字段检查:')\n      console.log('  * workerName:', this.workerInfo.workerName)\n      console.log('  * idCardNumber:', this.workerInfo.idCardNumber)\n      \n      // 确保localStorage也被更新\n      try {\n        // 从localStorage获取当前数据\n        const savedData = localStorage.getItem('contractWorkerInfo') || '{}'\n        const parsedData = JSON.parse(savedData)\n        \n        // 合并现有数据和更新的数据\n        const newData = { ...parsedData }\n        \n        // 只更新有值的字段\n        for (const key in info) {\n          if (info[key] !== null && info[key] !== undefined && info[key] !== '') {\n            newData[key] = info[key]\n          }\n        }\n        \n        // 保存回localStorage\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(newData))\n        console.log('已更新localStorage数据:', JSON.stringify(newData))\n        \n        // 再次确保this.workerInfo包含所有localStorage中的数据\n        for (const key in newData) {\n          if (newData[key] !== null && newData[key] !== undefined && newData[key] !== '' &&\n              (!this.workerInfo[key] || this.workerInfo[key] === '')) {\n            this.workerInfo[key] = newData[key]\n          }\n        }\n      } catch (e) {\n        console.error('更新localStorage失败:', e)\n      }\n      \n      // 强制子组件更新\n      this.$nextTick(() => {\n        if (this.$children && this.$children.length) {\n          this.$children.forEach(child => {\n            if (typeof child.initFormFromProps === 'function') {\n              console.log('强制更新子组件数据')\n              child.initFormFromProps()\n            }\n          })\n        }\n      })\n    },\n    \n    // 返回花名册\n    goBack() {\n      // 清空合同数据\n      this.clearContractData()\n      console.log('ContractSteps - 返回花名册，清空所有数据')\n      \n      this.$router.push({ name: 'RosterList' }).catch(err => {\n        // 忽略重复导航错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.contract-steps-container {\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.steps-header {\n  margin-bottom: 30px;\n}\n\n.steps-header h2 {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #303133;\n}\n\n.steps-content {\n  margin: 30px 0;\n  min-height: 400px;\n  padding: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background-color: #f9fafc;\n}\n\n.steps-action {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style> "]}]}