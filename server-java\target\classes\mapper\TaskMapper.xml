<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daka.pro.mapper.TaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.daka.pro.domain.Task">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="project_id" property="projectId" />
        <result column="project" property="project" />
        <result column="basic_task_id" property="basicTaskId" />
        <result column="basic_task_name" property="basicTaskName" />
        <result column="cron" property="cron" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="last_run_time" property="lastRunTime" />
        <result column="last_run_result" property="lastRunResult" />
        <result column="next_run_time" property="nextRunTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="handler_class" property="handlerClass" />
        <result column="params" property="params" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, project_id, project, basic_task_id, basic_task_name, cron, description, status, 
        last_run_time, last_run_result, next_run_time, create_time, update_time,
        handler_class, params
    </sql>
    
    <!-- 带表别名的查询结果列 -->
    <sql id="Base_Column_List_With_Alias">
        t.id, t.name, t.project_id, t.project, t.basic_task_id, t.basic_task_name, t.cron, t.description, t.status, 
        t.last_run_time, t.last_run_result, t.next_run_time, t.create_time, t.update_time,
        t.handler_class, t.params
    </sql>

    <!-- 分页查询任务列表 -->
    <select id="getTaskList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_With_Alias" />
        FROM sys_task t
        LEFT JOIN project p ON t.project_id = p.id
        <where>
            <if test="req.name != null and req.name != ''">
                AND t.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.projectId != null and req.projectId != ''">
                AND t.project_id = #{req.projectId}
            </if>
            <if test="req.basicTaskId != null and req.basicTaskId != ''">
                AND t.basic_task_id = #{req.basicTaskId}
            </if>
            <if test="req.status != null">
                AND t.status = #{req.status}
            </if>
            <if test="req.userId != null and req.userId != ''">
                AND p.user_id = #{req.userId}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

    <!-- 获取所有启用状态的任务 -->
    <select id="getAllEnabledTasks" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sys_task
        WHERE status = 1
    </select>

    <!-- 获取项目的定时任务名称列表 -->
    <select id="getTaskNamesByProjectId" resultType="java.lang.String">
        SELECT name
        FROM sys_task
        WHERE project_id = #{projectId}
        AND status = 1
        ORDER BY create_time
    </select>

    <!-- 获取项目的基础任务名称列表 -->
    <select id="getBasicTaskNamesByProjectId" resultType="java.lang.String">
        SELECT basic_task_name
        FROM sys_task
        WHERE project_id = #{projectId}
        AND status = 1
        GROUP BY basic_task_name
        ORDER BY MIN(create_time)
    </select>

</mapper> 