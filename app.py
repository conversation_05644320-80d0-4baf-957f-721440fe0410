import os
import base64
import io
import json
import re
import time
from flask import Flask, request, jsonify
from flask_cors import CORS
import cv2
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR

app = Flask(__name__)
CORS(app)

# 优化的PaddleOCR配置 - 针对身份证识别优化

# CPU优化配置 - 专门针对无GPU环境的身份证识别优化
def get_optimal_cpu_threads():
    cpu_count = os.cpu_count() or 4
    # 对于CPU环境下的身份证识别，4个线程通常是最优的
    return min(4, max(2, cpu_count // 2))

optimal_threads = get_optimal_cpu_threads()
print(f"OCR配置: CPU模式, 线程数={optimal_threads}")

# 针对CPU环境优化的PaddleOCR配置
ocr = PaddleOCR(
    use_angle_cls=False,  # 身份证通常方向正确，关闭角度分类以提速
    lang='ch',
    use_gpu=False,  # 明确使用CPU
    show_log=False,
    use_mp=False,  # 避免多进程开销
    total_process_num=1,
    cpu_threads=optimal_threads,
    enable_mkldnn=True,  # 启用Intel MKL-DNN加速
    # 针对身份证优化的参数
    det_limit_side_len=640,  # 降低到640px以提高速度
    det_db_thresh=0.3,       # 适中的检测阈值
    det_db_box_thresh=0.5,   # 适中的框选阈值
    det_db_unclip_ratio=1.6, # 稍微扩展文本框
    rec_batch_num=1,         # CPU环境下使用单批次处理
    # 使用轻量级模型
    det_model_dir=None,      # 使用默认轻量级检测模型
    rec_model_dir=None,      # 使用默认轻量级识别模型
    cls_model_dir=None       # 不使用分类模型
)

# 模型预热 - 避免首次调用的冷启动延迟
def warm_up_ocr():
    """预热OCR模型，减少首次识别延迟"""
    try:
        print("正在预热OCR模型...")
        # 创建一个小的测试图像
        import numpy as np
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255

        # 进行一次快速识别以预热模型
        _ = ocr.ocr(test_image, cls=False, det=True, rec=True)
        print("OCR模型预热完成")
    except Exception as e:
        print(f"OCR模型预热失败: {str(e)}")

# 启动时预热模型
warm_up_ocr()

def apply_lossless_scaling(image, max_dimension=1200):
    """
    对图像进行无损缩放，专门用于OCR识别
    只缩放过大的图像，保持图像质量
    """
    try:
        height, width = image.shape[:2]

        # 计算是否需要缩放
        max_current = max(width, height)

        if max_current > max_dimension:
            # 计算缩放比例，保持宽高比
            scale = max_dimension / max_current
            new_width = int(width * scale)
            new_height = int(height * scale)

            # 使用高质量的插值方法进行无损缩放
            scaled_image = cv2.resize(
                image,
                (new_width, new_height),
                interpolation=cv2.INTER_LANCZOS4  # 高质量插值
            )

            print(f"无损缩放: {width}x{height} -> {new_width}x{new_height} (比例: {scale:.2f})")
            return scaled_image, True, scale
        else:
            print(f"图像尺寸合适，无需缩放: {width}x{height}")
            return image, False, 1.0

    except Exception as e:
        print(f"无损缩放失败: {str(e)}")
        return image, False, 1.0

def preprocess_image_for_id_card(image):
    """
    专门针对身份证识别的图像预处理优化
    """
    try:
        height, width = image.shape[:2]

        # 身份证识别的最优尺寸是800-1200px宽度
        target_width = 1000

        # 如果图像过大，缩放到合适尺寸
        if width > target_width * 1.5 or height > target_width:
            scale = min(target_width / width, target_width / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"身份证图像缩放: {width}x{height} -> {new_width}x{new_height}")

        # 如果图像过小，适度放大
        elif width < 400:
            scale = 600 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            print(f"身份证图像放大: {width}x{height} -> {new_width}x{new_height}")

        return image
    except Exception as e:
        print(f"身份证图像预处理失败: {str(e)}")
        return image

def process_image_with_scaling(image, max_dimension=1200):
    """
    处理图像并返回OCR结果 - 包含无损缩放功能
    """
    try:
        # 步骤1: 对图像进行无损缩放（仅用于OCR识别）
        scaled_image, was_scaled, scale_factor = apply_lossless_scaling(image, max_dimension)

        # 步骤2: 身份证专用图像预处理
        processed_image = preprocess_image_for_id_card(scaled_image)

        # 步骤3: 针对身份证优化的OCR识别参数
        result = ocr.ocr(
            processed_image,
            cls=False,  # 身份证通常不需要角度分类
            det=True,   # 启用文本检测
            rec=True    # 启用文本识别
        )

        # 格式化结果 - v4版本结果格式保持兼容
        ocr_results = []
        if result and len(result) > 0 and result[0]:
            for line in result[0]:
                if line and len(line) >= 2:
                    # line[0] 是坐标点，line[1] 是(文本, 置信度)
                    text = line[1][0] if len(line[1]) > 0 else ""  # 识别的文本
                    confidence = float(line[1][1]) if len(line[1]) > 1 else 0.0  # 置信度
                    coordinates = line[0]  # 坐标点

                    ocr_results.append({
                        'text': text,
                        'confidence': confidence,
                        'coordinates': coordinates
                    })
        
        # 打印识别结果 - v4版本增强日志
        print("=" * 50)
        print("PaddleOCR v4 识别结果:")
        print(f"识别到 {len(ocr_results)} 个文本区域")
        for i, item in enumerate(ocr_results):
            print(f"区域{i+1}: {item['text']}, 置信度: {item['confidence']:.4f}")
        print("总文本内容:")
        print('\n'.join([item['text'] for item in ocr_results]))
        print("=" * 50)
        
        # 快速解析身份证信息
        id_card_data = parse_id_card_info_fast(ocr_results)

        return {
            'success': True,
            'data': id_card_data,
            'raw_results': ocr_results,
            'total_text': '\n'.join([item['text'] for item in ocr_results]),
            'version': 'PaddleOCR v4 (Lossless Scaling)',  # 版本标识
            'text_regions_count': len(ocr_results),
            'image_processing': {
                'lossless_scaling_applied': was_scaled,
                'scale_factor': scale_factor if was_scaled else 1.0,
                'original_size': f"{image.shape[1]}x{image.shape[0]}",
                'processed_size': f"{processed_image.shape[1]}x{processed_image.shape[0]}"
            }
        }
    except Exception as e:
        print(f"PaddleOCR v4 处理出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'version': 'PaddleOCR v4'
        }

def process_image_without_scaling(image):
    """
    处理图像并返回OCR结果 - 不使用无损缩放
    """
    try:
        # 直接使用身份证专用图像预处理
        processed_image = preprocess_image_for_id_card(image)

        # 针对身份证优化的OCR识别参数
        result = ocr.ocr(
            processed_image,
            cls=False,  # 身份证通常不需要角度分类
            det=True,   # 启用文本检测
            rec=True    # 启用文本识别
        )

        # 格式化结果
        ocr_results = []
        if result and len(result) > 0 and result[0]:
            for line in result[0]:
                if line and len(line) >= 2:
                    text = line[1][0] if len(line[1]) > 0 else ""
                    confidence = float(line[1][1]) if len(line[1]) > 1 else 0.0
                    coordinates = line[0]

                    ocr_results.append({
                        'text': text,
                        'confidence': confidence,
                        'coordinates': coordinates
                    })

        # 打印识别结果
        print("=" * 50)
        print("PaddleOCR 识别结果 (无缩放):")
        print(f"识别到 {len(ocr_results)} 个文本区域")
        for i, item in enumerate(ocr_results):
            print(f"区域{i+1}: {item['text']}, 置信度: {item['confidence']:.4f}")
        print("总文本内容:")
        print('\n'.join([item['text'] for item in ocr_results]))
        print("=" * 50)

        # 快速解析身份证信息
        id_card_data = parse_id_card_info_fast(ocr_results)

        return {
            'success': True,
            'data': id_card_data,
            'raw_results': ocr_results,
            'total_text': '\n'.join([item['text'] for item in ocr_results]),
            'version': 'PaddleOCR v4 (No Scaling)',
            'text_regions_count': len(ocr_results),
            'image_processing': {
                'lossless_scaling_applied': False,
                'scale_factor': 1.0,
                'original_size': f"{image.shape[1]}x{image.shape[0]}",
                'processed_size': f"{processed_image.shape[1]}x{processed_image.shape[0]}"
            }
        }
    except Exception as e:
        print(f"PaddleOCR 处理出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'version': 'PaddleOCR v4'
        }

def fix_address_ocr_errors(address_text):
    """
    修复地址中的常见OCR识别错误
    """
    if not address_text:
        return address_text

    # 常见的OCR识别错误修复映射
    error_fixes = {
        # 四川省什邡市相关错误 - 重点修复
        '四川省什市': '四川省什邡市',
        '四川什邡市': '四川省什邡市',
        '川省什邡市': '四川省什邡市',
        '四川省什邡': '四川省什邡市',
        '四川什市': '四川省什邡市',
        '四川省什': '四川省什邡市',
        '四川什': '四川省什邡市',
        '什市': '什邡市',
        '什邡': '什邡市',

        # 马井镇相关错误
        '马井镇': '马井镇',
        '马井': '马井镇',
        '马井锁': '马井镇',
        '马井镇': '马井镇',

        # 双石桥村相关错误
        '双石桥村': '双石桥村',
        '双石桥': '双石桥村',
        '双石村': '双石桥村',
        '双石桥村': '双石桥村',

        # 其他常见地名错误
        '成都巿': '成都市',
        '绵阳巿': '绵阳市',
        '德阳巿': '德阳市',
        '广汉巿': '广汉市',

        # 数字和符号错误
        '3组': '3组',
        '88号': '88号',
        '8号': '88号',  # 可能的识别错误

        # 关键词清理
        '住址': '',
        '地址': '',
        '佳址': '',  # 常见OCR错误
    }

    corrected_text = address_text

    # 应用错误修复
    for wrong, correct in error_fixes.items():
        if wrong in corrected_text:
            corrected_text = corrected_text.replace(wrong, correct)

    # 特殊处理：什邡地区地址修复
    if '四川省什' in corrected_text and '邡' not in corrected_text:
        corrected_text = corrected_text.replace('四川省什', '四川省什邡')
        print(f"自动添加'邡'字: 四川省什 -> 四川省什邡")

    # 确保什邡后面有"市"
    if '什邡' in corrected_text and '什邡市' not in corrected_text:
        corrected_text = corrected_text.replace('什邡', '什邡市')
        print(f"自动添加'市'字: 什邡 -> 什邡市")

    # 处理可能的地址顺序问题
    if '四川' in corrected_text and '省' not in corrected_text:
        corrected_text = corrected_text.replace('四川', '四川省')

    # 清理多余的空格和重复内容
    corrected_text = re.sub(r'\s+', '', corrected_text)

    # 移除重复的地名
    if corrected_text.count('四川省') > 1:
        corrected_text = corrected_text.replace('四川省四川省', '四川省')
    if corrected_text.count('什邡市') > 1:
        corrected_text = corrected_text.replace('什邡市什邡市', '什邡市')

    return corrected_text

def parse_id_card_info_fast(ocr_results):
    """
    快速解析身份证信息 - 专注于证件号码和地址
    """
    id_card_data = {
        'name': None,
        'gender': None,
        'id_number': None,
        'address': None
    }

    # 合并所有识别文本
    all_text = ''.join([item['text'] for item in ocr_results])
    print(f"身份证文本: {all_text}")

    # 1. 快速提取身份证号码 - 18位数字或17位数字+X
    id_pattern = r'[1-9]\d{5}(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dXx]'
    id_matches = re.findall(id_pattern, all_text)
    if id_matches:
        id_card_data['id_number'] = id_matches[0].upper()
        print(f"提取身份证号: {id_card_data['id_number']}")

    # 2. 智能提取地址 - 处理分段地址信息
    address_parts = []
    address_indicators = ['省', '市', '县', '区', '镇', '村', '路', '街', '号', '组']

    # 首先尝试从合并文本中直接提取完整地址
    # 查找"住址"关键词后的内容
    if '住址' in all_text:
        address_start = all_text.find('住址') + 2
        # 查找地址结束位置（通常是"公民身份号码"之前）
        address_end = all_text.find('公民身份号码')
        if address_end == -1:
            address_end = len(all_text)

        potential_address = all_text[address_start:address_end].strip()
        if potential_address and len(potential_address) > 5:
            address_parts.append(potential_address)
            print(f"从住址关键词提取: {potential_address}")

    # 如果没有找到完整地址，则分段收集
    if not address_parts:
        for item in ocr_results:
            text = item['text'].strip()
            # 跳过明显不是地址的内容
            if (len(text) < 3 or
                '身份' in text or '号码' in text or '姓名' in text or
                '性别' in text or '民族' in text or '出生' in text or
                '公民' in text):
                continue

            # 如果包含地址标识符，很可能是地址
            if any(indicator in text for indicator in address_indicators):
                address_parts.append(text)
                print(f"收集地址片段: {text}")

    # 合并地址片段
    address_text = ''.join(address_parts)

    if address_text:
        # 应用地址错误修复
        corrected_address = fix_address_ocr_errors(address_text)
        id_card_data['address'] = corrected_address
        if corrected_address != address_text:
            print(f"原始地址: {address_text}")
            print(f"修复后地址: {corrected_address}")
        else:
            print(f"提取地址: {address_text}")

    # 3. 简单提取姓名和性别（可选）
    for item in ocr_results:
        text = item['text'].strip()

        # 查找姓名
        if '姓名' in text and not id_card_data['name']:
            name_parts = text.split('姓名')
            if len(name_parts) > 1:
                id_card_data['name'] = name_parts[1].strip()
                print(f"提取姓名: {id_card_data['name']}")

        # 查找性别
        if '男' in text and not id_card_data['gender']:
            id_card_data['gender'] = '男'
            print("提取性别: 男")
        elif '女' in text and not id_card_data['gender']:
            id_card_data['gender'] = '女'
            print("提取性别: 女")

    return id_card_data

def base64_to_image(base64_string):
    """
    将base64字符串转换为OpenCV图像
    """
    try:
        # 移除base64前缀（如果存在）
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码base64
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为OpenCV格式
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        return image_cv
    except Exception as e:
        raise ValueError(f"Invalid base64 image: {str(e)}")

@app.route('/ocr', methods=['POST'])
def ocr_endpoint():
    """
    OCR识别端点 - 支持无损缩放
    支持多种输入方式：
    1. base64编码的图像 (JSON格式)
    2. base64编码的图像 (表单数据)
    3. 文件上传

    新增参数：
    - enable_lossless_scaling: 是否启用无损缩放 (默认: true)
    - max_dimension: 最大尺寸限制 (默认: 1200px)

    JSON示例:
    {
        "image": "base64_string",
        "enable_lossless_scaling": true,
        "max_dimension": 1200
    }
    """
    try:
        print("\n接收到OCR请求...")
        print(f"请求内容类型: {request.content_type}")
        print(f"请求方法: {request.method}")
        
        image_cv = None
        
        # 检查是否为JSON格式
        if request.is_json:
            print("处理JSON请求...")
            data = request.get_json()
            
            if 'image' in data:
                base64_string = data['image']
                print(f"从JSON提取base64图像数据，长度: {len(base64_string)}")
                image_cv = base64_to_image(base64_string)
            else:
                print("错误: JSON数据中没有'image'字段")
                return jsonify({
                    'success': False,
                    'error': 'No image field in JSON data'
                }), 400
        
        # 检查是否为表单数据
        elif request.form or request.files:
            print("处理表单数据请求...")
            
            # 检查表单中的base64数据
            if 'image_base64' in request.form:
                base64_string = request.form['image_base64']
                print(f"从表单获取base64图像数据，长度: {len(base64_string)}")
                image_cv = base64_to_image(base64_string)
            
            # 检查表单中的图片文件
            elif 'image' in request.files:
                file = request.files['image']
                if file.filename == '':
                    print("错误: 没有选择文件")
                    return jsonify({
                        'success': False,
                        'error': 'No file selected'
                    }), 400
                
                print(f"处理上传的文件: {file.filename}")
                file_data = file.read()
                image = Image.open(io.BytesIO(file_data))
                image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 检查是否有文件路径
            elif 'image_path' in request.form:
                image_path = request.form['image_path']
                print(f"从路径加载图像: {image_path}")
                
                if os.path.exists(image_path):
                    image_cv = cv2.imread(image_path)
                else:
                    print(f"错误: 文件路径不存在: {image_path}")
                    return jsonify({
                        'success': False,
                        'error': f'File not found: {image_path}'
                    }), 404
            
            else:
                print("错误: 表单数据中没有图像数据")
                return jsonify({
                    'success': False,
                    'error': 'No image data in form'
                }), 400
        
        # 如果都不是，尝试读取原始数据
        else:
            print("尝试从请求体读取原始数据...")
            raw_data = request.get_data()
            
            if raw_data:
                try:
                    # 尝试解析为JSON
                    data = json.loads(raw_data)
                    if 'image' in data:
                        base64_string = data['image']
                        print(f"从原始数据提取base64图像，长度: {len(base64_string)}")
                        image_cv = base64_to_image(base64_string)
                    else:
                        print("错误: JSON数据中没有'image'字段")
                        return jsonify({
                            'success': False,
                            'error': 'No image field in raw JSON data'
                        }), 400
                except json.JSONDecodeError:
                    # 不是JSON，可能是直接的base64数据
                    try:
                        print("尝试将整个请求体作为base64处理...")
                        image_cv = base64_to_image(raw_data.decode('utf-8'))
                    except Exception as e:
                        print(f"无法解析请求体为base64图像: {str(e)}")
                        return jsonify({
                            'success': False,
                            'error': 'Could not parse request body as base64 image'
                        }), 400
            else:
                print("错误: 请求体为空")
                return jsonify({
                    'success': False,
                    'error': 'Empty request body'
                }), 400
        
        # 如果没有获取到图像数据
        if image_cv is None:
            print("错误: 无法获取图像数据")
            return jsonify({
                'success': False,
                'error': 'No valid image data provided'
            }), 400
        
        # 获取缩放参数（可选）
        enable_scaling = True  # 默认启用无损缩放
        max_dimension = 1200   # 默认最大尺寸

        # 从请求中获取缩放参数
        if request.is_json:
            data = request.get_json()
            enable_scaling = data.get('enable_lossless_scaling', True)
            max_dimension = data.get('max_dimension', 1200)
        elif request.form:
            enable_scaling = request.form.get('enable_lossless_scaling', 'true').lower() == 'true'
            max_dimension = int(request.form.get('max_dimension', 1200))

        print(f"无损缩放设置: 启用={enable_scaling}, 最大尺寸={max_dimension}")

        # 处理图像
        print("开始OCR识别...")
        if enable_scaling:
            result = process_image_with_scaling(image_cv, max_dimension)
        else:
            result = process_image_without_scaling(image_cv)
        print("OCR识别完成，返回结果")
        return jsonify(result)
        
    except Exception as e:
        print(f"OCR处理异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查端点 - 优化版本
    """
    print("收到健康检查请求")
    try:
        # 快速健康检查
        import numpy as np
        start_time = time.time()
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        _ = ocr.ocr(test_image, cls=False, det=True, rec=True)
        check_time = time.time() - start_time

        return jsonify({
            'status': 'healthy',
            'service': 'Optimized PaddleOCR API for ID Cards',
            'version': 'PaddleOCR v4 (CPU Optimized)',
            'ocr_engine': 'functional',
            'check_time': f"{check_time:.2f}s",
            'optimizations': [
                'CPU-optimized configuration',
                'ID card specific preprocessing',
                'Fast text extraction',
                'Model prewarming',
                'Simplified parsing'
            ],
            'features': [
                'ID Number Extraction',
                'Address Extraction',
                'Fast Processing',
                'High Accuracy'
            ]
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'service': 'Optimized PaddleOCR API',
            'error': str(e)
        }), 500

@app.route('/', methods=['GET'])
def index():
    """
    根路径，返回API信息 - 优化版本
    """
    print("访问优化版OCR API首页")
    return jsonify({
        'message': 'Optimized PaddleOCR API for ID Cards',
        'version': 'PaddleOCR v4 (CPU Optimized)',
        'target': '专门针对身份证识别优化',
        'features': {
            'id_number_extraction': '身份证号码提取',
            'address_extraction': '地址信息提取',
            'fast_processing': '快速处理',
            'cpu_optimized': 'CPU环境优化',
            'lossless_scaling': '无损缩放处理'
        },
        'endpoints': {
            'ocr': '/ocr (POST) - 身份证OCR识别',
            'health': '/health (GET) - 健康检查'
        },
        'usage': {
            'base64': 'POST /ocr with JSON {"image": "base64_string"}',
            'base64_with_scaling': 'POST /ocr with JSON {"image": "base64_string", "enable_lossless_scaling": true, "max_dimension": 1200}',
            'file_upload': 'POST /ocr with file in form data',
            'image_path': 'POST /ocr with form data {"image_path": "path/to/image"}'
        },
        'optimizations': [
            'CPU线程数优化 (4线程)',
            '身份证专用图像预处理',
            '简化的信息提取算法',
            '模型预热机制',
            '关闭不必要的角度分类',
            '轻量级模型配置',
            '无损缩放技术 (LANCZOS4插值)'
        ],
        'performance': {
            'target_time': '< 5秒',
            'target_accuracy': '> 95%',
            'environment': 'CPU服务器'
        }
    })

if __name__ == '__main__':
    print("PaddleOCR服务启动中...")
    app.run(host='0.0.0.0', port=5000, debug=True) 