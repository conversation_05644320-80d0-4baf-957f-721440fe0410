import os
import base64
import io
import json
import re
from flask import Flask, request, jsonify
from flask_cors import CORS
import cv2
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR

app = Flask(__name__)
CORS(app)

# 优化的PaddleOCR配置 - 针对身份证识别优化

# 检测是否有GPU可用
def check_gpu_available():
    try:
        import paddle
        return paddle.device.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
    except:
        return False

# 根据系统配置优化参数
def get_optimal_cpu_threads():
    cpu_count = os.cpu_count() or 4
    # 对于身份证识别，4-6个线程通常是最优的
    return min(6, max(4, cpu_count // 2))

# 初始化优化的PaddleOCR
use_gpu = check_gpu_available()
optimal_threads = get_optimal_cpu_threads()

print(f"OCR配置: GPU可用={use_gpu}, CPU线程数={optimal_threads}")

ocr = PaddleOCR(
    use_angle_cls=False,  # 身份证通常不需要角度分类，可以关闭以提速
    lang='ch',
    use_gpu=use_gpu,  # 自动检测GPU
    show_log=False,
    use_mp=False,  # 单进程模式，避免进程间通信开销
    total_process_num=1,
    cpu_threads=optimal_threads,  # 优化的CPU线程数
    enable_mkldnn=True,
    # 使用轻量级模型以提高速度
    det_limit_side_len=960,  # 降低检测分辨率限制
    det_db_thresh=0.3,       # 降低检测阈值
    det_db_box_thresh=0.6,   # 提高框选阈值
    det_db_unclip_ratio=1.5, # 优化文本框扩展比例
    rec_batch_num=6          # 优化识别批次大小
)

def preprocess_image_v4(image):
    """
    PaddleOCR v4 图像预处理优化
    """
    try:
        # v4版本推荐的图像预处理
        if len(image.shape) == 3 and image.shape[2] == 3:
            # 转换为灰度图以提高识别速度（可选）
            # gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            # image = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
            pass

        # 图像尺寸优化 - v4版本对大图像处理更好
        height, width = image.shape[:2]
        if width > 2000 or height > 2000:
            # 按比例缩放大图像
            scale = min(2000/width, 2000/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"图像缩放: {width}x{height} -> {new_width}x{new_height}")

        return image
    except Exception as e:
        print(f"图像预处理失败: {str(e)}")
        return image

def process_image(image):
    """
    处理图像并返回OCR结果 - 适配PaddleOCR v4
    """
    try:
        # v4版本图像预处理
        processed_image = preprocess_image_v4(image)

        # 使用PaddleOCR v4进行识别
        # v4版本支持更多参数配置
        result = ocr.ocr(
            processed_image,
            cls=True,
            det=True,  # v4新增：是否进行文本检测
            rec=True   # v4新增：是否进行文本识别
        )

        # 格式化结果 - v4版本结果格式保持兼容
        ocr_results = []
        if result and len(result) > 0 and result[0]:
            for line in result[0]:
                if line and len(line) >= 2:
                    # line[0] 是坐标点，line[1] 是(文本, 置信度)
                    text = line[1][0] if len(line[1]) > 0 else ""  # 识别的文本
                    confidence = float(line[1][1]) if len(line[1]) > 1 else 0.0  # 置信度
                    coordinates = line[0]  # 坐标点

                    ocr_results.append({
                        'text': text,
                        'confidence': confidence,
                        'coordinates': coordinates
                    })
        
        # 打印识别结果 - v4版本增强日志
        print("=" * 50)
        print("PaddleOCR v4 识别结果:")
        print(f"识别到 {len(ocr_results)} 个文本区域")
        for i, item in enumerate(ocr_results):
            print(f"区域{i+1}: {item['text']}, 置信度: {item['confidence']:.4f}")
        print("总文本内容:")
        print('\n'.join([item['text'] for item in ocr_results]))
        print("=" * 50)
        
        # 尝试解析身份证信息
        id_card_data = parse_id_card_info(ocr_results)
        
        return {
            'success': True,
            'data': id_card_data,
            'raw_results': ocr_results,
            'total_text': '\n'.join([item['text'] for item in ocr_results]),
            'version': 'PaddleOCR v4',  # v4版本标识
            'text_regions_count': len(ocr_results)  # v4新增：文本区域数量
        }
    except Exception as e:
        print(f"PaddleOCR v4 处理出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'version': 'PaddleOCR v4'
        }

def parse_id_card_info(ocr_results):
    """
    解析身份证信息 - 优化地址识别
    """
    id_card_data = {
        'name': None,
        'gender': None,
        'id_number': None,
        'address': None
    }

    # 常见的身份证关键字
    name_keywords = ['姓名', '名字']
    gender_keywords = ['性别', '男', '女']
    id_keywords = ['公民身份号码', '身份号码', '身份证号码', '号码']
    address_keywords = ['住址', '地址', '佳址', '住所', '单位']
    exclude_keywords = ['出生', '民族', '有效期', '签发', '发证', '公民身份号码', '签发机关']

    # 判断文本是否包含特定关键词
    def contains_keyword(text, keywords):
        for keyword in keywords:
            if keyword in text:
                return True
        return False

    all_text = ' '.join([item['text'] for item in ocr_results])
    print(f"正在解析身份证信息，总文本: {all_text}")

    # 尝试提取身份证号码(18位数字，或者17位数字+X)
    id_pattern = r'[0-9]{17}[0-9Xx]'
    id_matches = re.findall(id_pattern, all_text.replace(' ', ''))
    if id_matches:
        id_card_data['id_number'] = id_matches[0]
        print(f"找到身份证号: {id_card_data['id_number']}")

    # 收集可能的地址片段
    address_fragments = []

    # 处理每个识别文本
    for item in ocr_results:
        text = item['text'].strip()

        # 查找姓名
        for keyword in name_keywords:
            if keyword in text and id_card_data['name'] is None:
                # 提取姓名 (通常在关键词后面)
                name_parts = text.split(keyword)
                if len(name_parts) > 1 and len(name_parts[1].strip()) > 0:
                    id_card_data['name'] = name_parts[1].strip()
                    print(f"找到姓名: {id_card_data['name']}")
                    break

        # 查找性别
        if '男' in text and id_card_data['gender'] is None:
            id_card_data['gender'] = '男'
            print("找到性别: 男")
        elif '女' in text and id_card_data['gender'] is None:
            id_card_data['gender'] = '女'
            print("找到性别: 女")

        # 查找并收集地址片段
        is_address_fragment = False

        # 检查是否包含地址关键词
        for keyword in address_keywords:
            if keyword in text:
                # 提取地址 (通常在关键词后面)
                address_parts = text.split(keyword)
                if len(address_parts) > 1 and len(address_parts[1].strip()) > 0:
                    address_fragment = address_parts[1].strip()
                    print(f"找到地址片段(关键词): {address_fragment}")
                    address_fragments.append(address_fragment)
                    is_address_fragment = True
                    break
                else:
                    # 如果关键词后面没有文本，可能整行都是地址的一部分
                    is_address_fragment = True

        # 如果没有明确的关键词，判断是否可能是地址片段
        if not is_address_fragment:
            # 跳过明显不是地址的内容
            if (contains_keyword(text, exclude_keywords) or
                contains_keyword(text, name_keywords) or
                contains_keyword(text, gender_keywords) or
                contains_keyword(text, id_keywords) or
                re.search(id_pattern, text.replace(' ', '')) or
                len(text) < 3):  # 太短的文本通常不是地址
                continue

            # 检查是否包含地址常见词汇 (省/市/县/区/村/镇等)
            address_indicators = ['省', '市', '县', '区', '镇', '村', '组', '路', '街', '号', '乡', '里', '巷', '弄']
            has_address_indicator = False
            for indicator in address_indicators:
                if indicator in text:
                    has_address_indicator = True
                    break

            # 如果包含地址标识符，或者是较长的文本（可能是地址的一部分）
            if has_address_indicator or len(text) > 8:
                print(f"找到疑似地址片段: {text}")
                address_fragments.append(text)

    # 优化地址片段合并逻辑
    if address_fragments:
        print(f"收集到的地址片段: {address_fragments}")

        # 按地址层级排序
        def get_address_priority(text):
            # 优先级: 省>市>区/县>镇>村>组/号
            if '省' in text: return 1
            if '市' in text: return 2
            if '区' in text or '县' in text: return 3
            if '镇' in text or '乡' in text: return 4
            if '村' in text or '里' in text: return 5
            if '组' in text or '号' in text or '路' in text or '街' in text: return 6
            return 10  # 其他

        # 按可能的地址顺序排序
        address_fragments.sort(key=get_address_priority)
        print(f"排序后的地址片段: {address_fragments}")

        # 智能合并地址片段
        merged_address = ""

        # 简化合并逻辑：直接按顺序连接所有地址片段
        for fragment in address_fragments:
            if merged_address:
                # 检查是否有重叠部分
                overlap_found = False
                max_overlap = min(len(merged_address), len(fragment))

                # 从最大可能的重叠开始检查
                for overlap_len in range(max_overlap, 0, -1):
                    if merged_address.endswith(fragment[:overlap_len]):
                        merged_address += fragment[overlap_len:]
                        overlap_found = True
                        print(f"发现重叠合并: '{merged_address[:-len(fragment)+overlap_len]}' + '{fragment}' -> '{merged_address}'")
                        break

                if not overlap_found:
                    merged_address += fragment
                    print(f"直接连接: '{merged_address[:-len(fragment)]}' + '{fragment}' -> '{merged_address}'")
            else:
                merged_address = fragment
                print(f"初始地址片段: '{fragment}'")

        # 清理最终地址
        merged_address = merged_address.strip()
        # 移除重复的空格
        merged_address = re.sub(r'\s+', '', merged_address)

        # 修复常见的OCR识别错误
        address_fixes = {
            '四川省什市': '四川省什邡市',  # 修复缺失的"邡"字
            '四川什邡市': '四川省什邡市',   # 修复缺失的"省"字
            '川省什邡市': '四川省什邡市',   # 修复缺失的"四"字
        }

        for wrong, correct in address_fixes.items():
            if wrong in merged_address:
                merged_address = merged_address.replace(wrong, correct)
                print(f"修复地址错误: '{wrong}' -> '{correct}'")

        # 验证地址的完整性
        if merged_address:
            # 检查是否包含基本的地址要素
            has_province = '省' in merged_address
            has_city = '市' in merged_address
            has_detail = any(x in merged_address for x in ['镇', '村', '组', '号', '路', '街'])

            if not (has_province and has_city and has_detail):
                print(f"警告: 地址可能不完整 - 省:{has_province}, 市:{has_city}, 详细:{has_detail}")

                # 尝试从所有OCR结果中寻找更完整的地址信息
                all_address_text = ""
                for item in ocr_results:
                    text = item['text'].strip()
                    if any(indicator in text for indicator in ['省', '市', '县', '区', '镇', '村', '组', '号']):
                        all_address_text += text

                if len(all_address_text) > len(merged_address):
                    print(f"使用更完整的地址信息: '{all_address_text}'")
                    merged_address = all_address_text

        id_card_data['address'] = merged_address
        print(f"最终合并的地址: {id_card_data['address']}")

    return id_card_data

def base64_to_image(base64_string):
    """
    将base64字符串转换为OpenCV图像
    """
    try:
        # 移除base64前缀（如果存在）
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # 解码base64
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为OpenCV格式
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        return image_cv
    except Exception as e:
        raise ValueError(f"Invalid base64 image: {str(e)}")

@app.route('/ocr', methods=['POST'])
def ocr_endpoint():
    """
    OCR识别端点
    支持多种输入方式：
    1. base64编码的图像 (JSON格式)
    2. base64编码的图像 (表单数据)
    3. 文件上传
    """
    try:
        print("\n接收到OCR请求...")
        print(f"请求内容类型: {request.content_type}")
        print(f"请求方法: {request.method}")
        
        image_cv = None
        
        # 检查是否为JSON格式
        if request.is_json:
            print("处理JSON请求...")
            data = request.get_json()
            
            if 'image' in data:
                base64_string = data['image']
                print(f"从JSON提取base64图像数据，长度: {len(base64_string)}")
                image_cv = base64_to_image(base64_string)
            else:
                print("错误: JSON数据中没有'image'字段")
                return jsonify({
                    'success': False,
                    'error': 'No image field in JSON data'
                }), 400
        
        # 检查是否为表单数据
        elif request.form or request.files:
            print("处理表单数据请求...")
            
            # 检查表单中的base64数据
            if 'image_base64' in request.form:
                base64_string = request.form['image_base64']
                print(f"从表单获取base64图像数据，长度: {len(base64_string)}")
                image_cv = base64_to_image(base64_string)
            
            # 检查表单中的图片文件
            elif 'image' in request.files:
                file = request.files['image']
                if file.filename == '':
                    print("错误: 没有选择文件")
                    return jsonify({
                        'success': False,
                        'error': 'No file selected'
                    }), 400
                
                print(f"处理上传的文件: {file.filename}")
                file_data = file.read()
                image = Image.open(io.BytesIO(file_data))
                image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 检查是否有文件路径
            elif 'image_path' in request.form:
                image_path = request.form['image_path']
                print(f"从路径加载图像: {image_path}")
                
                if os.path.exists(image_path):
                    image_cv = cv2.imread(image_path)
                else:
                    print(f"错误: 文件路径不存在: {image_path}")
                    return jsonify({
                        'success': False,
                        'error': f'File not found: {image_path}'
                    }), 404
            
            else:
                print("错误: 表单数据中没有图像数据")
                return jsonify({
                    'success': False,
                    'error': 'No image data in form'
                }), 400
        
        # 如果都不是，尝试读取原始数据
        else:
            print("尝试从请求体读取原始数据...")
            raw_data = request.get_data()
            
            if raw_data:
                try:
                    # 尝试解析为JSON
                    data = json.loads(raw_data)
                    if 'image' in data:
                        base64_string = data['image']
                        print(f"从原始数据提取base64图像，长度: {len(base64_string)}")
                        image_cv = base64_to_image(base64_string)
                    else:
                        print("错误: JSON数据中没有'image'字段")
                        return jsonify({
                            'success': False,
                            'error': 'No image field in raw JSON data'
                        }), 400
                except json.JSONDecodeError:
                    # 不是JSON，可能是直接的base64数据
                    try:
                        print("尝试将整个请求体作为base64处理...")
                        image_cv = base64_to_image(raw_data.decode('utf-8'))
                    except Exception as e:
                        print(f"无法解析请求体为base64图像: {str(e)}")
                        return jsonify({
                            'success': False,
                            'error': 'Could not parse request body as base64 image'
                        }), 400
            else:
                print("错误: 请求体为空")
                return jsonify({
                    'success': False,
                    'error': 'Empty request body'
                }), 400
        
        # 如果没有获取到图像数据
        if image_cv is None:
            print("错误: 无法获取图像数据")
            return jsonify({
                'success': False,
                'error': 'No valid image data provided'
            }), 400
        
        # 处理图像
        print("开始OCR识别...")
        result = process_image(image_cv)
        print("OCR识别完成，返回结果")
        return jsonify(result)
        
    except Exception as e:
        print(f"OCR处理异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查端点 - v4版本增强
    """
    print("收到健康检查请求")
    try:
        # v4版本健康检查 - 测试OCR是否正常工作
        import numpy as np
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
        _ = ocr.ocr(test_image, cls=False, det=False, rec=False)  # 测试OCR功能

        return jsonify({
            'status': 'healthy',
            'service': 'PaddleOCR v4 Flask API',
            'version': 'PaddleOCR v4 (2.7.x)',
            'ocr_engine': 'functional',
            'features': [
                'Text Detection',
                'Text Recognition',
                'Angle Classification',
                'Image Preprocessing',
                'ID Card Parsing'
            ]
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'service': 'PaddleOCR v4 Flask API',
            'error': str(e)
        }), 500

@app.route('/', methods=['GET'])
def index():
    """
    根路径，返回API信息 - v4版本
    """
    print("访问PaddleOCR v4 API首页")
    return jsonify({
        'message': 'PaddleOCR v4 Flask API',
        'version': 'PaddleOCR v4 (2.7.x)',
        'features': {
            'text_detection': '文本检测',
            'text_recognition': '文本识别',
            'angle_classification': '角度分类',
            'id_card_parsing': '身份证信息解析',
            'image_preprocessing': '图像预处理优化'
        },
        'endpoints': {
            'ocr': '/ocr (POST) - OCR识别',
            'health': '/health (GET) - 健康检查'
        },
        'usage': {
            'file_upload': 'POST /ocr with file in form data',
            'base64': 'POST /ocr with JSON {"image": "base64_string"}',
            'image_path': 'POST /ocr with form data {"image_path": "path/to/image"}'
        },
        'improvements_v4': [
            '更好的大图像处理',
            '增强的错误处理',
            '性能监控',
            '更准确的文本识别',
            '优化的内存使用'
        ]
    })

if __name__ == '__main__':
    print("PaddleOCR服务启动中...")
    app.run(host='0.0.0.0', port=5000, debug=True) 