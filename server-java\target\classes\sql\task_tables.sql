-- 定时任务表
CREATE TABLE IF NOT EXISTS `sys_task` (
  `id` varchar(32) NOT NULL COMMENT '任务ID',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `project_id` varchar(32) NOT NULL COMMENT '所属项目ID',
  `project` varchar(100) NOT NULL COMMENT '所属项目名称',
  `cron` varchar(255) NOT NULL COMMENT 'Cron表达式',
  `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务状态：1-启用，0-禁用',
  `last_run_time` datetime DEFAULT NULL COMMENT '上次执行时间',
  `last_run_result` tinyint(1) DEFAULT NULL COMMENT '上次执行结果：1-成功，0-失败',
  `next_run_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `handler_class` varchar(255) DEFAULT NULL COMMENT '任务处理器类名',
  `params` text COMMENT '任务参数（JSON格式）',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务表';

-- 定时任务执行日志表
CREATE TABLE IF NOT EXISTS `sys_task_log` (
  `id` varchar(32) NOT NULL COMMENT '日志ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `project` varchar(100) NOT NULL COMMENT '所属项目',
  `start_time` datetime NOT NULL COMMENT '开始执行时间',
  `end_time` datetime NOT NULL COMMENT '结束执行时间',
  `duration` bigint(20) NOT NULL COMMENT '执行耗时（毫秒）',
  `status` tinyint(1) NOT NULL COMMENT '执行结果：1-成功，0-失败',
  `error_msg` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `content` text COMMENT '执行内容',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务执行日志表'; 