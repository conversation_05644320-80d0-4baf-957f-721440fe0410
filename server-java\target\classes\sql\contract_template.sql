-- 创建合同模板表
CREATE TABLE IF NOT EXISTS `contract_template` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `content` longtext NOT NULL COMMENT '模板内容',
  `variables` longtext DEFAULT NULL COMMENT '模板变量，JSON格式存储',
  `form_data` longtext DEFAULT NULL COMMENT '表单数据，JSON格式存储',
  `creator_id` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`) COMMENT '模板名称索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同模板表';

-- 添加表单数据字段的更新脚本
ALTER TABLE `contract_template` 
ADD COLUMN `variables` longtext DEFAULT NULL COMMENT '模板变量，JSON格式存储' AFTER `content`,
ADD COLUMN `form_data` longtext DEFAULT NULL COMMENT '表单数据，JSON格式存储' AFTER `variables`; 