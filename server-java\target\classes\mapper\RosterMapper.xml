<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.daka.pro.mapper.RosterMapper">

    <!-- 获取项目在场总人数 -->
    <select id="countByProjectId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM roster
        WHERE project_code = #{projectCode}
    </select>

    <!-- 获取项目的参建单位列表 -->
    <select id="getProjectParticipants" resultType="java.util.Map">
        SELECT 
            project_sub_contractor_code as code,
            project_sub_contractor_name as name,
            project_sub_contractor_id as id,
            COUNT(*) as workerCount
        FROM roster
        WHERE project_code = #{projectCode}
        GROUP BY project_sub_contractor_code, project_sub_contractor_name, project_sub_contractor_id
        ORDER BY workerCount DESC
    </select>

</mapper> 