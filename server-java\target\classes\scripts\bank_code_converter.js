const fs = require('fs');
const path = require('path');

// 读取银行代码文本文件
const inputFile = path.join(__dirname, '../txt');
const outputFile = path.join(__dirname, '../json/bank_codes.json');

// 确保输出目录存在
const outputDir = path.dirname(outputFile);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 读取文件内容
fs.readFile(inputFile, 'utf8', (err, data) => {
  if (err) {
    console.error('读取文件失败:', err);
    return;
  }

  // 将文本按行分割
  const lines = data.split('\n');
  const bankCodes = [];

  // 处理每一行数据
  for (const line of lines) {
    // 跳过空行
    if (!line.trim()) continue;
    
    // 按第一个空格分割获取代码和名称
    const spaceIndex = line.indexOf(' ');
    if (spaceIndex !== -1) {
      const code = line.substring(0, spaceIndex).trim();
      const name = line.substring(spaceIndex + 1).trim();
      
      // 确保代码和名称都有值
      if (code && name) {
        bankCodes.push({
          code: code,
          name: name
        });
      }
    }
  }

  // 将数据写入JSON文件
  fs.writeFile(outputFile, JSON.stringify(bankCodes, null, 2), 'utf8', (err) => {
    if (err) {
      console.error('写入JSON文件失败:', err);
      return;
    }
    console.log(`成功将银行代码转换为JSON格式，共${bankCodes.length}条记录`);
    console.log(`文件保存路径: ${outputFile}`);
  });
}); 