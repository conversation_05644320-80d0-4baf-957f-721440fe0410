{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractSteps.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractSteps.vue", "mtime": 1753348142493}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\babel.config.js", "mtime": 1746865124045}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "activeStep", "workerInfo", "workerId", "worker<PERSON>ame", "idCardNumber", "gender", "home<PERSON>dd<PERSON>", "bankCardNumber", "bankName", "teamName", "teamCode", "projectCode", "projectName", "participantCode", "participantName", "jobPosition", "jobDescription", "contractType", "fixedStartDate", "fixedEndDate", "projectStartDate", "taskStartDate", "taskEndDate", "otherTask", "workLocation", "salaryType", "fixedSalary", "timeUnit", "timeUnitSalary", "pieceUnit", "pieceUnitSalary", "otherSalaryForm", "salaryDay", "benefits", "otherMatters", "signDate", "companyName", "legalRepresentative", "agent", "companyAddress", "templateId", "templateVariables", "created", "fromExternal", "document", "referrer", "includes", "dataRestored", "restoreFromLocalStorage", "console", "log", "clearContractData", "$route", "query", "loadProjectInfo", "setActiveStepFromRoute", "JSON", "stringify", "watch", "to", "from", "path", "isWithinContractFlow", "ensureDataConsistency", "methods", "savedWorkerInfo", "localStorage", "getItem", "parsedData", "parse", "key", "undefined", "error", "navigateToStep", "step", "$nextTick", "$router", "replace", "catch", "err", "message", "push", "removeItem", "nextStep", "prevStep", "params", "updateWorkerInfo", "info", "beforeUpdate", "Object", "keys", "join", "savedData", "newData", "setItem", "e", "$children", "length", "for<PERSON>ach", "child", "initFormFromProps", "goBack"], "sources": ["src/views/contract/ContractSteps.vue"], "sourcesContent": ["<template>\n  <div class=\"contract-steps-container\">\n    <div class=\"steps-header\">\n      <h2>合同生成流程</h2>\n      <el-steps :active=\"activeStep\" finish-status=\"success\" align-center>\n        <el-step title=\"工人身份信息\" description=\"识别身份证信息\"></el-step>\n        <el-step title=\"银行卡信息\" description=\"输入银行卡号\"></el-step>\n        <el-step title=\"合同信息\" description=\"填写合同表单\"></el-step>\n        <el-step title=\"合同预览\" description=\"预览并打印\"></el-step>\n      </el-steps>\n    </div>\n    \n    <div class=\"steps-content\">\n      <router-view \n        @next-step=\"nextStep\" \n        @prev-step=\"prevStep\" \n        :worker-info=\"workerInfo\" \n        @update-worker-info=\"updateWorkerInfo\">\n      </router-view>\n    </div>\n    \n    <div class=\"steps-action\">\n      <el-button @click=\"prevStep\" :disabled=\"activeStep === 1\">上一步</el-button>\n      <el-button type=\"primary\" @click=\"nextStep\" :disabled=\"activeStep === 4\">下一步</el-button>\n      <el-button @click=\"goBack\">返回花名册</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ContractSteps',\n  data() {\n    return {\n      activeStep: 1,\n      workerInfo: {\n        // 工人基本信息\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        // 银行卡信息\n        bankCardNumber: '',\n        bankName: '',\n        // 工作信息\n        teamName: '',\n        teamCode: '',\n        projectCode: '',\n        projectName: '',\n        participantCode: '',\n        participantName: '',\n        jobPosition: '',\n        jobDescription: '',\n        // 合同信息\n        contractType: 0,\n        fixedStartDate: '',\n        fixedEndDate: '',\n        projectStartDate: '',\n        taskStartDate: '',\n        taskEndDate: '',\n        otherTask: '',\n        workLocation: '',\n        salaryType: 1,\n        fixedSalary: 0,\n        timeUnit: '日',\n        timeUnitSalary: 0,\n        pieceUnit: '平方',\n        pieceUnitSalary: 0,\n        otherSalaryForm: '',\n        salaryDay: 15,\n        benefits: [],\n        otherMatters: '',\n        signDate: '',\n        // 公司信息\n        companyName: '',\n        legalRepresentative: '',\n        agent: '',\n        companyAddress: '',\n        // 模板信息\n        templateId: '',\n        templateVariables: []\n      }\n    }\n  },\n  created() {\n    // 检查是否是从合同流程外部进入\n    const fromExternal = !document.referrer.includes('/contract/contract-steps/')\n\n    // 尝试从localStorage恢复数据\n    const dataRestored = this.restoreFromLocalStorage()\n\n    // 只有从外部进入且没有恢复到数据时才清空\n    if (fromExternal && !dataRestored) {\n      console.log('ContractSteps - 从外部进入合同流程，且没有已保存的数据，初始化空数据')\n      this.clearContractData()\n    } else {\n      console.log('ContractSteps - 使用已存在的表单数据')\n    }\n    \n    // 从URL获取项目编码\n    const { projectCode } = this.$route.query\n    if (projectCode) {\n      this.workerInfo.projectCode = projectCode\n      // 可以在这里加载项目信息\n      this.loadProjectInfo(projectCode)\n    }\n    \n    // 根据当前路由设置激活步骤\n    this.setActiveStepFromRoute()\n    \n    console.log('ContractSteps created - 初始化后的数据:', JSON.stringify(this.workerInfo))\n  },\n  watch: {\n    // 监听路由变化，更新激活的步骤\n    $route(to, from) {\n      console.log(`路由变化: 从 ${from.path} 到 ${to.path}`)\n      \n      // 只有在合同流程内部导航时才保留数据\n      const isWithinContractFlow = to.path.includes('/contract/contract-steps/') && from.path.includes('/contract/contract-steps/')\n\n      if (isWithinContractFlow) {\n        console.log('ContractSteps - 在合同流程内部导航，保留数据')\n        this.setActiveStepFromRoute()\n        this.ensureDataConsistency()\n      } else if (to.path.includes('/contract/contract-steps/')) {\n        console.log('ContractSteps - 从外部进入合同流程，设置步骤')\n      this.setActiveStepFromRoute()\n      }\n    }\n  },\n  methods: {\n    // 确保数据一致性\n    ensureDataConsistency() {\n      // 从localStorage恢复数据，确保数据是最新的\n      this.restoreFromLocalStorage()\n      \n      // 添加调试日志\n      console.log('ContractSteps - 确保数据一致性后的数据:', JSON.stringify(this.workerInfo))\n    },\n    \n    // 从localStorage恢复数据\n    restoreFromLocalStorage() {\n      try {\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\n        if (savedWorkerInfo) {\n          const parsedData = JSON.parse(savedWorkerInfo)\n          console.log('ContractSteps - 从localStorage恢复数据:', JSON.stringify(parsedData))\n          \n          // 更新工人信息，保留现有非空值\n          for (const key in parsedData) {\n            if (parsedData[key] !== null && parsedData[key] !== undefined && parsedData[key] !== '') {\n              this.workerInfo[key] = parsedData[key]\n            }\n          }\n          \n          console.log('ContractSteps - 恢复后的数据:', JSON.stringify(this.workerInfo))\n          return true\n        }\n      } catch (error) {\n        console.error('ContractSteps - 从localStorage恢复数据失败:', error)\n      }\n      return false\n    },\n    \n    // 导航到指定步骤\n    navigateToStep(step) {\n      // 设置活动步骤\n      this.activeStep = step\n      \n      // 使用 $nextTick 确保视图已更新\n      this.$nextTick(() => {\n        console.log(`尝试导航到步骤 ${step}`)\n        \n        // 构造目标路径\n        const path = `/contract/contract-steps/step${step}`\n        const query = { projectCode: this.workerInfo.projectCode || '' }\n        \n        // 检查当前路由是否已经是目标路由\n        if (this.$route.path === path) {\n          console.log(`已在路径 ${path}，使用 replace 而非 push`)\n          // 如果已经在相同路径，使用 replace 而非 push\n          this.$router.replace({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航替换被忽略:', err.message)\n          })\n        } else {\n          // 否则使用 push 导航\n          this.$router.push({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航推送被忽略:', err.message)\n          })\n        }\n      })\n    },\n    \n    // 根据路由设置激活步骤\n    setActiveStepFromRoute() {\n      const path = this.$route.path\n      if (path.includes('step1')) {\n        this.activeStep = 1\n      } else if (path.includes('step2')) {\n        this.activeStep = 2\n      } else if (path.includes('step3')) {\n        this.activeStep = 3\n      } else if (path.includes('step4')) {\n        this.activeStep = 4\n      }\n    },\n    \n    // 加载项目信息\n    loadProjectInfo(projectCode) {\n      // 这里可以调用API获取项目信息\n      // 示例代码，实际需要替换为真实API调用\n      console.log('加载项目信息:', projectCode)\n      // 可以设置公司信息的默认值\n      this.workerInfo.companyName = ''\n      this.workerInfo.legalRepresentative = ''\n      this.workerInfo.agent = ''\n      this.workerInfo.companyAddress = ''\n    },\n    \n    // 清空合同数据\n    clearContractData() {\n      console.log('ContractSteps - 清空所有合同相关数据')\n      \n      // 重置工人信息对象到初始状态\n      this.workerInfo = {\n        // 工人基本信息\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        // 银行卡信息\n        bankCardNumber: '',\n        bankName: '',\n        // 工作信息\n        teamName: '',\n        teamCode: '',\n        projectCode: this.$route.query.projectCode || '',\n        projectName: '',\n        participantCode: '',\n        participantName: '',\n        jobPosition: '',\n        jobDescription: '',\n        // 合同信息\n        contractType: 0,\n        fixedStartDate: '',\n        fixedEndDate: '',\n        projectStartDate: '',\n        taskStartDate: '',\n        taskEndDate: '',\n        otherTask: '',\n        workLocation: '',\n        salaryType: 1,\n        fixedSalary: 0,\n        timeUnit: '日',\n        timeUnitSalary: 0,\n        pieceUnit: '平方',\n        pieceUnitSalary: 0,\n        otherSalaryForm: '',\n        salaryDay: 15,\n        benefits: [],\n        otherMatters: '',\n        signDate: '',\n        // 公司信息\n        companyName: '',\n        legalRepresentative: '',\n        agent: '',\n        companyAddress: '',\n        // 模板信息\n        templateId: '',\n        templateVariables: []\n      }\n      \n      // 清空localStorage中的数据\n      localStorage.removeItem('contractWorkerInfo')\n      \n      // 如果有公司默认信息，重新加载\n      if (this.$route.query.projectCode) {\n        this.loadProjectInfo(this.$route.query.projectCode)\n      }\n    },\n    \n    // 下一步\n    nextStep() {\n      console.log('=== ContractSteps nextStep 开始 ===')\n      console.log('ContractSteps nextStep - 当前步骤:', this.activeStep)\n      console.log('ContractSteps nextStep - 当前workerInfo:', JSON.stringify(this.workerInfo))\n\n      if (this.activeStep < 4) {\n        const nextStep = this.activeStep + 1\n        console.log('ContractSteps nextStep - 将导航到步骤:', nextStep)\n\n        // 先确保数据已从localStorage同步\n        this.restoreFromLocalStorage()\n        console.log('ContractSteps nextStep - 从localStorage恢复后的数据:', JSON.stringify(this.workerInfo))\n\n        // 强制更新后再导航\n        this.$nextTick(() => {\n          // 确保激活步骤已更新\n          this.activeStep = nextStep\n\n          // 导航到下一步\n          this.navigateToStep(nextStep)\n\n          // 在导航后再次确保数据一致\n          this.$nextTick(() => {\n            this.ensureDataConsistency()\n            console.log('ContractSteps nextStep - 导航完成，最终数据:', JSON.stringify(this.workerInfo))\n            console.log('=== ContractSteps nextStep 结束 ===')\n          })\n        })\n      }\n    },\n    \n    // 上一步\n    prevStep(params) {\n      console.log('父组件: 触发上一步, 当前步骤:', this.activeStep, '参数:', params)\n      if (this.activeStep > 1) {\n        const prevStep = this.activeStep - 1\n        console.log('父组件: 将导航到步骤:', prevStep)\n        \n        // 先确保数据已从localStorage同步\n        this.restoreFromLocalStorage()\n        \n        // 强制更新后再导航\n        this.$nextTick(() => {\n          // 确保激活步骤已更新\n          this.activeStep = prevStep\n          \n          // 导航到上一步，传递query参数\n          const query = { \n            projectCode: this.workerInfo.projectCode || '',\n            ...(params || {}) // 合并传入的参数\n          }\n          \n          // 构造目标路径\n          const path = `/contract/contract-steps/step${prevStep}`\n          \n          // 导航到上一步\n          this.$router.push({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航推送被忽略:', err.message)\n          })\n          \n          // 在导航后再次确保数据一致\n          this.$nextTick(() => {\n            this.ensureDataConsistency()\n          })\n        })\n      }\n    },\n    \n    // 更新工人信息\n    updateWorkerInfo(info) {\n      if (!info) return;\n\n      console.log('=== ContractSteps updateWorkerInfo 开始 ===')\n      console.log('ContractSteps 接收到更新请求:', JSON.stringify(info))\n      console.log('ContractSteps 当前workerInfo:', JSON.stringify(this.workerInfo))\n\n      // 保存更新前的数据\n      const beforeUpdate = JSON.stringify(this.workerInfo)\n\n      // 防止空值覆盖已有的有效值\n      for (const key in info) {\n        // 只有当新值不为空或未定义时才更新\n        if (info[key] !== null && info[key] !== undefined && info[key] !== '') {\n          this.workerInfo[key] = info[key]\n        }\n      }\n\n      // 直接确保关键字段已正确更新\n      if (info.workerName) this.workerInfo.workerName = info.workerName;\n      if (info.idCardNumber) this.workerInfo.idCardNumber = info.idCardNumber;\n      if (info.bankCardNumber) this.workerInfo.bankCardNumber = info.bankCardNumber;\n\n      // 打印详细的数据对比\n      console.log('ContractSteps 数据更新结果:')\n      console.log('- 更新前:', beforeUpdate)\n      console.log('- 更新后:', JSON.stringify(this.workerInfo))\n      console.log('- 更新的字段:', Object.keys(info).join(', '))\n      console.log('- 关键字段检查:')\n      console.log('  * workerName:', this.workerInfo.workerName)\n      console.log('  * idCardNumber:', this.workerInfo.idCardNumber)\n      \n      // 确保localStorage也被更新\n      try {\n        // 从localStorage获取当前数据\n        const savedData = localStorage.getItem('contractWorkerInfo') || '{}'\n        const parsedData = JSON.parse(savedData)\n        \n        // 合并现有数据和更新的数据\n        const newData = { ...parsedData }\n        \n        // 只更新有值的字段\n        for (const key in info) {\n          if (info[key] !== null && info[key] !== undefined && info[key] !== '') {\n            newData[key] = info[key]\n          }\n        }\n        \n        // 保存回localStorage\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(newData))\n        console.log('已更新localStorage数据:', JSON.stringify(newData))\n        \n        // 再次确保this.workerInfo包含所有localStorage中的数据\n        for (const key in newData) {\n          if (newData[key] !== null && newData[key] !== undefined && newData[key] !== '' &&\n              (!this.workerInfo[key] || this.workerInfo[key] === '')) {\n            this.workerInfo[key] = newData[key]\n          }\n        }\n      } catch (e) {\n        console.error('更新localStorage失败:', e)\n      }\n      \n      // 强制子组件更新\n      this.$nextTick(() => {\n        if (this.$children && this.$children.length) {\n          this.$children.forEach(child => {\n            if (typeof child.initFormFromProps === 'function') {\n              console.log('强制更新子组件数据')\n              child.initFormFromProps()\n            }\n          })\n        }\n      })\n    },\n    \n    // 返回花名册\n    goBack() {\n      // 清空合同数据\n      this.clearContractData()\n      console.log('ContractSteps - 返回花名册，清空所有数据')\n      \n      this.$router.push({ name: 'RosterList' }).catch(err => {\n        // 忽略重复导航错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.contract-steps-container {\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.steps-header {\n  margin-bottom: 30px;\n}\n\n.steps-header h2 {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #303133;\n}\n\n.steps-content {\n  margin: 30px 0;\n  min-height: 400px;\n  padding: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background-color: #f9fafc;\n}\n\n.steps-action {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style> "], "mappings": ";;;AA8BA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;QACA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,MAAA;QACAC,WAAA;QACA;QACAC,cAAA;QACAC,QAAA;QACA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACA;QACAC,YAAA;QACAC,cAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,cAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAC,SAAA;QACAC,QAAA;QACAC,YAAA;QACAC,QAAA;QACA;QACAC,WAAA;QACAC,mBAAA;QACAC,KAAA;QACAC,cAAA;QACA;QACAC,UAAA;QACAC,iBAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACA,MAAAC,YAAA,IAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA;;IAEA;IACA,MAAAC,YAAA,QAAAC,uBAAA;;IAEA;IACA,IAAAL,YAAA,KAAAI,YAAA;MACAE,OAAA,CAAAC,GAAA;MACA,KAAAC,iBAAA;IACA;MACAF,OAAA,CAAAC,GAAA;IACA;;IAEA;IACA;MAAAvC;IAAA,SAAAyC,MAAA,CAAAC,KAAA;IACA,IAAA1C,WAAA;MACA,KAAAV,UAAA,CAAAU,WAAA,GAAAA,WAAA;MACA;MACA,KAAA2C,eAAA,CAAA3C,WAAA;IACA;;IAEA;IACA,KAAA4C,sBAAA;IAEAN,OAAA,CAAAC,GAAA,qCAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;EACA;EACAyD,KAAA;IACA;IACAN,OAAAO,EAAA,EAAAC,IAAA;MACAX,OAAA,CAAAC,GAAA,YAAAU,IAAA,CAAAC,IAAA,MAAAF,EAAA,CAAAE,IAAA;;MAEA;MACA,MAAAC,oBAAA,GAAAH,EAAA,CAAAE,IAAA,CAAAf,QAAA,iCAAAc,IAAA,CAAAC,IAAA,CAAAf,QAAA;MAEA,IAAAgB,oBAAA;QACAb,OAAA,CAAAC,GAAA;QACA,KAAAK,sBAAA;QACA,KAAAQ,qBAAA;MACA,WAAAJ,EAAA,CAAAE,IAAA,CAAAf,QAAA;QACAG,OAAA,CAAAC,GAAA;QACA,KAAAK,sBAAA;MACA;IACA;EACA;EACAS,OAAA;IACA;IACAD,sBAAA;MACA;MACA,KAAAf,uBAAA;;MAEA;MACAC,OAAA,CAAAC,GAAA,iCAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;IACA;IAEA;IACA+C,wBAAA;MACA;QACA,MAAAiB,eAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,IAAAF,eAAA;UACA,MAAAG,UAAA,GAAAZ,IAAA,CAAAa,KAAA,CAAAJ,eAAA;UACAhB,OAAA,CAAAC,GAAA,uCAAAM,IAAA,CAAAC,SAAA,CAAAW,UAAA;;UAEA;UACA,WAAAE,GAAA,IAAAF,UAAA;YACA,IAAAA,UAAA,CAAAE,GAAA,cAAAF,UAAA,CAAAE,GAAA,MAAAC,SAAA,IAAAH,UAAA,CAAAE,GAAA;cACA,KAAArE,UAAA,CAAAqE,GAAA,IAAAF,UAAA,CAAAE,GAAA;YACA;UACA;UAEArB,OAAA,CAAAC,GAAA,4BAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;UACA;QACA;MACA,SAAAuE,KAAA;QACAvB,OAAA,CAAAuB,KAAA,yCAAAA,KAAA;MACA;MACA;IACA;IAEA;IACAC,eAAAC,IAAA;MACA;MACA,KAAA1E,UAAA,GAAA0E,IAAA;;MAEA;MACA,KAAAC,SAAA;QACA1B,OAAA,CAAAC,GAAA,YAAAwB,IAAA;;QAEA;QACA,MAAAb,IAAA,mCAAAa,IAAA;QACA,MAAArB,KAAA;UAAA1C,WAAA,OAAAV,UAAA,CAAAU,WAAA;QAAA;;QAEA;QACA,SAAAyC,MAAA,CAAAS,IAAA,KAAAA,IAAA;UACAZ,OAAA,CAAAC,GAAA,SAAAW,IAAA;UACA;UACA,KAAAe,OAAA,CAAAC,OAAA;YAAAhB,IAAA;YAAAR;UAAA,GAAAyB,KAAA,CAAAC,GAAA;YACA;YACA9B,OAAA,CAAAC,GAAA,aAAA6B,GAAA,CAAAC,OAAA;UACA;QACA;UACA;UACA,KAAAJ,OAAA,CAAAK,IAAA;YAAApB,IAAA;YAAAR;UAAA,GAAAyB,KAAA,CAAAC,GAAA;YACA;YACA9B,OAAA,CAAAC,GAAA,aAAA6B,GAAA,CAAAC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAzB,uBAAA;MACA,MAAAM,IAAA,QAAAT,MAAA,CAAAS,IAAA;MACA,IAAAA,IAAA,CAAAf,QAAA;QACA,KAAA9C,UAAA;MACA,WAAA6D,IAAA,CAAAf,QAAA;QACA,KAAA9C,UAAA;MACA,WAAA6D,IAAA,CAAAf,QAAA;QACA,KAAA9C,UAAA;MACA,WAAA6D,IAAA,CAAAf,QAAA;QACA,KAAA9C,UAAA;MACA;IACA;IAEA;IACAsD,gBAAA3C,WAAA;MACA;MACA;MACAsC,OAAA,CAAAC,GAAA,YAAAvC,WAAA;MACA;MACA,KAAAV,UAAA,CAAAmC,WAAA;MACA,KAAAnC,UAAA,CAAAoC,mBAAA;MACA,KAAApC,UAAA,CAAAqC,KAAA;MACA,KAAArC,UAAA,CAAAsC,cAAA;IACA;IAEA;IACAY,kBAAA;MACAF,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAjD,UAAA;QACA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,MAAA;QACAC,WAAA;QACA;QACAC,cAAA;QACAC,QAAA;QACA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA,OAAAyC,MAAA,CAAAC,KAAA,CAAA1C,WAAA;QACAC,WAAA;QACAC,eAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACA;QACAC,YAAA;QACAC,cAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;QACAC,cAAA;QACAC,SAAA;QACAC,eAAA;QACAC,eAAA;QACAC,SAAA;QACAC,QAAA;QACAC,YAAA;QACAC,QAAA;QACA;QACAC,WAAA;QACAC,mBAAA;QACAC,KAAA;QACAC,cAAA;QACA;QACAC,UAAA;QACAC,iBAAA;MACA;;MAEA;MACAyB,YAAA,CAAAgB,UAAA;;MAEA;MACA,SAAA9B,MAAA,CAAAC,KAAA,CAAA1C,WAAA;QACA,KAAA2C,eAAA,MAAAF,MAAA,CAAAC,KAAA,CAAA1C,WAAA;MACA;IACA;IAEA;IACAwE,SAAA;MACAlC,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,wCAAAlD,UAAA;MACAiD,OAAA,CAAAC,GAAA,2CAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;MAEA,SAAAD,UAAA;QACA,MAAAmF,QAAA,QAAAnF,UAAA;QACAiD,OAAA,CAAAC,GAAA,qCAAAiC,QAAA;;QAEA;QACA,KAAAnC,uBAAA;QACAC,OAAA,CAAAC,GAAA,kDAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;;QAEA;QACA,KAAA0E,SAAA;UACA;UACA,KAAA3E,UAAA,GAAAmF,QAAA;;UAEA;UACA,KAAAV,cAAA,CAAAU,QAAA;;UAEA;UACA,KAAAR,SAAA;YACA,KAAAZ,qBAAA;YACAd,OAAA,CAAAC,GAAA,wCAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;YACAgD,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IAEA;IACAkC,SAAAC,MAAA;MACApC,OAAA,CAAAC,GAAA,2BAAAlD,UAAA,SAAAqF,MAAA;MACA,SAAArF,UAAA;QACA,MAAAoF,QAAA,QAAApF,UAAA;QACAiD,OAAA,CAAAC,GAAA,iBAAAkC,QAAA;;QAEA;QACA,KAAApC,uBAAA;;QAEA;QACA,KAAA2B,SAAA;UACA;UACA,KAAA3E,UAAA,GAAAoF,QAAA;;UAEA;UACA,MAAA/B,KAAA;YACA1C,WAAA,OAAAV,UAAA,CAAAU,WAAA;YACA,IAAA0E,MAAA;UACA;;UAEA;UACA,MAAAxB,IAAA,mCAAAuB,QAAA;;UAEA;UACA,KAAAR,OAAA,CAAAK,IAAA;YAAApB,IAAA;YAAAR;UAAA,GAAAyB,KAAA,CAAAC,GAAA;YACA;YACA9B,OAAA,CAAAC,GAAA,aAAA6B,GAAA,CAAAC,OAAA;UACA;;UAEA;UACA,KAAAL,SAAA;YACA,KAAAZ,qBAAA;UACA;QACA;MACA;IACA;IAEA;IACAuB,iBAAAC,IAAA;MACA,KAAAA,IAAA;MAEAtC,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,2BAAAM,IAAA,CAAAC,SAAA,CAAA8B,IAAA;MACAtC,OAAA,CAAAC,GAAA,gCAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;;MAEA;MACA,MAAAuF,YAAA,GAAAhC,IAAA,CAAAC,SAAA,MAAAxD,UAAA;;MAEA;MACA,WAAAqE,GAAA,IAAAiB,IAAA;QACA;QACA,IAAAA,IAAA,CAAAjB,GAAA,cAAAiB,IAAA,CAAAjB,GAAA,MAAAC,SAAA,IAAAgB,IAAA,CAAAjB,GAAA;UACA,KAAArE,UAAA,CAAAqE,GAAA,IAAAiB,IAAA,CAAAjB,GAAA;QACA;MACA;;MAEA;MACA,IAAAiB,IAAA,CAAApF,UAAA,OAAAF,UAAA,CAAAE,UAAA,GAAAoF,IAAA,CAAApF,UAAA;MACA,IAAAoF,IAAA,CAAAnF,YAAA,OAAAH,UAAA,CAAAG,YAAA,GAAAmF,IAAA,CAAAnF,YAAA;MACA,IAAAmF,IAAA,CAAAhF,cAAA,OAAAN,UAAA,CAAAM,cAAA,GAAAgF,IAAA,CAAAhF,cAAA;;MAEA;MACA0C,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,WAAAsC,YAAA;MACAvC,OAAA,CAAAC,GAAA,WAAAM,IAAA,CAAAC,SAAA,MAAAxD,UAAA;MACAgD,OAAA,CAAAC,GAAA,aAAAuC,MAAA,CAAAC,IAAA,CAAAH,IAAA,EAAAI,IAAA;MACA1C,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,yBAAAjD,UAAA,CAAAE,UAAA;MACA8C,OAAA,CAAAC,GAAA,2BAAAjD,UAAA,CAAAG,YAAA;;MAEA;MACA;QACA;QACA,MAAAwF,SAAA,GAAA1B,YAAA,CAAAC,OAAA;QACA,MAAAC,UAAA,GAAAZ,IAAA,CAAAa,KAAA,CAAAuB,SAAA;;QAEA;QACA,MAAAC,OAAA;UAAA,GAAAzB;QAAA;;QAEA;QACA,WAAAE,GAAA,IAAAiB,IAAA;UACA,IAAAA,IAAA,CAAAjB,GAAA,cAAAiB,IAAA,CAAAjB,GAAA,MAAAC,SAAA,IAAAgB,IAAA,CAAAjB,GAAA;YACAuB,OAAA,CAAAvB,GAAA,IAAAiB,IAAA,CAAAjB,GAAA;UACA;QACA;;QAEA;QACAJ,YAAA,CAAA4B,OAAA,uBAAAtC,IAAA,CAAAC,SAAA,CAAAoC,OAAA;QACA5C,OAAA,CAAAC,GAAA,uBAAAM,IAAA,CAAAC,SAAA,CAAAoC,OAAA;;QAEA;QACA,WAAAvB,GAAA,IAAAuB,OAAA;UACA,IAAAA,OAAA,CAAAvB,GAAA,cAAAuB,OAAA,CAAAvB,GAAA,MAAAC,SAAA,IAAAsB,OAAA,CAAAvB,GAAA,aACA,MAAArE,UAAA,CAAAqE,GAAA,UAAArE,UAAA,CAAAqE,GAAA;YACA,KAAArE,UAAA,CAAAqE,GAAA,IAAAuB,OAAA,CAAAvB,GAAA;UACA;QACA;MACA,SAAAyB,CAAA;QACA9C,OAAA,CAAAuB,KAAA,sBAAAuB,CAAA;MACA;;MAEA;MACA,KAAApB,SAAA;QACA,SAAAqB,SAAA,SAAAA,SAAA,CAAAC,MAAA;UACA,KAAAD,SAAA,CAAAE,OAAA,CAAAC,KAAA;YACA,WAAAA,KAAA,CAAAC,iBAAA;cACAnD,OAAA,CAAAC,GAAA;cACAiD,KAAA,CAAAC,iBAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC,OAAA;MACA;MACA,KAAAlD,iBAAA;MACAF,OAAA,CAAAC,GAAA;MAEA,KAAA0B,OAAA,CAAAK,IAAA;QAAAnF,IAAA;MAAA,GAAAgF,KAAA,CAAAC,GAAA;QACA;QACA,IAAAA,GAAA,CAAAjF,IAAA;UACA,MAAAiF,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}