{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractSteps.vue?vue&type=style&index=0&id=32ff7172&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractSteps.vue", "mtime": 1753348142493}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749542423828}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749542425518}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749542425132}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jb250cmFjdC1zdGVwcy1jb250YWluZXIgewogIHBhZGRpbmc6IDIwcHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLnN0ZXBzLWhlYWRlciB7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKfQoKLnN0ZXBzLWhlYWRlciBoMiB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgY29sb3I6ICMzMDMxMzM7Cn0KCi5zdGVwcy1jb250ZW50IHsKICBtYXJnaW46IDMwcHggMDsKICBtaW4taGVpZ2h0OiA0MDBweDsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmOWZhZmM7Cn0KCi5zdGVwcy1hY3Rpb24gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgZ2FwOiAyMHB4OwogIG1hcmdpbi10b3A6IDIwcHg7CiAgcGFkZGluZy10b3A6IDIwcHg7CiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNGU3ZWQ7Cn0K"}, {"version": 3, "sources": ["ContractSteps.vue"], "names": [], "mappings": ";AAkcA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ContractSteps.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\n  <div class=\"contract-steps-container\">\n    <div class=\"steps-header\">\n      <h2>合同生成流程</h2>\n      <el-steps :active=\"activeStep\" finish-status=\"success\" align-center>\n        <el-step title=\"工人身份信息\" description=\"识别身份证信息\"></el-step>\n        <el-step title=\"银行卡信息\" description=\"输入银行卡号\"></el-step>\n        <el-step title=\"合同信息\" description=\"填写合同表单\"></el-step>\n        <el-step title=\"合同预览\" description=\"预览并打印\"></el-step>\n      </el-steps>\n    </div>\n    \n    <div class=\"steps-content\">\n      <router-view \n        @next-step=\"nextStep\" \n        @prev-step=\"prevStep\" \n        :worker-info=\"workerInfo\" \n        @update-worker-info=\"updateWorkerInfo\">\n      </router-view>\n    </div>\n    \n    <div class=\"steps-action\">\n      <el-button @click=\"prevStep\" :disabled=\"activeStep === 1\">上一步</el-button>\n      <el-button type=\"primary\" @click=\"nextStep\" :disabled=\"activeStep === 4\">下一步</el-button>\n      <el-button @click=\"goBack\">返回花名册</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ContractSteps',\n  data() {\n    return {\n      activeStep: 1,\n      workerInfo: {\n        // 工人基本信息\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        // 银行卡信息\n        bankCardNumber: '',\n        bankName: '',\n        // 工作信息\n        teamName: '',\n        teamCode: '',\n        projectCode: '',\n        projectName: '',\n        participantCode: '',\n        participantName: '',\n        jobPosition: '',\n        jobDescription: '',\n        // 合同信息\n        contractType: 0,\n        fixedStartDate: '',\n        fixedEndDate: '',\n        projectStartDate: '',\n        taskStartDate: '',\n        taskEndDate: '',\n        otherTask: '',\n        workLocation: '',\n        salaryType: 1,\n        fixedSalary: 0,\n        timeUnit: '日',\n        timeUnitSalary: 0,\n        pieceUnit: '平方',\n        pieceUnitSalary: 0,\n        otherSalaryForm: '',\n        salaryDay: 15,\n        benefits: [],\n        otherMatters: '',\n        signDate: '',\n        // 公司信息\n        companyName: '',\n        legalRepresentative: '',\n        agent: '',\n        companyAddress: '',\n        // 模板信息\n        templateId: '',\n        templateVariables: []\n      }\n    }\n  },\n  created() {\n    // 检查是否是从合同流程外部进入\n    const fromExternal = !document.referrer.includes('/contract/contract-steps/')\n\n    // 尝试从localStorage恢复数据\n    const dataRestored = this.restoreFromLocalStorage()\n\n    // 只有从外部进入且没有恢复到数据时才清空\n    if (fromExternal && !dataRestored) {\n      console.log('ContractSteps - 从外部进入合同流程，且没有已保存的数据，初始化空数据')\n      this.clearContractData()\n    } else {\n      console.log('ContractSteps - 使用已存在的表单数据')\n    }\n    \n    // 从URL获取项目编码\n    const { projectCode } = this.$route.query\n    if (projectCode) {\n      this.workerInfo.projectCode = projectCode\n      // 可以在这里加载项目信息\n      this.loadProjectInfo(projectCode)\n    }\n    \n    // 根据当前路由设置激活步骤\n    this.setActiveStepFromRoute()\n    \n    console.log('ContractSteps created - 初始化后的数据:', JSON.stringify(this.workerInfo))\n  },\n  watch: {\n    // 监听路由变化，更新激活的步骤\n    $route(to, from) {\n      console.log(`路由变化: 从 ${from.path} 到 ${to.path}`)\n      \n      // 只有在合同流程内部导航时才保留数据\n      const isWithinContractFlow = to.path.includes('/contract/contract-steps/') && from.path.includes('/contract/contract-steps/')\n\n      if (isWithinContractFlow) {\n        console.log('ContractSteps - 在合同流程内部导航，保留数据')\n        this.setActiveStepFromRoute()\n        this.ensureDataConsistency()\n      } else if (to.path.includes('/contract/contract-steps/')) {\n        console.log('ContractSteps - 从外部进入合同流程，设置步骤')\n      this.setActiveStepFromRoute()\n      }\n    }\n  },\n  methods: {\n    // 确保数据一致性\n    ensureDataConsistency() {\n      // 从localStorage恢复数据，确保数据是最新的\n      this.restoreFromLocalStorage()\n      \n      // 添加调试日志\n      console.log('ContractSteps - 确保数据一致性后的数据:', JSON.stringify(this.workerInfo))\n    },\n    \n    // 从localStorage恢复数据\n    restoreFromLocalStorage() {\n      try {\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\n        if (savedWorkerInfo) {\n          const parsedData = JSON.parse(savedWorkerInfo)\n          console.log('ContractSteps - 从localStorage恢复数据:', JSON.stringify(parsedData))\n          \n          // 更新工人信息，保留现有非空值\n          for (const key in parsedData) {\n            if (parsedData[key] !== null && parsedData[key] !== undefined && parsedData[key] !== '') {\n              this.workerInfo[key] = parsedData[key]\n            }\n          }\n          \n          console.log('ContractSteps - 恢复后的数据:', JSON.stringify(this.workerInfo))\n          return true\n        }\n      } catch (error) {\n        console.error('ContractSteps - 从localStorage恢复数据失败:', error)\n      }\n      return false\n    },\n    \n    // 导航到指定步骤\n    navigateToStep(step) {\n      // 设置活动步骤\n      this.activeStep = step\n      \n      // 使用 $nextTick 确保视图已更新\n      this.$nextTick(() => {\n        console.log(`尝试导航到步骤 ${step}`)\n        \n        // 构造目标路径\n        const path = `/contract/contract-steps/step${step}`\n        const query = { projectCode: this.workerInfo.projectCode || '' }\n        \n        // 检查当前路由是否已经是目标路由\n        if (this.$route.path === path) {\n          console.log(`已在路径 ${path}，使用 replace 而非 push`)\n          // 如果已经在相同路径，使用 replace 而非 push\n          this.$router.replace({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航替换被忽略:', err.message)\n          })\n        } else {\n          // 否则使用 push 导航\n          this.$router.push({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航推送被忽略:', err.message)\n          })\n        }\n      })\n    },\n    \n    // 根据路由设置激活步骤\n    setActiveStepFromRoute() {\n      const path = this.$route.path\n      if (path.includes('step1')) {\n        this.activeStep = 1\n      } else if (path.includes('step2')) {\n        this.activeStep = 2\n      } else if (path.includes('step3')) {\n        this.activeStep = 3\n      } else if (path.includes('step4')) {\n        this.activeStep = 4\n      }\n    },\n    \n    // 加载项目信息\n    loadProjectInfo(projectCode) {\n      // 这里可以调用API获取项目信息\n      // 示例代码，实际需要替换为真实API调用\n      console.log('加载项目信息:', projectCode)\n      // 可以设置公司信息的默认值\n      this.workerInfo.companyName = ''\n      this.workerInfo.legalRepresentative = ''\n      this.workerInfo.agent = ''\n      this.workerInfo.companyAddress = ''\n    },\n    \n    // 清空合同数据\n    clearContractData() {\n      console.log('ContractSteps - 清空所有合同相关数据')\n      \n      // 重置工人信息对象到初始状态\n      this.workerInfo = {\n        // 工人基本信息\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        // 银行卡信息\n        bankCardNumber: '',\n        bankName: '',\n        // 工作信息\n        teamName: '',\n        teamCode: '',\n        projectCode: this.$route.query.projectCode || '',\n        projectName: '',\n        participantCode: '',\n        participantName: '',\n        jobPosition: '',\n        jobDescription: '',\n        // 合同信息\n        contractType: 0,\n        fixedStartDate: '',\n        fixedEndDate: '',\n        projectStartDate: '',\n        taskStartDate: '',\n        taskEndDate: '',\n        otherTask: '',\n        workLocation: '',\n        salaryType: 1,\n        fixedSalary: 0,\n        timeUnit: '日',\n        timeUnitSalary: 0,\n        pieceUnit: '平方',\n        pieceUnitSalary: 0,\n        otherSalaryForm: '',\n        salaryDay: 15,\n        benefits: [],\n        otherMatters: '',\n        signDate: '',\n        // 公司信息\n        companyName: '',\n        legalRepresentative: '',\n        agent: '',\n        companyAddress: '',\n        // 模板信息\n        templateId: '',\n        templateVariables: []\n      }\n      \n      // 清空localStorage中的数据\n      localStorage.removeItem('contractWorkerInfo')\n      \n      // 如果有公司默认信息，重新加载\n      if (this.$route.query.projectCode) {\n        this.loadProjectInfo(this.$route.query.projectCode)\n      }\n    },\n    \n    // 下一步\n    nextStep() {\n      console.log('=== ContractSteps nextStep 开始 ===')\n      console.log('ContractSteps nextStep - 当前步骤:', this.activeStep)\n      console.log('ContractSteps nextStep - 当前workerInfo:', JSON.stringify(this.workerInfo))\n\n      if (this.activeStep < 4) {\n        const nextStep = this.activeStep + 1\n        console.log('ContractSteps nextStep - 将导航到步骤:', nextStep)\n\n        // 先确保数据已从localStorage同步\n        this.restoreFromLocalStorage()\n        console.log('ContractSteps nextStep - 从localStorage恢复后的数据:', JSON.stringify(this.workerInfo))\n\n        // 强制更新后再导航\n        this.$nextTick(() => {\n          // 确保激活步骤已更新\n          this.activeStep = nextStep\n\n          // 导航到下一步\n          this.navigateToStep(nextStep)\n\n          // 在导航后再次确保数据一致\n          this.$nextTick(() => {\n            this.ensureDataConsistency()\n            console.log('ContractSteps nextStep - 导航完成，最终数据:', JSON.stringify(this.workerInfo))\n            console.log('=== ContractSteps nextStep 结束 ===')\n          })\n        })\n      }\n    },\n    \n    // 上一步\n    prevStep(params) {\n      console.log('父组件: 触发上一步, 当前步骤:', this.activeStep, '参数:', params)\n      if (this.activeStep > 1) {\n        const prevStep = this.activeStep - 1\n        console.log('父组件: 将导航到步骤:', prevStep)\n        \n        // 先确保数据已从localStorage同步\n        this.restoreFromLocalStorage()\n        \n        // 强制更新后再导航\n        this.$nextTick(() => {\n          // 确保激活步骤已更新\n          this.activeStep = prevStep\n          \n          // 导航到上一步，传递query参数\n          const query = { \n            projectCode: this.workerInfo.projectCode || '',\n            ...(params || {}) // 合并传入的参数\n          }\n          \n          // 构造目标路径\n          const path = `/contract/contract-steps/step${prevStep}`\n          \n          // 导航到上一步\n          this.$router.push({ path, query }).catch(err => {\n            // 忽略导航错误\n            console.log('导航推送被忽略:', err.message)\n          })\n          \n          // 在导航后再次确保数据一致\n          this.$nextTick(() => {\n            this.ensureDataConsistency()\n          })\n        })\n      }\n    },\n    \n    // 更新工人信息\n    updateWorkerInfo(info) {\n      if (!info) return;\n\n      console.log('=== ContractSteps updateWorkerInfo 开始 ===')\n      console.log('ContractSteps 接收到更新请求:', JSON.stringify(info))\n      console.log('ContractSteps 当前workerInfo:', JSON.stringify(this.workerInfo))\n\n      // 保存更新前的数据\n      const beforeUpdate = JSON.stringify(this.workerInfo)\n\n      // 防止空值覆盖已有的有效值\n      for (const key in info) {\n        // 只有当新值不为空或未定义时才更新\n        if (info[key] !== null && info[key] !== undefined && info[key] !== '') {\n          this.workerInfo[key] = info[key]\n        }\n      }\n\n      // 直接确保关键字段已正确更新\n      if (info.workerName) this.workerInfo.workerName = info.workerName;\n      if (info.idCardNumber) this.workerInfo.idCardNumber = info.idCardNumber;\n      if (info.bankCardNumber) this.workerInfo.bankCardNumber = info.bankCardNumber;\n\n      // 打印详细的数据对比\n      console.log('ContractSteps 数据更新结果:')\n      console.log('- 更新前:', beforeUpdate)\n      console.log('- 更新后:', JSON.stringify(this.workerInfo))\n      console.log('- 更新的字段:', Object.keys(info).join(', '))\n      console.log('- 关键字段检查:')\n      console.log('  * workerName:', this.workerInfo.workerName)\n      console.log('  * idCardNumber:', this.workerInfo.idCardNumber)\n      \n      // 确保localStorage也被更新\n      try {\n        // 从localStorage获取当前数据\n        const savedData = localStorage.getItem('contractWorkerInfo') || '{}'\n        const parsedData = JSON.parse(savedData)\n        \n        // 合并现有数据和更新的数据\n        const newData = { ...parsedData }\n        \n        // 只更新有值的字段\n        for (const key in info) {\n          if (info[key] !== null && info[key] !== undefined && info[key] !== '') {\n            newData[key] = info[key]\n          }\n        }\n        \n        // 保存回localStorage\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(newData))\n        console.log('已更新localStorage数据:', JSON.stringify(newData))\n        \n        // 再次确保this.workerInfo包含所有localStorage中的数据\n        for (const key in newData) {\n          if (newData[key] !== null && newData[key] !== undefined && newData[key] !== '' &&\n              (!this.workerInfo[key] || this.workerInfo[key] === '')) {\n            this.workerInfo[key] = newData[key]\n          }\n        }\n      } catch (e) {\n        console.error('更新localStorage失败:', e)\n      }\n      \n      // 强制子组件更新\n      this.$nextTick(() => {\n        if (this.$children && this.$children.length) {\n          this.$children.forEach(child => {\n            if (typeof child.initFormFromProps === 'function') {\n              console.log('强制更新子组件数据')\n              child.initFormFromProps()\n            }\n          })\n        }\n      })\n    },\n    \n    // 返回花名册\n    goBack() {\n      // 清空合同数据\n      this.clearContractData()\n      console.log('ContractSteps - 返回花名册，清空所有数据')\n      \n      this.$router.push({ name: 'RosterList' }).catch(err => {\n        // 忽略重复导航错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.contract-steps-container {\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.steps-header {\n  margin-bottom: 30px;\n}\n\n.steps-header h2 {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #303133;\n}\n\n.steps-content {\n  margin: 30px 0;\n  min-height: 400px;\n  padding: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background-color: #f9fafc;\n}\n\n.steps-action {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style> "]}]}