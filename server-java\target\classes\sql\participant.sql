-- 创建参建单位表
CREATE TABLE IF NOT EXISTS `participant` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `participant_name` varchar(200) NOT NULL COMMENT '参建单位名称',
  `participant_code` varchar(50) NOT NULL COMMENT '参建单位code',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `legal_representative` varchar(100) DEFAULT NULL COMMENT '法定代表人',
  `entrusted_agent` varchar(100) DEFAULT NULL COMMENT '委托代理人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_participant_code` (`participant_code`),
  <PERSON><PERSON>Y `idx_participant_name` (`participant_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参建单位表';

