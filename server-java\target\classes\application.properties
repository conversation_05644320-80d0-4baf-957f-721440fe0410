server.port=8080

spring.datasource.url=*****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
#spring.datasource.password=sjDIRcls1314!!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.min-idle=1
spring.datasource.druid.max-active=200
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=0
spring.datasource.druid.stat-view-servlet.enabled=false

mybatis.configuration.map-underscore-to-camel-case=true
mybatis-plus.mapper-locations=classpath*:mapper/*.xml

spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER

proxy.password.secret=!@#$%^&dshajdhask
proxy.jwt.TOKEN_SECRET=HJKSD$%^&**()
proxy.jwt.EXPIRE_TIME=2147483646

spring.main.allow-circular-references=true

