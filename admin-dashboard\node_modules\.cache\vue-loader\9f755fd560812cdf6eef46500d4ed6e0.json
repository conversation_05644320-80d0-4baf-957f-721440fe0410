{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep2.vue?vue&type=style&index=0&id=2f6b79b1&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep2.vue", "mtime": 1753347546119}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749542423828}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749542425518}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749542425132}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractStep2.vue"], "names": [], "mappings": ";AA4oBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ContractStep2.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\r\n  <div class=\"contract-step2-container\">\r\n    <h3>步骤2：银行卡信息</h3>\r\n    \r\n    <div class=\"bank-info-form\">\r\n      <el-form :model=\"bankForm\" label-width=\"120px\" ref=\"bankForm\" :rules=\"rules\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\r\n              <el-input v-model=\"bankForm.workerName\" placeholder=\"请输入工人姓名\" disabled></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\r\n              <el-input v-model=\"bankForm.idCardNumber\" placeholder=\"请输入证件号码\" disabled></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行卡号\" prop=\"bankCardNumber\">\r\n              <el-input v-model=\"bankForm.bankCardNumber\" placeholder=\"请输入银行卡号\" maxlength=\"23\" @input=\"formatBankCardNumber\" @blur=\"queryBankInfo\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行名称\" prop=\"bankName\">\r\n              <el-input v-model=\"bankForm.bankName\" placeholder=\"银行名称将自动识别\" readonly></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 删除调试信息显示区域 -->\r\n      \r\n      <div class=\"bank-card-preview\">\r\n        <div class=\"card-container\" :class=\"getBankCardClass()\">\r\n          <div class=\"card-header\">\r\n            <div class=\"bank-logo\">{{ getBankLogo() }}</div>\r\n            <div class=\"bank-name\">{{ bankForm.bankName || '银行卡' }}</div>\r\n          </div>\r\n          <div class=\"card-number\">{{ formatDisplayCardNumber() }}</div>\r\n          <div class=\"card-footer\">\r\n            <div class=\"card-holder\">持卡人：{{ bankForm.workerName || '未填写' }}</div>\r\n            <div class=\"card-valid\">有效期：长期</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-actions\">\r\n        <el-button @click=\"prevStep\">上一步</el-button>\r\n        <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 银行选择对话框 -->\r\n    <el-dialog title=\"选择开户银行\" :visible.sync=\"bankSelectorVisible\" width=\"50%\">\r\n      <div class=\"bank-search\">\r\n        <el-input\r\n          placeholder=\"请输入银行名称或代码\"\r\n          v-model=\"bankSearchKeyword\"\r\n          clearable\r\n          @keyup.enter.native=\"searchBanks\"\r\n        >\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchBanks\"></el-button>\r\n        </el-input>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bankListLoading\"\r\n        :data=\"pagedBankList\"\r\n        border\r\n        style=\"width: 100%; margin-top: 15px;\"\r\n        height=\"400px\"\r\n        @row-click=\"handleRowClick\"\r\n        @row-dblclick=\"handleRowDblClick\"\r\n        highlight-current-row\r\n        ref=\"bankTable\"\r\n      >\r\n        <el-table-column width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio :label=\"scope.row.id\" v-model=\"selectedBankId\" @change.native=\"handleRadioChange(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bankName\" label=\"银行名称\" min-width=\"200\"></el-table-column>\r\n        <el-table-column prop=\"bankCode\" label=\"银行代码\" width=\"150\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"bankListQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"bankListQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"totalBankCount\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bankSelectorVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBankSelection\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getBankInfoList } from '@/api/bankInfo'\r\nimport { getBankInfoByBin } from '@/api/bankCardBin'\r\n\r\nexport default {\r\n  name: 'ContractStep2',\r\n  props: {\r\n    workerInfo: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    // 银行卡号验证\r\n    const validateBankCard = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请输入银行卡号'))\r\n      }\r\n      \r\n      // 移除空格后验证\r\n      const cardNumber = value.replace(/\\s/g, '')\r\n      if (!/^\\d{16,19}$/.test(cardNumber)) {\r\n        return callback(new Error('银行卡号格式不正确，应为16-19位数字'))\r\n      }\r\n      \r\n      callback()\r\n    }\r\n    \r\n    // 证件号码验证\r\n    const validateIdCard = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请输入证件号码'))\r\n      }\r\n      \r\n      // 简单验证18位或15位身份证号\r\n      // const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\r\n      // if (!reg.test(value)) {\r\n      //   return callback(new Error('证件号码格式不正确'))\r\n      // }\r\n      \r\n      callback()\r\n    }\r\n    \r\n    return {\r\n      bankForm: {\r\n        workerName: '',\r\n        idCardNumber: '',\r\n        bankCardNumber: '',\r\n        bankName: '',\r\n        bankCode: ''\r\n      },\r\n      rules: {\r\n        workerName: [\r\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\r\n        ],\r\n        idCardNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ],\r\n        bankCardNumber: [\r\n          { required: true, message: '请输入银行卡号', trigger: 'blur' },\r\n          { validator: validateBankCard, trigger: 'blur' }\r\n        ],\r\n        bankName: [\r\n          { required: true, message: '银行名称不能为空', trigger: 'change' }\r\n        ]\r\n      },\r\n      // 银行选择相关数据\r\n      bankSelectorVisible: false,\r\n      bankListLoading: false,\r\n      bankList: [],\r\n      bankSearchKeyword: '',\r\n      selectedBank: null,\r\n      selectedBankId: null, // 添加selectedBankId用于单选按钮\r\n      bankListQuery: {\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      totalBankCount: 0 // 添加总银行数量字段\r\n    }\r\n  },\r\n  computed: {\r\n    filteredBankList() {\r\n      return this.bankList;\r\n    },\r\n    pagedBankList() {\r\n      return this.bankList;\r\n    }\r\n  },\r\n  created() {\r\n    // 添加详细的调试日志\r\n    console.log('=== ContractStep2 created 开始 ===')\r\n    console.log('ContractStep2 created - 接收到的props:', JSON.stringify(this.workerInfo))\r\n    console.log('ContractStep2 created - 初始表单数据:', JSON.stringify(this.bankForm))\r\n\r\n    // 首先尝试从props初始化表单\r\n    this.initFormFromProps()\r\n\r\n    // 如果props中没有姓名和证件号码，尝试从localStorage恢复\r\n    if (!this.bankForm.workerName || !this.bankForm.idCardNumber) {\r\n      console.log('ContractStep2 created - props中缺少数据，尝试从localStorage恢复')\r\n      // false表示不要emit更新父组件\r\n      this.tryRestoreFromLocalStorage(false)\r\n    }\r\n\r\n    // 添加调试日志\r\n    console.log('ContractStep2 created - 最终表单数据:', JSON.stringify(this.bankForm))\r\n    console.log('=== ContractStep2 created 结束 ===')\r\n  },\r\n  mounted() {\r\n    // 在组件挂载后，强制刷新表单数据\r\n    this.$nextTick(() => {\r\n      // 确保数据已经正确初始化\r\n      if ((!this.bankForm.workerName || !this.bankForm.idCardNumber) && this.workerInfo) {\r\n        console.log('ContractStep2 mounted - 重新从props初始化数据')\r\n        this.initFormFromProps()\r\n      }\r\n      \r\n      // 如果还是没有数据，尝试从localStorage恢复\r\n      if (!this.bankForm.workerName || !this.bankForm.idCardNumber) {\r\n        console.log('ContractStep2 mounted - 尝试从localStorage恢复数据')\r\n        this.tryRestoreFromLocalStorage(true)\r\n      }\r\n      \r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n      \r\n      // 添加调试日志\r\n      console.log('ContractStep2 mounted - 刷新后的表单数据:', JSON.stringify(this.bankForm))\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 只有当完全离开合同流程时才清空数据\r\n    // 不要在步骤之间导航时清空\r\n    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {\r\n      console.log('ContractStep2 - 离开合同流程，清空数据')\r\n      localStorage.removeItem('contractWorkerInfo')\r\n    } else {\r\n      console.log('ContractStep2 - 在合同流程内导航，保留数据')\r\n      // 确保数据已保存到localStorage\r\n      this.saveToLocalStorage()\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听 workerInfo 变化\r\n    workerInfo: {\r\n      handler(newVal, oldVal) {\r\n        // 只在有实质变化时处理\r\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\r\n          console.log('ContractStep2 - workerInfo变化:', JSON.stringify(newVal))\r\n          \r\n          // 如果新的 workerInfo 包含有效的工人信息，初始化表单\r\n          if (newVal?.workerName || newVal?.idCardNumber) {\r\n            this.$nextTick(() => {\r\n              this.initFormFromProps();\r\n              console.log('ContractStep2 - 从props更新后的表单数据:', JSON.stringify(this.bankForm))\r\n            });\r\n          }\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true // 立即触发，确保组件创建时就处理props\r\n    }\r\n  },\r\n  methods: {\r\n    // 显示银行选择器\r\n    showBankSelector() {\r\n      this.bankSelectorVisible = true;\r\n      this.bankSearchKeyword = '';\r\n      this.loadBankList();\r\n    },\r\n    \r\n    // 加载银行列表\r\n    loadBankList() {\r\n      this.bankListLoading = true;\r\n      getBankInfoList({\r\n        page: this.bankListQuery.page,\r\n        limit: this.bankListQuery.limit,\r\n        bankName: this.bankSearchKeyword,  // 添加搜索关键词\r\n        bankCode: this.bankSearchKeyword   // 添加搜索关键词\r\n      }).then(response => {\r\n        if (response.code === 0) {\r\n          this.bankList = response.data || [];\r\n          this.totalBankCount = response.count || 0; // 设置总记录数\r\n        } else {\r\n          this.$message.error(response.msg || '获取银行列表失败');\r\n          this.bankList = [];\r\n          this.totalBankCount = 0;\r\n        }\r\n        this.bankListLoading = false;\r\n      }).catch(() => {\r\n        this.bankListLoading = false;\r\n        this.$message.error('获取银行列表失败');\r\n      });\r\n    },\r\n    \r\n    // 搜索银行\r\n    searchBanks() {\r\n      // 重置到第一页\r\n      this.bankListQuery.page = 1;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 处理每页条数变化\r\n    handleSizeChange(val) {\r\n      this.bankListQuery.limit = val;\r\n      this.bankListQuery.page = 1;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.bankListQuery.page = val;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 控制行是否可选\r\n    rowSelectable(row, index) {\r\n      return true; // 所有行都可选\r\n    },\r\n    \r\n    // 处理单选按钮变化\r\n    handleRadioChange(row) {\r\n      this.selectBank(row);\r\n    },\r\n    \r\n    // 选择银行\r\n    selectBank(row) {\r\n      this.selectedBank = row;\r\n      this.selectedBankId = row.id;\r\n      // 设置当前行高亮\r\n      this.$refs.bankTable.setCurrentRow(row);\r\n    },\r\n    \r\n    // 处理行点击事件\r\n    handleRowClick(row) {\r\n      this.selectBank(row);\r\n    },\r\n    \r\n    // 处理行双击事件\r\n    handleRowDblClick(row) {\r\n      this.selectBank(row);\r\n      this.confirmBankSelection();\r\n    },\r\n    \r\n    // 确认银行选择\r\n    confirmBankSelection() {\r\n      if (this.selectedBank) {\r\n        this.bankForm.bankName = this.selectedBank.bankName;\r\n        this.bankForm.bankCode = this.selectedBank.bankCode;\r\n        this.bankSelectorVisible = false;\r\n      } else {\r\n        this.$message.warning('请选择一家银行');\r\n      }\r\n    },\r\n    \r\n    // 根据银行卡号查询银行信息\r\n    queryBankInfo() {\r\n      const cardNumber = this.bankForm.bankCardNumber.replace(/\\s/g, '');\r\n      if (cardNumber.length >= 3) {  // 改为3位就开始查询\r\n        getBankInfoByBin(cardNumber).then(response => {  // 传入完整卡号，让后端处理\r\n          if (response.code === 0 && response.data) {\r\n            this.bankForm.bankName = response.data.bank;\r\n            this.bankForm.bankCode = response.data.bankCode;\r\n          } else {\r\n            this.bankForm.bankName = '';\r\n            this.bankForm.bankCode = '';\r\n            // 只有在输入足够位数时才提示未找到\r\n            if (cardNumber.length >= 6) {\r\n              this.$message.warning('未找到对应的银行信息');\r\n            }\r\n          }\r\n        }).catch(() => {\r\n          this.bankForm.bankName = '';\r\n          this.bankForm.bankCode = '';\r\n          if (cardNumber.length >= 6) {\r\n            this.$message.error('查询银行信息失败');\r\n          }\r\n        });\r\n      } else {\r\n        this.bankForm.bankName = '';\r\n        this.bankForm.bankCode = '';\r\n      }\r\n    },\r\n    \r\n    // 获取银行卡样式类\r\n    getBankCardClass() {\r\n      if (!this.bankForm.bankName) return 'default-card';\r\n      \r\n      // 根据银行名称返回对应的样式类\r\n      const bankNameMap = {\r\n        '中国工商银行': 'icbc-card',\r\n        '中国农业银行': 'abc-card',\r\n        '中国建设银行': 'ccb-card',\r\n        '中国银行': 'boc-card',\r\n        '交通银行': 'bocom-card',\r\n        '中国邮政储蓄银行': 'psbc-card',\r\n        '招商银行': 'cmb-card',\r\n        '中信银行': 'citic-card',\r\n        '上海浦东发展银行': 'spdb-card',\r\n        '兴业银行': 'cib-card',\r\n        '中国光大银行': 'ceb-card',\r\n        '中国民生银行': 'cmbc-card',\r\n        '华夏银行': 'hxb-card',\r\n        '广发银行': 'cgb-card',\r\n        '平安银行': 'pab-card'\r\n      };\r\n      \r\n      return bankNameMap[this.bankForm.bankName] || 'default-card';\r\n    },\r\n    \r\n    // 获取银行Logo\r\n    getBankLogo() {\r\n      if (!this.bankForm.bankName) return '银';\r\n      \r\n      // 返回银行名称的第一个字\r\n      return this.bankForm.bankName.charAt(0);\r\n    },\r\n    \r\n    // 清空合同数据\r\n    clearContractData() {\r\n      console.log('ContractStep2 - 清空合同数据')\r\n      localStorage.removeItem('contractWorkerInfo')\r\n    },\r\n    \r\n    // 尝试从localStorage恢复数据\r\n    tryRestoreFromLocalStorage(shouldEmit = true) {\r\n      try {\r\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\r\n        if (savedWorkerInfo) {\r\n          const parsedData = JSON.parse(savedWorkerInfo)\r\n          console.log('ContractStep2 - 从本地存储恢复数据:', JSON.stringify(parsedData))\r\n          \r\n          // 更新表单数据\r\n          this.bankForm.workerName = parsedData.workerName || this.bankForm.workerName || '';\r\n          this.bankForm.idCardNumber = parsedData.idCardNumber || this.bankForm.idCardNumber || '';\r\n          this.bankForm.bankCardNumber = parsedData.bankCardNumber || this.bankForm.bankCardNumber || '';\r\n          this.bankForm.bankName = parsedData.bankName || this.bankForm.bankName || '';\r\n          this.bankForm.bankCode = parsedData.bankCode || this.bankForm.bankCode || '';\r\n          \r\n          // 只在需要时更新父组件数据\r\n          if (shouldEmit && (parsedData.workerName || parsedData.idCardNumber)) {\r\n            this.$emit('update-worker-info', { ...parsedData })\r\n          }\r\n          \r\n          return true;\r\n        }\r\n      } catch (error) {\r\n        console.error('ContractStep2 - 从本地存储恢复数据失败:', error)\r\n      }\r\n      return false;\r\n    },\r\n    \r\n    // 保存数据到localStorage\r\n    saveToLocalStorage() {\r\n      try {\r\n        // 确保保存的数据包含所有必要字段\r\n        const existingData = {};\r\n        try {\r\n          const saved = localStorage.getItem('contractWorkerInfo');\r\n          if (saved) {\r\n            Object.assign(existingData, JSON.parse(saved));\r\n          }\r\n        } catch (e) {\r\n          console.error('解析已存储数据失败:', e)\r\n        }\r\n        \r\n        // 合并现有数据和当前表单数据\r\n        const dataToSave = { \r\n          ...existingData,\r\n          // 确保工人和银行卡信息都被保存\r\n          workerName: this.bankForm.workerName || existingData.workerName || '',\r\n          idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n          bankCardNumber: this.bankForm.bankCardNumber || '',\r\n          bankName: this.bankForm.bankName || '',\r\n          bankCode: this.bankForm.bankCode || ''\r\n        };\r\n        \r\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave));\r\n        console.log('ContractStep2 - 已保存数据到localStorage:', JSON.stringify(dataToSave))\r\n        return dataToSave;\r\n      } catch (error) {\r\n        console.error('ContractStep2 - 保存到本地存储失败:', error);\r\n        return this.bankForm;\r\n      }\r\n    },\r\n    \r\n    // 从props初始化表单\r\n    initFormFromProps() {\r\n      // 直接从props中获取数据\r\n      if (this.workerInfo) {\r\n        console.log('ContractStep2 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))\r\n        \r\n        // 防止覆盖已有的有效数据\r\n        if (this.workerInfo.workerName) this.bankForm.workerName = this.workerInfo.workerName;\r\n        if (this.workerInfo.idCardNumber) this.bankForm.idCardNumber = this.workerInfo.idCardNumber;\r\n        if (this.workerInfo.bankCardNumber) this.bankForm.bankCardNumber = this.workerInfo.bankCardNumber;\r\n        if (this.workerInfo.bankName) this.bankForm.bankName = this.workerInfo.bankName;\r\n        if (this.workerInfo.bankCode) this.bankForm.bankCode = this.workerInfo.bankCode;\r\n        \r\n        // 如果从props中获取到了有效的工人信息，保存到localStorage\r\n        if (this.workerInfo.workerName || this.workerInfo.idCardNumber) {\r\n          this.saveToLocalStorage();\r\n        }\r\n        \r\n        // 强制更新视图\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 格式化银行卡号（每4位添加空格）\r\n    formatBankCardNumber(value) {\r\n      if (!value) return\r\n      \r\n      // 移除所有空格\r\n      let cardNumber = value.replace(/\\s/g, '')\r\n      \r\n      // 限制只能输入数字\r\n      cardNumber = cardNumber.replace(/[^\\d]/g, '')\r\n      \r\n      // 限制最大长度为19位\r\n      cardNumber = cardNumber.substring(0, 19)\r\n      \r\n      // 每4位添加空格\r\n      let formattedNumber = ''\r\n      for (let i = 0; i < cardNumber.length; i++) {\r\n        if (i > 0 && i % 4 === 0) {\r\n          formattedNumber += ' '\r\n        }\r\n        formattedNumber += cardNumber.charAt(i)\r\n      }\r\n      \r\n      // 更新表单值\r\n      this.bankForm.bankCardNumber = formattedNumber\r\n      \r\n      // 如果输入了足够的位数，自动查询银行信息\r\n      if (cardNumber.length >= 3) {  // 改为3位就开始查询\r\n        this.queryBankInfo();\r\n      } else {\r\n        this.bankForm.bankName = '';\r\n        this.bankForm.bankCode = '';\r\n      }\r\n    },\r\n    \r\n    // 获取银行卡预览显示的卡号\r\n    formatDisplayCardNumber() {\r\n      if (!this.bankForm.bankCardNumber) {\r\n        return '**** **** **** ****'\r\n      }\r\n      \r\n      return this.bankForm.bankCardNumber\r\n    },\r\n    \r\n    // 上一步\r\n    prevStep() {\r\n      // 准备要保存的数据\r\n      const existingData = {};\r\n      try {\r\n        const saved = localStorage.getItem('contractWorkerInfo');\r\n        if (saved) {\r\n          Object.assign(existingData, JSON.parse(saved));\r\n        }\r\n      } catch (e) {}\r\n      \r\n      // 合并现有数据和当前表单数据\r\n      const bankInfo = {\r\n        ...existingData, // 保留其他字段的数据\r\n        // 确保工人的基本信息和银行卡信息被保存\r\n        workerName: this.bankForm.workerName || existingData.workerName || '',\r\n        idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n        bankCardNumber: this.bankForm.bankCardNumber || '',\r\n        bankName: this.bankForm.bankName || '',\r\n        bankCode: this.bankForm.bankCode || ''\r\n      };\r\n      \r\n      // 保存到本地存储\r\n      localStorage.setItem('contractWorkerInfo', JSON.stringify(bankInfo));\r\n      \r\n      // 先更新数据，再通知父组件导航\r\n      this.$emit('update-worker-info', bankInfo);\r\n      \r\n      // 等待数据更新后再导航\r\n      this.$nextTick(() => {\r\n        // 发送上一步事件，由父组件处理导航\r\n        this.$emit('prev-step');\r\n      });\r\n    },\r\n    \r\n    // 下一步\r\n    nextStep() {\r\n      this.$refs.bankForm.validate(valid => {\r\n        if (valid) {\r\n          // 准备要保存的数据\r\n          const existingData = {};\r\n          try {\r\n            const saved = localStorage.getItem('contractWorkerInfo');\r\n            if (saved) {\r\n              Object.assign(existingData, JSON.parse(saved));\r\n            }\r\n          } catch (e) {}\r\n          \r\n          // 合并现有数据和当前表单数据\r\n          const bankInfo = {\r\n            ...existingData, // 保留其他字段的数据\r\n            // 确保工人的基本信息也被保存\r\n            workerName: this.bankForm.workerName || existingData.workerName || '',\r\n            idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n            gender: existingData.gender || '',\r\n            homeAddress: existingData.homeAddress || '',\r\n            teamName: existingData.teamName || '',\r\n            jobPosition: existingData.jobPosition || '',\r\n            participantName: existingData.participantName || '',\r\n            // 银行卡信息\r\n            bankCardNumber: this.bankForm.bankCardNumber || '',\r\n            bankName: this.bankForm.bankName || '',\r\n            bankCode: this.bankForm.bankCode || ''\r\n          };\r\n          \r\n          // 保存到本地存储\r\n          localStorage.setItem('contractWorkerInfo', JSON.stringify(bankInfo));\r\n          \r\n          // 更新父组件中的工人信息\r\n          this.$emit('update-worker-info', bankInfo);\r\n          \r\n          // 等待数据更新后再导航\r\n          this.$nextTick(() => {\r\n            // 发送下一步事件，由父组件处理导航\r\n            this.$emit('next-step');\r\n          });\r\n        } else {\r\n          this.$message.warning('请完善银行卡信息');\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contract-step2-container {\r\n  padding: 20px;\r\n}\r\n\r\n.contract-step2-container h3 {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  color: #303133;\r\n}\r\n\r\n.bank-info-form {\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.bank-card-preview {\r\n  margin: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.card-container {\r\n  width: 360px;\r\n  height: 220px;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  background: linear-gradient(135deg, #0a4b78, #0a78a1);\r\n  color: white;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-container::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: -50px;\r\n  right: -50px;\r\n  width: 150px;\r\n  height: 150px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  z-index: 1;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 2;\r\n}\r\n\r\n.bank-logo {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-right: 15px;\r\n}\r\n\r\n.bank-name {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.card-number {\r\n  font-size: 22px;\r\n  letter-spacing: 2px;\r\n  font-family: 'Courier New', monospace;\r\n  text-align: center;\r\n  margin: 20px 0;\r\n  z-index: 2;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  z-index: 2;\r\n}\r\n\r\n/* 银行卡样式 */\r\n.icbc-card {\r\n  background: linear-gradient(135deg, #d10b0b, #ff5252);\r\n}\r\n\r\n.abc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.ccb-card {\r\n  background: linear-gradient(135deg, #003399, #0066cc);\r\n}\r\n\r\n.boc-card {\r\n  background: linear-gradient(135deg, #8b0000, #cc0000);\r\n}\r\n\r\n.bocom-card {\r\n  background: linear-gradient(135deg, #0066cc, #3399ff);\r\n}\r\n\r\n.psbc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.cmb-card {\r\n  background: linear-gradient(135deg, #cc0000, #ff3333);\r\n}\r\n\r\n.citic-card {\r\n  background: linear-gradient(135deg, #003366, #0066cc);\r\n}\r\n\r\n.spdb-card {\r\n  background: linear-gradient(135deg, #006699, #0099cc);\r\n}\r\n\r\n.cib-card {\r\n  background: linear-gradient(135deg, #003366, #0066cc);\r\n}\r\n\r\n.ceb-card {\r\n  background: linear-gradient(135deg, #990000, #cc3333);\r\n}\r\n\r\n.cmbc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.hxb-card {\r\n  background: linear-gradient(135deg, #990000, #cc3333);\r\n}\r\n\r\n.cgb-card {\r\n  background: linear-gradient(135deg, #cc0000, #ff3333);\r\n}\r\n\r\n.pab-card {\r\n  background: linear-gradient(135deg, #cc6600, #ff9933);\r\n}\r\n\r\n.default-card {\r\n  background: linear-gradient(135deg, #0a4b78, #0a78a1);\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 银行选择对话框样式 */\r\n.bank-search {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .card-container {\r\n    width: 300px;\r\n    height: 180px;\r\n    padding: 15px;\r\n  }\r\n  \r\n  .bank-logo {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .bank-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .card-number {\r\n    font-size: 18px;\r\n    margin: 15px 0;\r\n  }\r\n  \r\n  .card-footer {\r\n    font-size: 12px;\r\n  }\r\n}\r\n</style> \r\n"]}]}