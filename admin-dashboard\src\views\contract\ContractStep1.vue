<template>
  <div class="contract-step1-container">
    <h3>步骤1：工人身份信息识别</h3>
    
    <div class="scanner-container">
      <div class="scanner-preview">
        <img id="photo" src="" width="600" height="400" ref="scannerPreview">
      </div>
      
      <div class="scanner-controls">
        <el-button type="primary" @click="startScanner">启动高拍仪</el-button>
        <el-button type="success" @click="scanIdCard">识别身份证</el-button>
        <el-button type="warning" @click="stopScanner">停止高拍仪</el-button>
        <el-upload
          class="upload-button"
          action="#"
          :show-file-list="false"
          :before-upload="handleUploadImage">
          <el-button type="primary" icon="el-icon-upload">上传图片</el-button>
        </el-upload>
      </div>
    </div>
    
    <div class="worker-info-form">
      <el-form :model="workerForm" label-width="120px" ref="workerForm" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工人姓名" prop="workerName">
              <el-input v-model="workerForm.workerName" placeholder="请输入工人姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="idCardNumber">
              <div class="input-with-button">
              <el-input v-model="workerForm.idCardNumber" placeholder="请输入证件号码"></el-input>
                <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshWorkerInfo" :disabled="!workerForm.idCardNumber" title="重新获取信息">刷新</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="workerForm.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="男" value="男"></el-option>
                <el-option label="女" value="女"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭住址" prop="homeAddress">
              <el-input v-model="workerForm.homeAddress" placeholder="请输入家庭住址"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="班组" prop="teamName">
              <el-input v-model="workerForm.teamName" placeholder="请输入班组名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工种" prop="jobPosition">
              <el-input v-model="workerForm.jobPosition" placeholder="请输入工种"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="参建单位" prop="participantName">
              <el-input v-model="workerForm.participantName" placeholder="请输入参建单位"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div class="form-actions">
        <el-button type="primary" @click="searchWorker">查找工人</el-button>
        <el-button type="success" @click="nextStep">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getWorkerDetail, getWorkerByIdCard } from '@/api/roster'
import axios from 'axios'

export default {
  name: 'ContractStep1',
  props: {
    workerInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      webSocket: null,
      scannerConnected: false,
      workerForm: {
        workerId: '',
        workerName: '',
        idCardNumber: '',
        gender: '男',
        homeAddress: '',
        teamName: '',
        teamCode: '',
        participantName: '',
        participantCode: '',
        projectSubContractorId: '',
        projectCode: '',
        projectName: '',
        jobPosition: ''
      },
      rules: {
        workerName: [
          { required: true, message: '请输入工人姓名', trigger: 'blur' }
        ],
        idCardNumber: [
          { required: true, message: '请输入证件号码', trigger: 'blur' }
        ],
        homeAddress: [
          { required: true, message: '请输入家庭住址', trigger: 'blur' }
        ],
        teamName: [
          { required: true, message: '请输入班组名称', trigger: 'blur' }
        ],
        participantName: [
          { required: true, message: '请输入参建单位', trigger: 'blur' }
        ]
      },
      simulationMode: false, // 模拟模式标志
      connectionTimeout: null, // 连接超时
      manuallyDisconnected: false, // 手动断开标志
      scannerConfig: {
        wsUrl: 'ws://localhost:1818', // WebSocket连接地址
        timeout: 3000, // 连接超时时间(毫秒)
        autoSimulate: true, // 连接失败时是否自动切换到模拟模式
        ocrApiUrl: 'http://127.0.0.1:5000/ocr' // OCR API地址
      },
      configDialogVisible: false, // 高拍仪配置对话框可见性
      tempScannerConfig: { // 临时存储的高拍仪配置
        wsUrl: 'ws://localhost:1818',
        timeout: 3000,
        autoSimulate: true,
        ocrApiUrl: 'http://127.0.0.1:5000/ocr'
      },
      currentPhotoPath: '', // 当前拍摄的照片路径
      isProcessingOcr: false, // 是否正在处理OCR
      currentImageData: '', // 当前接收到的图像数据
      waitingForBase64: false // 是否正在等待 Base64Encode 的响应
    }
  },
  created() {
    // 从props初始化表单
    this.initFormFromProps()
    
    // 从URL获取项目编码
    const { projectCode } = this.$route.query
    if (projectCode) {
      this.workerForm.projectCode = projectCode
    }
    
    // 如果props中没有必要的数据，尝试从本地存储恢复
    if (!this.workerForm.workerName && !this.workerForm.idCardNumber) {
      this.tryRestoreFromLocalStorage(false) // 传false参数表示不要emit到父组件
    }
    
    // 添加调试日志
    console.log('ContractStep1 created - 初始化后的表单数据:', JSON.stringify(this.workerForm))
  },
  mounted() {
    // 初始化高拍仪WebSocket连接
    this.initScannerWebSocket()
    
    console.log('ContractStep1 mounted, 当前表单数据:', JSON.stringify(this.workerForm))
  },
  watch: {
    // 监听workerInfo变化
    workerInfo: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          console.log('ContractStep1 - workerInfo变化:', JSON.stringify(newVal))
          
          // 更新表单数据
          this.initFormFromProps()
        }
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    // 组件销毁前关闭WebSocket连接
    this.closeWebSocket()

    // 只有当完全离开合同流程时才清空数据
    // 不要在步骤之间导航时清空
    console.log('ContractStep1 beforeDestroy - 当前路径:', this.$route?.path)
    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {
      console.log('ContractStep1 - 离开合同流程，清空数据')
      this.clearContractData()
    } else {
      console.log('ContractStep1 - 在合同流程内导航，保留数据')
      // 确保数据已保存到localStorage
      this.saveToLocalStorage()
    }
  },
  methods: {
    // 清空合同数据
    clearContractData() {
      console.log('ContractStep1 - 清空合同数据')
      localStorage.removeItem('contractWorkerInfo')
    },
    
    // 尝试从本地存储恢复数据
    tryRestoreFromLocalStorage(shouldEmit = true) {
      try {
        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')
        if (savedWorkerInfo) {
          const parsedData = JSON.parse(savedWorkerInfo)
          console.log('ContractStep1 - 从本地存储恢复数据:', JSON.stringify(parsedData))
          
          // 更新表单数据
          Object.keys(this.workerForm).forEach(key => {
            if (parsedData[key] !== undefined && parsedData[key] !== null) {
              this.workerForm[key] = parsedData[key]
            }
          })
          
          // 只在需要时更新父组件数据
          if (shouldEmit) {
            this.$emit('update-worker-info', { ...parsedData })
          }
          
          return true
        }
      } catch (error) {
        console.error('ContractStep1 - 从本地存储恢复数据失败:', error)
      }
      return false
    },
    
    // 从props初始化表单
    initFormFromProps() {
      if (this.workerInfo) {
        console.log('ContractStep1 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))
        
        // 复制属性到表单，只复制有值的字段
        Object.keys(this.workerForm).forEach(key => {
          if (this.workerInfo[key] !== undefined && this.workerInfo[key] !== null && this.workerInfo[key] !== '') {
            this.workerForm[key] = this.workerInfo[key]
          }
        })
        
        console.log('ContractStep1 - 从props初始化数据后:', JSON.stringify(this.workerForm))
      }
    },
    
    // 初始化高拍仪WebSocket连接
    initScannerWebSocket() {
      try {
        // 添加模拟模式标志
        this.simulationMode = false;
        
        // 尝试连接WebSocket
        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)
        
        // 设置连接超时
        this.connectionTimeout = setTimeout(() => {
          if (!this.scannerConnected) {
            console.warn('高拍仪连接超时，切换到模拟模式');
            if (this.scannerConfig.autoSimulate) {
              this.switchToSimulationMode();
            } else {
              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);
            }
          }
        }, this.scannerConfig.timeout);
        
        this.webSocket.onopen = (event) => {
          console.log('高拍仪WebSocket连接成功')
          this.scannerConnected = true
          this.$message.success('高拍仪连接成功')
          clearTimeout(this.connectionTimeout);
        }
        
        this.webSocket.onclose = (event) => {
          console.log('高拍仪WebSocket连接关闭')
          this.scannerConnected = false
          // 如果不是主动关闭，尝试切换到模拟模式
          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {
            this.switchToSimulationMode();
          }
        }
        
        this.webSocket.onerror = (event) => {
          console.error('高拍仪WebSocket连接错误', event)
          this.scannerConnected = false
          if (this.scannerConfig.autoSimulate) {
            this.switchToSimulationMode();
          } else {
            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);
          }
        }
        
        this.webSocket.onmessage = (event) => {
          this.handleScannerMessage(event)
        }
      } catch (error) {
        console.error('初始化高拍仪WebSocket失败', error)
        if (this.scannerConfig.autoSimulate) {
          this.switchToSimulationMode();
        } else {
          this.$message.error(`初始化高拍仪失败: ${error.message}`);
        }
      }
    },
    
    // 处理高拍仪消息
    handleScannerMessage(event) {
      const begin_data = "data:image/jpeg;base64,"
      
      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {
        // 处理条码识别结果
        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')
        this.$message.success('识别到条码: ' + barcode)
        
        // 如果是身份证号格式，填入表单
        if (this.isIdCardNumber(barcode)) {
          this.workerForm.idCardNumber = barcode
          this.searchWorkerByIdCard(barcode)
        }
      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {
        // 处理保存图片结果
        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')
        this.$message.success('图片保存成功: ' + result)
      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {
        // 处理 Base64Encode 命令返回的 base64 数据
        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')
        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)
        
        // 重置等待标志
        this.waitingForBase64 = false
        
        if (base64Data && base64Data.length > 1000) { // 确保数据有效
          // 保存 base64 数据
          this.currentImageData = begin_data + base64Data
          
          // 使用高质量 base64 数据进行 OCR 识别
          console.log('使用高质量 base64 数据进行 OCR 识别')
          this.processOcrWithImage(this.currentImageData)
        } else {
          console.error('获取到的 base64 数据无效或太短')
          
          // 如果 base64 数据无效，尝试使用预览图或文件路径
          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && 
              this.$refs.scannerPreview.src.startsWith('data:image')) {
            console.log('使用预览图数据进行 OCR 识别')
            this.processOcrWithImage(this.$refs.scannerPreview.src)
          } else {
            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)
            this.processOcrWithImage(this.currentPhotoPath)
          }
        }
      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {
        // 处理目录检查结果
        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')
        console.log('目录检查结果:', result)
        // 如果目录不存在，结果为"0"，存在则为"1"
        if (result === "0") {
          console.log('C:\\pic\\ 目录不存在，将创建')
        }
      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {
        // 处理创建目录结果
        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')
        console.log('创建目录结果:', result)
        // 如果创建成功，结果为"1"，失败则为"0"
        if (result === "1") {
          console.log('C:\\pic\\ 目录创建成功')
        } else {
          console.warn('C:\\pic\\ 目录创建失败或已存在')
        }
      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {
        // 处理条码识别命令响应，不作为图像数据处理
        console.log('收到条码识别命令响应:', event.data)
      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {
        // 处理图像数据 - 判断是否为base64图像数据
        // 增加额外检查，确保不是条码数据
        if (this.$refs.scannerPreview) {
          try {
            // 尝试验证是否为有效的base64图像数据
            const testData = event.data.substring(0, 100); // 只取前100个字符测试
            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常
            
            // 确保是完整的base64数据
            const imgData = begin_data + event.data
            this.$refs.scannerPreview.src = imgData
            
            // 保存当前图像数据以备后用
            this.currentImageData = imgData
            console.log('成功保存图像数据，长度:', event.data.length)
          } catch (e) {
            console.error('收到的数据不是有效的base64图像:', e)
          }
        }
      } else {
        // 其他消息，可能是普通文本或命令响应
        console.log('收到高拍仪消息:', event.data)
      }
    },
    
    // 启动高拍仪
    startScanner() {
      if (!this.scannerConnected) {
        this.initScannerWebSocket()
        return
      }
      
      try {
        // 设置分辨率
        this.sendScannerCommand('vSetResolution(8)')
        
        // 启用去黑边功能
        this.sendScannerCommand('vSetDelHBFlag(true)')
        
        // 增加亮度控制
        this.sendScannerCommand('vSetBrightness(80)') // 增加亮度
        
        // 增加对比度控制
        this.sendScannerCommand('vSetContrast(70)') // 增加对比度
        
        // 设置曝光
        this.sendScannerCommand('vSetExposure(60)') // 设置曝光
        
        // 启动主摄像头
        this.sendScannerCommand('bStartPlay()')
        this.$message.success('高拍仪已启动')
      } catch (error) {
        console.error('启动高拍仪失败', error)
        this.$message.error('启动高拍仪失败')
      }
    },
    
    // 停止高拍仪
    stopScanner() {
      if (!this.scannerConnected) {
        return
      }
      
      try {
        this.sendScannerCommand('bStopPlay()')
        this.$message.success('高拍仪已停止')
      } catch (error) {
        console.error('停止高拍仪失败', error)
        this.$message.error('停止高拍仪失败')
      }
    },
    
    // 扫描身份证
    scanIdCard() {
      if (!this.scannerConnected && !this.simulationMode) {
        this.$message.warning('请先启动高拍仪')
        return
      }
      
      try {
        if (this.simulationMode) {
          // 模拟模式下，直接调用OCR接口处理示例图片
          this.processOcrWithSimulationImage()
          return
        }
        
        // 确保启用去黑边功能
        this.sendScannerCommand('vSetDelHBFlag(true)')
        
        // 设置身份证自动寻边模式
        this.sendScannerCommand('bSetMode(4)')
        
        // 先检查目录是否存在，不存在则创建
        this.sendScannerCommand('bDirIsExist(C:\\pic\\)')
        
        // 延迟一下，确保目录检查完成
        setTimeout(() => {
          // 创建目录（即使目录已存在，这个命令也不会报错）
          this.sendScannerCommand('bCreateDir(C:\\pic\\)')
          
          // 生成唯一文件名（使用时间戳）
          const timestamp = new Date().getTime()
          const filename = `idcard_${timestamp}`
          this.currentPhotoPath = `C:\\pic\\${filename}.jpg`
          console.log('当前照片路径:', this.currentPhotoPath)
          
          // 拍照并保存到本地
          this.sendScannerCommand(`bSaveJPG(C:\\pic\\,${filename})`)
          
          // 设置一个标志，表示我们正在等待 Base64Encode 的响应
          this.waitingForBase64 = true
          
          // 延迟一下，确保图片保存完成
          setTimeout(() => {
            // 使用 Base64Encode 命令获取高质量的 base64 图像数据
            this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)
            
            // 识别条码
            this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)
            
            // 清除之前的图像数据，确保不会使用旧数据
            this.currentImageData = null
            
            // 设置超时，确保即使没有收到 Base64Encode 的响应，也会调用 OCR 接口
            setTimeout(() => {
              if (this.waitingForBase64) {
                console.log('Base64Encode 响应超时，使用备用方法调用 OCR')
                this.waitingForBase64 = false
                
                // 如果有预览图数据，使用预览图数据
                if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && 
                    this.$refs.scannerPreview.src.startsWith('data:image')) {
                  console.log('使用预览图数据进行 OCR 识别')
                  this.processOcrWithImage(this.$refs.scannerPreview.src)
                } else {
                  // 否则使用文件路径
                  console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)
                  this.processOcrWithImage(this.currentPhotoPath)
                }
              }
            }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法
            
          }, 1000) // 延迟1秒，确保图片保存完成
        }, 500) // 延迟500ms，确保目录检查完成
        
        this.$message.info('正在识别身份证，请稍候...')
      } catch (error) {
        console.error('扫描身份证失败', error)
        this.$message.error('扫描身份证失败')
      }
    },
    
         // 处理OCR识别结果
    processOcrWithImage(imagePath) {
      if (this.isProcessingOcr) {
        return
      }
      
      this.isProcessingOcr = true
      this.$message.info('正在进行OCR识别...')
      
      // 判断是否是base64格式的图片数据
      if (imagePath.startsWith('data:image')) {
        // 对图片进行缩放处理，然后再进行OCR识别
        this.scaleImageForOcr(imagePath).then(scaledImageData => {
          console.log('图片已缩放处理，准备进行OCR识别')
          
          // 创建文件对象从base64数据
          const base64Data = scaledImageData.split(',')[1]
          
          // 添加JSON格式的请求头
          const config = {
            headers: {
              'Content-Type': 'application/json'
            }
          }
          
          // 创建JSON数据
          const jsonData = {
            image: base64Data
          }
          
          // 调用OCR API - 使用JSON格式发送
          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)
            .then(response => {
              console.log('OCR API 响应:', response)
              this.handleOcrResult(response.data)
            })
            .catch(error => {
              console.error('OCR识别失败', error)
              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))
              
              // 如果JSON格式失败，尝试使用表单数据
              console.log('尝试使用表单数据格式重新发送请求')
              
              // 创建表单数据
              const formData = new FormData()
              const byteCharacters = atob(base64Data)
              const byteArrays = []
              
              for (let i = 0; i < byteCharacters.length; i++) {
                byteArrays.push(byteCharacters.charCodeAt(i))
              }
              
              const byteArray = new Uint8Array(byteArrays)
              const blob = new Blob([byteArray], { type: 'image/jpeg' })
              
              // 创建文件对象
              const fileName = `idcard_${new Date().getTime()}.jpg`
              const file = new File([blob], fileName, { type: 'image/jpeg' })
              
              // 添加到表单
              formData.append('image', file)
              this.callOcrApi(formData)
            })
            .finally(() => {
              this.isProcessingOcr = false
            })
        }).catch(error => {
          console.error('图像缩放处理失败:', error)
          
          // 如果缩放处理失败，直接使用原始图像
          this.sendOriginalImage(imagePath)
        })
      } else {
        // 如果是文件路径，尝试读取文件并上传
        const formData = new FormData()
        formData.append('image_path', imagePath)
        console.log(`发送图片路径进行OCR识别: ${imagePath}`)
        
        // 调用OCR API
        this.callOcrApi(formData)
      }
    },
    
    // 对图片进行缩放处理，只进行尺寸调整
    scaleImageForOcr(imageDataUrl) {
      return new Promise((resolve, reject) => {
        try {
          const img = new Image()
          img.src = imageDataUrl
          
          img.onload = () => {
            // 判断是否需要缩放
            if (img.width <= 1000 && img.height <= 630) {
              console.log('图片尺寸已经合适，无需缩放')
              resolve(imageDataUrl)
              return
            }
            
            // 为OCR识别优化的尺寸，减小尺寸提高处理速度
            // 身份证比例大约是1.58:1
            const maxWidth = 1000  // 从1800减小到1000
            const maxHeight = 630  // 从1140减小到630
            
            // 计算等比例缩放后的尺寸
            let width = img.width
            let height = img.height
            
            if (width > maxWidth) {
              height = (height * maxWidth) / width
              width = maxWidth
            }
            
            if (height > maxHeight) {
              width = (width * maxHeight) / height
              height = maxHeight
            }
            
            // 创建Canvas
            const canvas = document.createElement('canvas')
            canvas.width = width
            canvas.height = height
            const ctx = canvas.getContext('2d')
            
            // 绘制图像
            ctx.drawImage(img, 0, 0, width, height)
            
            // 转换为适中质量JPEG，进一步减小文件大小
            const scaledImageData = canvas.toDataURL('image/jpeg', 0.85)
            
            // 输出调试信息
            console.log(`图片已缩放: 原始尺寸=${img.width}x${img.height}, 缩放尺寸=${width}x${height}`)
            
            resolve(scaledImageData)
          }
          
          img.onerror = (error) => {
            console.error('图像加载失败:', error)
            reject(error)
          }
        } catch (e) {
          console.error('图像缩放处理失败:', e)
          reject(e)
        }
      })
    },
    
    // 发送原始图像
    sendOriginalImage(imagePath) {
      try {
        console.log('使用原始图像数据进行OCR识别')
        
        if (imagePath.startsWith('data:image')) {
          // 创建文件对象从base64数据
          const base64Data = imagePath.split(',')[1]
          
          // 添加JSON格式的请求头
          const config = {
            headers: {
              'Content-Type': 'application/json'
            }
          }
          
          // 创建JSON数据
          const jsonData = {
            image: base64Data
          }
          
          // 使用JSON格式发送
          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)
            .then(response => {
              console.log('OCR API 响应:', response)
              this.handleOcrResult(response.data)
            })
            .catch(error => {
              console.error('OCR识别失败', error)
              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))
            })
            .finally(() => {
              this.isProcessingOcr = false
            })
        } else {
          // 如果是文件路径
          const formData = new FormData()
          formData.append('image_path', imagePath)
          this.callOcrApi(formData)
        }
      } catch (e) {
        console.error('发送原始图像失败:', e)
        this.isProcessingOcr = false
        this.$message.error('发送图像失败: ' + e.message)
      }
    },
    
    // 模拟模式下使用示例图片进行OCR识别
    processOcrWithSimulationImage() {
      this.isProcessingOcr = true
      this.$message.info('模拟模式：正在进行OCR识别...')
      
      // 准备表单数据
      const formData = new FormData()
      formData.append('simulation', 'true')
      
      // 调用OCR API
      axios.post(this.scannerConfig.ocrApiUrl, formData)
        .then(response => {
          this.handleOcrResult(response.data)
        })
        .catch(error => {
          console.error('模拟OCR识别失败', error)
          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))
        })
        .finally(() => {
          this.isProcessingOcr = false
        })
    },
    
    // 处理OCR识别结果
    handleOcrResult(result) {
      if (!result || !result.success) {
        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))
        return
      }
      
      this.$message.success('OCR识别成功')
      console.log('OCR识别结果:', result)
      
      // 更新表单数据
      const ocrData = result.data || {}
      
      // 保存OCR识别的地址，以便在API查询后仍能使用
      const ocrAddress = ocrData.address || ''
      
      // 更新身份证号
      if (ocrData.id_number) {
        this.workerForm.idCardNumber = ocrData.id_number
        // 添加更明显的提示
        this.$notify({
          title: '证件号码识别成功',
          type: 'success',
          duration: 5000
        })
      }
      
      // 更新姓名
      if (ocrData.name) {
        this.workerForm.workerName = ocrData.name
      }
      
      // 更新性别
      if (ocrData.gender) {
        this.workerForm.gender = ocrData.gender
      }
      
      // 更新地址
      if (ocrData.address) {
        this.workerForm.homeAddress = ocrData.address
        // 添加地址识别成功的提示
        this.$notify({
          title: '地址识别成功',
          message: `识别到地址: ${ocrData.address}`,
          type: 'success',
          duration: 5000
        })
      }
      
      // 如果有身份证号，尝试从系统中查询更多信息
      if (ocrData.id_number) {
        // 传递OCR识别的地址作为参数
        this.searchWorkerByIdCard(ocrData.id_number, ocrAddress)
      }
    },
    
    // 发送高拍仪命令
    sendScannerCommand(command) {
      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        this.webSocket.send(command)
      } else {
        throw new Error('WebSocket未连接')
      }
    },
    
    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.webSocket) {
        // 先停止高拍仪
        if (this.scannerConnected) {
          try {
            this.webSocket.send('bStopPlay()')
          } catch (e) {
            console.error('停止高拍仪失败', e)
          }
        }
        
        // 关闭连接
        this.webSocket.close()
        this.webSocket = null
        this.scannerConnected = false
      }
    },
    
    // 验证是否为身份证号
    isIdCardNumber(str) {
      // 简单验证18位或15位身份证号
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      return reg.test(str)
    },
    
    // 根据身份证号查询工人信息
    searchWorkerByIdCard(idCardNumber, addressFromOcr = '') {
      if (!idCardNumber) {
        this.$message.warning('证件号码不能为空')
        return
      }
      
      this.$message.info('正在查询工人信息...')
      
      // 调用API根据身份证号查询工人信息
      getWorkerByIdCard(idCardNumber)
        .then(response => {
          if (response.code === 0 && response.data) {
            const workerData = response.data
            
            // 更新表单数据
            this.workerForm.workerId = workerData.id || ''
            this.workerForm.workerName = workerData.workerName || ''
            this.workerForm.gender = workerData.gender || '男'
            this.workerForm.homeAddress = workerData.homeAddress || ''
            this.workerForm.teamName = workerData.teamSysName || ''
            this.workerForm.teamCode = workerData.teamSysNo || ''
            this.workerForm.jobPosition = workerData.workerType || ''
            this.workerForm.participantName = workerData.projectSubContractorName || workerData.corpName || ''
            this.workerForm.participantCode = workerData.corpCode || ''
            this.workerForm.projectSubContractorId = workerData.projectSubContractorId || workerData.corpId || ''
            this.workerForm.projectCode = workerData.projectCode || this.workerForm.projectCode
            this.workerForm.projectName = workerData.projectName || ''
            
            // 如果API没有返回地址，但OCR识别到了地址，则使用OCR识别的地址
            if ((!workerData.homeAddress || workerData.homeAddress === '') && addressFromOcr) {
              console.log('使用OCR识别的地址:', addressFromOcr)
              this.workerForm.homeAddress = addressFromOcr
            }
            
            // 保存到本地存储
            this.saveToLocalStorage()
        
            this.$message.success('工人信息查询成功')
            
            // 添加更详细的通知
            this.$notify({
              title: '工人信息查询成功',
              message: `已找到工人: ${workerData.workerName}，所属班组: ${workerData.teamSysName || '未知'}`,
              type: 'success',
              duration: 5000
            })
          } else {
            this.$message.warning(response.msg || '未找到工人信息')
            
            // 如果没有找到，可以保留一些基本信息
            if (this.workerForm.idCardNumber && !this.workerForm.workerName) {
              // 从身份证号提取出生日期和性别信息
              this.extractInfoFromIdCard(idCardNumber)
            }
            
            // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址
            if (addressFromOcr) {
              console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)
              this.workerForm.homeAddress = addressFromOcr
            }
          }
        })
        .catch(error => {
          console.error('查询工人信息失败:', error)
          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))
          
          // 如果API调用失败，可以尝试从身份证号提取一些基本信息
          if (this.workerForm.idCardNumber) {
            this.extractInfoFromIdCard(idCardNumber)
          }
          
          // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址
          if (addressFromOcr) {
            console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)
            this.workerForm.homeAddress = addressFromOcr
          }
        })
    },
    
    // 从身份证号提取信息
    extractInfoFromIdCard(idCardNumber) {
      if (!idCardNumber || idCardNumber.length < 18) return
      
      try {
        // 提取性别 (第17位，奇数为男，偶数为女)
        const genderCode = parseInt(idCardNumber.charAt(16))
        this.workerForm.gender = genderCode % 2 === 1 ? '男' : '女'
        
        // 可以添加更多提取逻辑，如出生日期等
        console.log('从身份证号提取的性别:', this.workerForm.gender)
      } catch (e) {
        console.error('从身份证号提取信息失败:', e)
      }
    },
    
    // 查找工人
    searchWorker() {
      if (!this.workerForm.idCardNumber) {
        this.$message.warning('请先输入证件号码')
        return
      }
      
      // 根据证件号码查询工人信息
      this.searchWorkerByIdCard(this.workerForm.idCardNumber)
    },
    
    // 刷新工人信息
    refreshWorkerInfo() {
      if (this.workerForm.idCardNumber) {
        this.$message.info('正在重新获取工人信息...')
        this.searchWorkerByIdCard(this.workerForm.idCardNumber)
      } else {
        this.$message.warning('请先输入证件号码')
      }
    },
    
    // 保存数据到本地存储
    saveToLocalStorage() {
      try {
        // 合并表单数据和已有数据
        let existingData = {}
        try {
          const savedData = localStorage.getItem('contractWorkerInfo')
          if (savedData) {
            existingData = JSON.parse(savedData)
          }
        } catch (e) {}
        
        const dataToSave = { 
          ...existingData,
          ...this.workerForm 
        }
        
        console.log('ContractStep1 保存数据到本地存储:', JSON.stringify(dataToSave))
        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave))
        return dataToSave
      } catch (error) {
        console.error('保存到本地存储失败:', error)
        return this.workerForm
      }
    },
    
    // 下一步
    nextStep() {
      this.$refs.workerForm.validate(valid => {
        if (valid) {
          // 关闭高拍仪
          if (this.scannerConnected) {
            this.stopScanner();
            this.closeWebSocket();
            console.log('下一步操作：已自动关闭高拍仪');
          }
          
          // 添加详细的调试日志
          console.log('=== ContractStep1 nextStep 开始 ===')
          console.log('ContractStep1 当前表单数据:', JSON.stringify(this.workerForm))

          // 保存到本地存储并获取完整数据
          const completeData = this.saveToLocalStorage()
          console.log('ContractStep1 保存到localStorage的完整数据:', JSON.stringify(completeData))

          // 准备一个包含所有必要字段的数据对象
          const dataToUpdate = {
            ...completeData,
            // 确保这些重要字段一定会被传递
            workerName: this.workerForm.workerName,
            idCardNumber: this.workerForm.idCardNumber,
            gender: this.workerForm.gender,
            homeAddress: this.workerForm.homeAddress,
            teamName: this.workerForm.teamName,
            jobPosition: this.workerForm.jobPosition,
            participantName: this.workerForm.participantName
          }

          console.log('ContractStep1 准备发送给父组件的数据:', JSON.stringify(dataToUpdate))
          console.log('ContractStep1 关键字段检查:')
          console.log('- workerName:', dataToUpdate.workerName)
          console.log('- idCardNumber:', dataToUpdate.idCardNumber)

          // 先更新父组件中的工人信息
          this.$emit('update-worker-info', dataToUpdate)
          console.log('ContractStep1 已发送update-worker-info事件')
          
          // 等待数据更新后再触发导航
          this.$nextTick(() => {
            // 发送下一步事件，让父组件处理导航
            this.$emit('next-step')
          })
        } else {
          this.$message.warning('请完善工人信息')
        }
      })
    },
    
    // 切换到模拟模式
    switchToSimulationMode() {
      this.simulationMode = true;
      this.scannerConnected = false;
      
      if (this.webSocket) {
        this.manuallyDisconnected = true;
        this.webSocket.close();
        this.webSocket = null;
      }
      
      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动输入信息或使用模拟识别功能。');
      
      // 清除连接超时
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
      }
    },

    // 上传图片处理函数
    handleUploadImage(file) {
      if (file) {
        // 验证文件类型
        const isImage = file.type.indexOf('image/') !== -1;
        if (!isImage) {
          this.$message.error('请上传图片文件!');
          return false;
        }
        
        // 验证文件大小 (限制为10MB)
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          this.$message.error('图片大小不能超过10MB!');
          return false;
        }
        
        this.$message.info('正在处理图片，请稍候...');
        
        // 更新预览图并压缩图片
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          // 压缩图片
          this.compressImage(e.target.result, (compressedDataUrl) => {
            // 更新预览图
            if (this.$refs.scannerPreview) {
              this.$refs.scannerPreview.src = compressedDataUrl;
            }
            
            // 保存图片路径并处理OCR
            this.currentPhotoPath = compressedDataUrl;
            this.$message.info('正在识别上传的图片，请稍候...');
            
            // 调用OCR识别
            setTimeout(() => {
              this.processOcrWithImage(this.currentPhotoPath);
            }, 300); // 短暂延迟，确保UI更新
          });
        };
        
        reader.onerror = (error) => {
          console.error('读取图片文件失败', error);
          this.$message.error('读取图片文件失败');
        };
      }
      return false; // 阻止默认的上传行为
    },
    
    // 压缩图片函数
    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {
      const img = new Image();
      img.src = dataUrl;
      
      img.onload = () => {
        // 创建Canvas
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        // 计算缩放比例
        if (width > height) {
          if (width > maxWidth) {
            height *= maxWidth / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width *= maxHeight / height;
            height = maxHeight;
          }
        }
        
        // 设置Canvas大小
        canvas.width = width;
        canvas.height = height;
        
        // 绘制图像
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);
        
        // 转换为压缩后的DataURL
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        
        // 计算压缩率
        const originalSize = dataUrl.length;
        const compressedSize = compressedDataUrl.length;
        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
        
        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);
        
        callback(compressedDataUrl);
      };
      
      img.onerror = () => {
        console.error('图片压缩失败');
        callback(dataUrl); // 失败时使用原始图片
      };
    },

    // 增强图像质量
    enhanceImage(imageDataUrl) {
      return new Promise((resolve, reject) => {
        try {
          const img = new Image();
          img.src = imageDataUrl;
          
          img.onload = () => {
            // 创建Canvas
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            
            // 绘制原始图像
            ctx.drawImage(img, 0, 0, img.width, img.height);
            
            // 1. 自动裁剪 - 找到身份证区域
            this.cropIdCard(canvas, ctx);
            
            // 2. 亮度和对比度增强
            this.adjustBrightnessContrast(canvas, ctx, 50, 80); // 增加亮度和对比度
            
            // 3. 锐化处理
            this.sharpenImage(canvas, ctx);
            
            // 返回处理后的图像
            resolve(canvas.toDataURL('image/jpeg', 0.95)); // 使用高质量
          };
          
          img.onerror = (error) => {
            console.error('图像加载失败:', error);
            reject(error);
          };
        } catch (e) {
          console.error('图像增强处理失败:', e);
          reject(e);
        }
      });
    },
    
    // 裁剪身份证区域
    cropIdCard(canvas, ctx) {
      try {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // 查找非黑色区域边界
        let minX = canvas.width, minY = canvas.height, maxX = 0, maxY = 0;
        const threshold = 40; // 亮度阈值
        
        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < canvas.width; x++) {
            const idx = (y * canvas.width + x) * 4;
            const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
            
            if (brightness > threshold) {
              minX = Math.min(minX, x);
              minY = Math.min(minY, y);
              maxX = Math.max(maxX, x);
              maxY = Math.max(maxY, y);
            }
          }
        }
        
        // 添加边距
        const padding = 10;
        minX = Math.max(0, minX - padding);
        minY = Math.max(0, minY - padding);
        maxX = Math.min(canvas.width, maxX + padding);
        maxY = Math.min(canvas.height, maxY + padding);
        
        // 检查是否找到了有效区域
        const width = maxX - minX;
        const height = maxY - minY;
        
        if (width > 50 && height > 50 && width < canvas.width && height < canvas.height) {
          // 裁剪图像
          const croppedData = ctx.getImageData(minX, minY, width, height);
          canvas.width = width;
          canvas.height = height;
          ctx.putImageData(croppedData, 0, 0);
          console.log('成功裁剪身份证区域:', width, 'x', height);
        } else {
          console.log('未找到明确的身份证区域，跳过裁剪');
        }
      } catch (e) {
        console.error('裁剪身份证区域失败:', e);
      }
    },
    
    // 调整亮度和对比度
    adjustBrightnessContrast(canvas, ctx, brightness = 0, contrast = 0) {
      try {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // 计算对比度因子
        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
        
        for (let i = 0; i < data.length; i += 4) {
          // 亮度调整
          data[i] += brightness;     // R
          data[i + 1] += brightness; // G
          data[i + 2] += brightness; // B
          
          // 对比度调整
          data[i] = factor * (data[i] - 128) + 128;
          data[i + 1] = factor * (data[i + 1] - 128) + 128;
          data[i + 2] = factor * (data[i + 2] - 128) + 128;
          
          // 确保值在0-255范围内
          data[i] = Math.min(255, Math.max(0, data[i]));
          data[i + 1] = Math.min(255, Math.max(0, data[i + 1]));
          data[i + 2] = Math.min(255, Math.max(0, data[i + 2]));
        }
        
        ctx.putImageData(imageData, 0, 0);
        console.log('成功调整亮度和对比度');
      } catch (e) {
        console.error('调整亮度和对比度失败:', e);
      }
    },
    
    // 锐化图像
    sharpenImage(canvas, ctx) {
      try {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        const width = canvas.width;
        const height = canvas.height;
        const sharpenData = new Uint8ClampedArray(data);
        
        // 锐化卷积核
        const kernel = [
          0, -1, 0,
          -1, 5, -1,
          0, -1, 0
        ];
        
        // 应用卷积
        for (let y = 1; y < height - 1; y++) {
          for (let x = 1; x < width - 1; x++) {
            for (let c = 0; c < 3; c++) {
              let sum = 0;
              for (let ky = -1; ky <= 1; ky++) {
                for (let kx = -1; kx <= 1; kx++) {
                  const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                  sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];
                }
              }
              sharpenData[(y * width + x) * 4 + c] = Math.min(255, Math.max(0, sum));
            }
          }
        }
        
        // 更新图像数据
        const enhancedImageData = new ImageData(sharpenData, width, height);
        ctx.putImageData(enhancedImageData, 0, 0);
        console.log('成功锐化图像');
      } catch (e) {
        console.error('锐化图像失败:', e);
      }
    },

    // 调用OCR API
    callOcrApi(formData) {
      console.log('调用OCR API:', this.scannerConfig.ocrApiUrl)
      
      // 添加跨域请求头
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Control-Allow-Origin': '*'
        }
      }
      
      axios.post(this.scannerConfig.ocrApiUrl, formData, config)
        .then(response => {
          console.log('OCR API 响应:', response)
          this.handleOcrResult(response.data)
        })
        .catch(error => {
          console.error('OCR识别失败', error)
          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))
        })
        .finally(() => {
          this.isProcessingOcr = false
        })
    }
  }
}
</script>

<style scoped>
.contract-step1-container {
  padding: 20px;
}

.contract-step1-container h3 {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
}

.scanner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9fafc;
}

.scanner-preview {
  width: 600px;
  height: 400px;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  background-color: #ebeef5;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.scanner-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.scanner-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.upload-button {
  display: inline-block;
  margin-left: 10px;
}

.worker-info-form {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.input-with-button {
  display: flex;
  align-items: center;
}

.input-with-button .el-input {
  flex: 1;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .scanner-preview {
    width: 100%;
    height: 300px;
  }
  
  .scanner-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .upload-button {
    margin-left: 0;
    margin-top: 10px;
  }
  
  .input-with-button {
    flex-direction: column;
    align-items: stretch;
  }
  
  .input-with-button .el-input {
    margin-right: 0;
    margin-bottom: 8px;
  }
}
</style> 