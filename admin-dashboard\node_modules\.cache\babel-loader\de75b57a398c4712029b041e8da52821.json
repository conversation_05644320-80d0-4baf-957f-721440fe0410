{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractUpload.vue", "mtime": 1753669028133}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\babel.config.js", "mtime": 1746865124045}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "saveContractFile", "getWorkerByIdCard", "uploadContract", "jsPDF", "html2canvas", "name", "data", "webSocket", "scannerConnected", "contractForm", "workerId", "worker<PERSON>ame", "idCardNumber", "ProjectName", "projectCode", "projectSubContractorId", "projectSubContractorName", "teamCode", "teamName", "contractPeriodType", "contractStartDate", "Date", "contractEndDate", "contractSignDate", "salaryType", "salaryUnitPrice", "salaryPerUnit", "salaryUnitType", "remark", "contractFile", "attachments", "rules", "required", "message", "trigger", "simulationMode", "connectionTimeout", "manuallyDisconnected", "scannerConfig", "wsUrl", "timeout", "autoSimulate", "ocrApiUrl", "configDialogVisible", "tempScannerConfig", "currentPhotoPath", "isProcessingUpload", "isProcessingOcr", "photoList", "idCardFound", "isLoadingWorkerInfo", "isGeneratingPDF", "currentImageData", "waitingForBase64", "created", "$route", "query", "mounted", "initScannerWebSocket", "<PERSON><PERSON><PERSON><PERSON>", "closeWebSocket", "methods", "handleSalaryTypeChange", "value", "$set", "$delete", "WebSocket", "setTimeout", "console", "warn", "switchToSimulationMode", "$message", "error", "onopen", "event", "log", "success", "clearTimeout", "onclose", "onerror", "onmessage", "handleScannerMessage", "begin_data", "indexOf", "barcode", "replace", "isIdCardNumber", "searchWorkerByIdCard", "result", "base64Data", "length", "push", "imageData", "path", "timestamp", "getTime", "info", "processOcrWithImage", "$refs", "scannerPreview", "src", "startsWith", "includes", "testData", "substring", "window", "atob", "imgData", "e", "startScanner", "sendScannerCommand", "stopScanner", "scanContract", "warning", "processWithSimulationImage", "processOcrWithSimulationImage", "filename", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "fillStyle", "fillRect", "font", "textAlign", "fillText", "emptyImageUrl", "toDataURL", "command", "readyState", "OPEN", "send", "Error", "close", "handleUploadImage", "file", "isImage", "type", "isLt10M", "size", "reader", "FileReader", "readAsDataURL", "onload", "compressImage", "target", "compressedDataUrl", "isIdCard", "dataUrl", "callback", "max<PERSON><PERSON><PERSON>", "maxHeight", "quality", "img", "Image", "drawImage", "originalSize", "compressedSize", "compressionRatio", "toFixed", "submitContract", "validate", "valid", "convertImagesToPdf", "JSON", "stringify", "contract", "corpCode", "corpName", "idCardType", "idNumber", "teamNo", "getSalaryTypeValue", "startDate", "formatDate", "endDate", "signDate", "unit", "unitPrice", "Number", "perUnit", "unitType", "attachment", "split", "base64", "readFileAsBase64Promise", "requestData", "contractList", "attachmentsCount", "then", "response", "code", "goBack", "catch", "finally", "Promise", "resolve", "reject", "typeMap", "getSalaryTypeText", "date", "d", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "$router", "go", "previewAttachment", "$msgbox", "title", "dangerouslyUseHTMLString", "customClass", "showCancelButton", "showConfirmButton", "pdfBlob", "dataURLtoBlob", "blobUrl", "URL", "createObjectURL", "beforeClose", "action", "instance", "done", "revokeObjectURL", "url", "link", "href", "download", "click", "Blob", "blob", "dataURL", "parts", "contentType", "raw", "<PERSON><PERSON><PERSON><PERSON>", "uInt8Array", "Uint8Array", "i", "charCodeAt", "removeAttachment", "index", "$confirm", "confirmButtonText", "cancelButtonText", "splice", "scanIdCard", "imagePath", "formData", "FormData", "isHighQualityBase64", "byteCharacters", "byteArrays", "byteArray", "fileName", "File", "append", "callOcrApi", "preprocessImage", "processedImageData", "processedBase64", "post", "handleOcrResult", "_error$response", "base64Image", "getImageData", "r", "g", "b", "putImageData", "grayImageData", "grayData", "avg", "_error$response2", "ocrData", "id_number", "$notify", "duration", "str", "reg", "test", "workerData", "workId", "teamSysNo", "teamSysName", "projectName", "participantCode", "corpId", "subContractorId", "msg", "extractInfoFromIdCard", "genderCode", "parseInt", "char<PERSON>t", "gender", "parseIdCardNumber", "mockIdCardNumber", "fetchWorkerInfo", "mockWorker", "contractType", "removePhoto", "refreshWorkerInfo", "getUnitTypeLabel", "unitTypeMap", "pdf", "promises", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "for<PERSON>ach", "photo", "promise", "imgWidth", "imgHeight", "addPage", "addImage", "all", "output", "onloadend", "base64data", "handleAttachmentUpload", "actualFile", "fileIcon", "getFileTypeIcon", "fileData", "icon", "fileType", "beforeAttachmentUpload", "allowedTypes", "isAllowedType"], "sources": ["src/views/contract/ContractUpload.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contract-upload-container\">\r\n    <h3>合同上传</h3>\r\n    \r\n    <div class=\"scanner-container\">\r\n      <div class=\"scanner-layout\">\r\n        <!-- 左侧：高拍仪界面 -->\r\n        <div class=\"scanner-left\">\r\n          <div class=\"scanner-preview\">\r\n            <img id=\"photo\" src=\"\" width=\"600\" height=\"400\" ref=\"scannerPreview\">\r\n          </div>\r\n          \r\n          <div class=\"scanner-controls\">\r\n            <el-button type=\"primary\" @click=\"startScanner\">启动高拍仪</el-button>\r\n            <el-button type=\"success\" @click=\"scanContract\">拍摄合同</el-button>\r\n            <el-button type=\"warning\" @click=\"stopScanner\">停止高拍仪</el-button>\r\n            <el-upload\r\n              class=\"upload-button\"\r\n              action=\"#\"\r\n              :show-file-list=\"false\"\r\n              :before-upload=\"handleUploadImage\">\r\n              <el-button type=\"primary\" icon=\"el-icon-upload\">上传图片</el-button>\r\n            </el-upload>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 右侧：已拍摄照片 -->\r\n        <div class=\"scanner-right\" v-if=\"photoList.length > 0\">\r\n          <h4>已拍摄照片 ({{ photoList.length }})</h4>\r\n          <div class=\"photo-actions-top\">\r\n            <el-button type=\"primary\" @click=\"convertImagesToPdf\" :loading=\"isGeneratingPDF\" icon=\"el-icon-document\">\r\n              生成PDF\r\n            </el-button>\r\n          </div>\r\n          <div class=\"photo-items\">\r\n            <div v-for=\"(photo, index) in photoList\" :key=\"index\" class=\"photo-item\">\r\n              <img :src=\"photo.imageData\" alt=\"拍摄照片\" class=\"photo-thumbnail\">\r\n              <div class=\"photo-actions\">\r\n                <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-delete\" @click=\"removePhoto(index)\">删除</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"contract-info-form\">\r\n      <el-form :model=\"contractForm\" label-width=\"120px\" ref=\"contractForm\" :rules=\"rules\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\r\n              <el-input v-model=\"contractForm.workerName\" placeholder=\"请输入工人姓名\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\r\n              <div class=\"id-card-input\">\r\n                <el-input v-model=\"contractForm.idCardNumber\" placeholder=\"请输入证件号码\"></el-input>\r\n                <el-button type=\"primary\" icon=\"el-icon-refresh\" class=\"refresh-button\" @click=\"refreshWorkerInfo\" :loading=\"isLoadingWorkerInfo\">刷新</el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目名称\" prop=\"ProjectName\">\r\n              <el-input v-model=\"contractForm.ProjectName\" placeholder=\"项目名称\" :disabled=\"true\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目编号\" prop=\"projectCode\">\r\n              <el-input v-model=\"contractForm.projectCode\" placeholder=\"项目编号\" :disabled=\"true\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"参建单位\" prop=\"projectSubContractorName\">\r\n              <el-input v-model=\"contractForm.projectSubContractorName\" placeholder=\"请输入参建单位\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"签订日期\" prop=\"contractSignDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractSignDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同期限类型\" prop=\"contractPeriodType\">\r\n              <el-select v-model=\"contractForm.contractPeriodType\" placeholder=\"请选择合同期限类型\" style=\"width: 100%\">\r\n                <el-option label=\"固定期限\" value=\"固定期限\"></el-option>\r\n                <el-option label=\"无固定期限\" value=\"无固定期限\"></el-option>\r\n                <el-option label=\"以完成一定工作任务为期限\" value=\"以完成一定工作任务为期限\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同开始日期\" prop=\"contractStartDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractStartDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择开始日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同结束日期\" prop=\"contractEndDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractEndDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择结束日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工资类型\" prop=\"salaryType\">\r\n              <el-select v-model=\"contractForm.salaryType\" placeholder=\"请选择工资类型\" style=\"width: 100%\" @change=\"handleSalaryTypeChange\">\r\n                <el-option label=\"按天\" value=\"按天\"></el-option>\r\n                <el-option label=\"按月\" value=\"按月\"></el-option>\r\n                <el-option label=\"按工程量\" value=\"按工程量\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"contractForm.remark\" placeholder=\"请输入备注信息\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <!-- 根据工资类型显示不同的字段 -->\r\n        <el-row :gutter=\"20\" v-if=\"['按天', '按月'].includes(contractForm.salaryType)\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"计量单价\" prop=\"salaryUnitPrice\">\r\n              <el-input v-model=\"contractForm.salaryUnitPrice\" placeholder=\"请输入计量单价\">\r\n                <template slot=\"append\">元/{{contractForm.salaryType === '按天' ? '天' : '月'}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\" v-if=\"contractForm.salaryType === '按工程量'\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"每(工程数量)\" prop=\"salaryPerUnit\">\r\n              <el-input v-model=\"contractForm.salaryPerUnit\" placeholder=\"请输入工程数量\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"计量单位\" prop=\"salaryUnitType\">\r\n              <el-select v-model=\"contractForm.salaryUnitType\" placeholder=\"请选择计量单位\" style=\"width: 100%\">\r\n                <el-option label=\"米\" value=\"80\"></el-option>\r\n                <el-option label=\"平方米\" value=\"81\"></el-option>\r\n                <el-option label=\"立方米\" value=\"82\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"计量单价\" prop=\"salaryUnitPrice\">\r\n              <el-input v-model=\"contractForm.salaryUnitPrice\" placeholder=\"请输入计量单价\">\r\n                <template slot=\"append\">元/{{getUnitTypeLabel(contractForm.salaryUnitType) || '单位'}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 显示合同附件 -->\r\n      <div class=\"contract-attachments\">\r\n        <h4>合同附件</h4>\r\n        \r\n        <!-- 添加手动上传附件按钮 -->\r\n        <div class=\"attachment-actions\">\r\n          <el-upload\r\n            class=\"upload-attachment\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            :on-change=\"handleAttachmentUpload\"\r\n            :before-upload=\"beforeAttachmentUpload\"\r\n            :multiple=\"false\">\r\n            <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">上传附件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持PDF、Word、Excel、图片等格式文件，单个文件不超过10MB</div>\r\n          </el-upload>\r\n        </div>\r\n        \r\n        <el-table v-if=\"contractForm.attachments.length > 0\" :data=\"contractForm.attachments\" style=\"width: 100%\">\r\n          <el-table-column label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"scope.row.icon || 'el-icon-document'\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.name }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"type\" label=\"类型\" width=\"150\"></el-table-column>\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ (scope.row.size / 1024).toFixed(2) }} KB\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button \r\n                size=\"mini\" \r\n                type=\"primary\" \r\n                icon=\"el-icon-view\"\r\n                @click=\"previewAttachment(scope.row)\">预览</el-button>\r\n              <el-button \r\n                size=\"mini\" \r\n                type=\"danger\" \r\n                icon=\"el-icon-delete\"\r\n                @click=\"removeAttachment(scope.$index)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div v-else class=\"no-attachments\">\r\n          暂无附件\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-actions\">\r\n        <el-button type=\"primary\" @click=\"submitContract\">提交合同</el-button>\r\n        <el-button @click=\"goBack\">返回</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { saveContractFile, getWorkerByIdCard, uploadContract } from '@/api/roster'\r\nimport jsPDF from 'jspdf'\r\nimport html2canvas from 'html2canvas'\r\n\r\nexport default {\r\n  name: 'ContractUpload',\r\n  data() {\r\n    return {\r\n      webSocket: null,\r\n      scannerConnected: false,\r\n      contractForm: {\r\n        workerId: '',\r\n        workerName: '',\r\n        idCardNumber: '',\r\n        ProjectName: '', // 项目名称\r\n        projectCode: '', // 项目编号\r\n        projectSubContractorId: '', // 参建单位ID\r\n        projectSubContractorName: '', // 参建单位\r\n        teamCode: '', // 班组编号\r\n        teamName: '', // 班组名称\r\n        contractPeriodType: '固定期限', // 合同期限类型\r\n        contractStartDate: new Date(), // 合同开始日期\r\n        contractEndDate: null, // 合同结束日期\r\n        contractSignDate: new Date(),\r\n        salaryType: '按天', // 工资类型\r\n        salaryUnitPrice: '', // 工资单价\r\n        salaryPerUnit: '', // 每(工程数量)\r\n        salaryUnitType: '', // 计量单位\r\n        remark: '',\r\n        contractFile: null,\r\n        attachments: [] // 合同附件数组\r\n      },\r\n      rules: {\r\n        contractSignDate: [\r\n          { required: true, message: '请选择签订日期', trigger: 'change' }\r\n        ],\r\n        workerName: [\r\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\r\n        ],\r\n        idCardNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' }\r\n        ],\r\n        projectSubContractorName: [\r\n          { required: true, message: '请输入参建单位', trigger: 'blur' }\r\n        ],\r\n        contractPeriodType: [\r\n          { required: true, message: '请选择合同期限类型', trigger: 'change' }\r\n        ],\r\n        contractStartDate: [\r\n          { required: true, message: '请选择合同开始日期', trigger: 'change' }\r\n        ],\r\n        salaryType: [\r\n          { required: true, message: '请选择工资类型', trigger: 'change' }\r\n        ],\r\n        salaryUnitPrice: [\r\n          { required: true, message: '请输入计量单价', trigger: 'blur' }\r\n        ]\r\n      },\r\n      simulationMode: false, // 模拟模式标志\r\n      connectionTimeout: null, // 连接超时\r\n      manuallyDisconnected: false, // 手动断开标志\r\n      scannerConfig: {\r\n        wsUrl: 'ws://localhost:1818', // WebSocket连接地址\r\n        timeout: 3000, // 连接超时时间(毫秒)\r\n        autoSimulate: true, // 连接失败时是否自动切换到模拟模式\r\n        ocrApiUrl: 'http://127.0.0.1:5000/ocr' // OCR API地址\r\n      },\r\n      configDialogVisible: false, // 高拍仪配置对话框可见性\r\n      tempScannerConfig: { // 临时存储的高拍仪配置\r\n        wsUrl: 'ws://localhost:1818',\r\n        timeout: 3000,\r\n        autoSimulate: true,\r\n        ocrApiUrl: 'http://127.0.0.1:5000/ocr'\r\n      },\r\n      currentPhotoPath: '', // 当前拍摄的照片路径\r\n      isProcessingUpload: false, // 是否正在处理上传\r\n      isProcessingOcr: false, // 是否正在处理OCR\r\n      photoList: [], // 拍摄的照片列表\r\n      idCardFound: false, // 是否已识别到身份证\r\n      isLoadingWorkerInfo: false, // 是否正在加载工人信息\r\n      isGeneratingPDF: false, // 是否正在生成PDF\r\n      currentImageData: '', // 当前接收到的图像数据\r\n      waitingForBase64: false // 是否正在等待 Base64Encode 的响应\r\n    }\r\n  },\r\n  created() {\r\n    // 从路由参数获取工人信息\r\n    const { workerId, workerName, idCardNumber } = this.$route.query\r\n    if (workerId && workerName && idCardNumber) {\r\n      this.contractForm.workerId = workerId\r\n      this.contractForm.workerName = workerName\r\n      this.contractForm.idCardNumber = idCardNumber\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化高拍仪WebSocket连接\r\n    this.initScannerWebSocket()\r\n  },\r\n  beforeDestroy() {\r\n    // 组件销毁前关闭WebSocket连接\r\n    this.closeWebSocket()\r\n  },\r\n  methods: {\r\n    // 处理工资类型变更\r\n    handleSalaryTypeChange(value) {\r\n      // 重置相关字段\r\n      this.contractForm.salaryUnitPrice = '';\r\n      \r\n      if (value === '按工程量') {\r\n        this.contractForm.salaryPerUnit = '';\r\n        this.contractForm.salaryUnitType = '';\r\n        \r\n        // 动态添加验证规则\r\n        this.$set(this.rules, 'salaryPerUnit', [\r\n          { required: true, message: '请输入工程数量', trigger: 'blur' }\r\n        ]);\r\n        this.$set(this.rules, 'salaryUnitType', [\r\n          { required: true, message: '请选择计量单位', trigger: 'change' }\r\n        ]);\r\n      } else {\r\n        // 移除验证规则\r\n        this.$delete(this.rules, 'salaryPerUnit');\r\n        this.$delete(this.rules, 'salaryUnitType');\r\n      }\r\n    },\r\n    \r\n    // 初始化高拍仪WebSocket连接\r\n    initScannerWebSocket() {\r\n      try {\r\n        // 添加模拟模式标志\r\n        this.simulationMode = false;\r\n        \r\n        // 尝试连接WebSocket\r\n        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)\r\n        \r\n        // 设置连接超时\r\n        this.connectionTimeout = setTimeout(() => {\r\n          if (!this.scannerConnected) {\r\n            console.warn('高拍仪连接超时，切换到模拟模式');\r\n            if (this.scannerConfig.autoSimulate) {\r\n              this.switchToSimulationMode();\r\n            } else {\r\n              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\r\n            }\r\n          }\r\n        }, this.scannerConfig.timeout);\r\n        \r\n        this.webSocket.onopen = (event) => {\r\n          console.log('高拍仪WebSocket连接成功')\r\n          this.scannerConnected = true\r\n          this.$message.success('高拍仪连接成功')\r\n          clearTimeout(this.connectionTimeout);\r\n        }\r\n        \r\n        this.webSocket.onclose = (event) => {\r\n          console.log('高拍仪WebSocket连接关闭')\r\n          this.scannerConnected = false\r\n          // 如果不是主动关闭，尝试切换到模拟模式\r\n          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {\r\n            this.switchToSimulationMode();\r\n          }\r\n        }\r\n        \r\n        this.webSocket.onerror = (event) => {\r\n          console.error('高拍仪WebSocket连接错误', event)\r\n          this.scannerConnected = false\r\n          if (this.scannerConfig.autoSimulate) {\r\n            this.switchToSimulationMode();\r\n          } else {\r\n            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\r\n          }\r\n        }\r\n        \r\n        this.webSocket.onmessage = (event) => {\r\n          this.handleScannerMessage(event)\r\n        }\r\n      } catch (error) {\r\n        console.error('初始化高拍仪WebSocket失败', error)\r\n        if (this.scannerConfig.autoSimulate) {\r\n          this.switchToSimulationMode();\r\n        } else {\r\n          this.$message.error(`初始化高拍仪失败: ${error.message}`);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理高拍仪消息\r\n    handleScannerMessage(event) {\r\n      const begin_data = \"data:image/jpeg;base64,\"\r\n      \r\n      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {\r\n        // 处理条码识别结果\r\n        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')\r\n        this.$message.success('识别到条码: ' + barcode)\r\n        \r\n        // 如果是身份证号格式，填入表单\r\n        if (this.isIdCardNumber(barcode)) {\r\n          this.contractForm.idCardNumber = barcode\r\n          this.searchWorkerByIdCard(barcode)\r\n        }\r\n      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {\r\n        // 处理保存图片结果\r\n        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')\r\n        this.$message.success('图片保存成功: ' + result)\r\n      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {\r\n        // 处理 Base64Encode 命令返回的 base64 数据\r\n        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')\r\n        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)\r\n        \r\n        // 重置等待标志\r\n        this.waitingForBase64 = false\r\n        \r\n        if (base64Data && base64Data.length > 1000) { // 确保数据有效\r\n          // 保存 base64 数据\r\n          this.currentImageData = begin_data + base64Data\r\n          \r\n          // 添加到照片列表\r\n          this.photoList.push({\r\n            imageData: this.currentImageData,\r\n            path: this.currentPhotoPath,\r\n            timestamp: new Date().getTime()\r\n          })\r\n          \r\n          // 使用高质量 base64 数据进行 OCR 识别\r\n          console.log('使用高质量 base64 数据进行 OCR 识别')\r\n          this.$message.info('正在识别证件号码...')\r\n          this.processOcrWithImage(this.currentImageData)\r\n        } else {\r\n          console.error('获取到的 base64 数据无效或太短')\r\n          \r\n          // 如果 base64 数据无效，尝试使用预览图或文件路径\r\n          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \r\n              this.$refs.scannerPreview.src.startsWith('data:image')) {\r\n            console.log('使用预览图数据进行 OCR 识别')\r\n            const imageData = this.$refs.scannerPreview.src\r\n            \r\n            // 添加到照片列表\r\n            this.photoList.push({\r\n              imageData: imageData,\r\n              path: this.currentPhotoPath,\r\n              timestamp: new Date().getTime()\r\n            })\r\n            \r\n            this.processOcrWithImage(imageData)\r\n          } else {\r\n            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\r\n            \r\n            // 添加到照片列表（使用空图像数据）\r\n            this.photoList.push({\r\n              imageData: '',\r\n              path: this.currentPhotoPath,\r\n              timestamp: new Date().getTime()\r\n            })\r\n            \r\n            this.processOcrWithImage(this.currentPhotoPath)\r\n          }\r\n        }\r\n      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {\r\n        // 处理目录检查结果\r\n        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')\r\n        console.log('目录检查结果:', result)\r\n        // 如果目录不存在，结果为\"0\"，存在则为\"1\"\r\n        if (result === \"0\") {\r\n          console.log('C:\\\\pic\\\\ 目录不存在，将创建')\r\n        }\r\n      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {\r\n        // 处理创建目录结果\r\n        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')\r\n        console.log('创建目录结果:', result)\r\n        // 如果创建成功，结果为\"1\"，失败则为\"0\"\r\n        if (result === \"1\") {\r\n          console.log('C:\\\\pic\\\\ 目录创建成功')\r\n        } else {\r\n          console.warn('C:\\\\pic\\\\ 目录创建失败或已存在')\r\n        }\r\n      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {\r\n        // 处理条码识别命令响应，不作为图像数据处理\r\n        console.log('收到条码识别命令响应:', event.data)\r\n      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {\r\n        // 处理图像数据 - 判断是否为base64图像数据\r\n        // 增加额外检查，确保不是条码数据\r\n        if (this.$refs.scannerPreview) {\r\n          try {\r\n            // 尝试验证是否为有效的base64图像数据\r\n            const testData = event.data.substring(0, 100); // 只取前100个字符测试\r\n            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常\r\n            \r\n            // 确保是完整的base64数据\r\n            const imgData = begin_data + event.data\r\n            this.$refs.scannerPreview.src = imgData\r\n            \r\n            // 保存当前图像数据以备后用\r\n            this.currentImageData = imgData\r\n            \r\n            // 保存当前图像数据到合同文件\r\n            this.contractForm.contractFile = imgData\r\n            console.log('成功保存图像数据，长度:', event.data.length)\r\n          } catch (e) {\r\n            console.error('收到的数据不是有效的base64图像:', e)\r\n          }\r\n        }\r\n      } else {\r\n        // 其他消息，可能是普通文本或命令响应\r\n        console.log('收到高拍仪消息:', event.data)\r\n      }\r\n    },\r\n    \r\n    // 启动高拍仪\r\n    startScanner() {\r\n      if (!this.scannerConnected) {\r\n        this.initScannerWebSocket()\r\n        return\r\n      }\r\n      \r\n      try {\r\n        // 设置分辨率\r\n        this.sendScannerCommand('vSetResolution(8)')\r\n        // 启用去黑边功能\r\n        this.sendScannerCommand('vSetDelHBFlag(true)')\r\n        // 启动主摄像头\r\n        this.sendScannerCommand('bStartPlay()')\r\n        this.$message.success('高拍仪已启动')\r\n      } catch (error) {\r\n        console.error('启动高拍仪失败', error)\r\n        this.$message.error('启动高拍仪失败')\r\n      }\r\n    },\r\n    \r\n    // 停止高拍仪\r\n    stopScanner() {\r\n      if (!this.scannerConnected) {\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.sendScannerCommand('bStopPlay()')\r\n        this.$message.success('高拍仪已停止')\r\n      } catch (error) {\r\n        console.error('停止高拍仪失败', error)\r\n        this.$message.error('停止高拍仪失败')\r\n      }\r\n    },\r\n    \r\n    // 扫描合同\r\n    scanContract() {\r\n      if (!this.scannerConnected && !this.simulationMode) {\r\n        this.$message.warning('请先启动高拍仪')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        if (this.simulationMode) {\r\n          // 模拟模式下，直接使用示例图片\r\n          this.processWithSimulationImage()\r\n          // 自动进行OCR识别\r\n          setTimeout(() => {\r\n            this.processOcrWithSimulationImage()\r\n          }, 1000)\r\n          return\r\n        }\r\n        \r\n        // 设置更高的分辨率 - 使用最高分辨率以确保合同完整清晰\r\n        // 分辨率值: 1=320*240, 2=640*480, 3=800*600, 4=1024*768, 5=1280*1024, 6=1600*1200, 7=2048*1536, 8=2592*1944\r\n        this.sendScannerCommand('vSetResolution(8)')\r\n        \r\n        // 确保启用去黑边功能\r\n        this.sendScannerCommand('vSetDelHBFlag(true)')\r\n        \r\n        // 设置A4文档模式 - 使用文档模式而不是证件模式\r\n        //this.sendScannerCommand('bSetMode(2)')\r\n        \r\n        // 设置自动裁剪模式 - 确保合同完整捕获\r\n        this.sendScannerCommand('vSetAutoCrop(true)')\r\n        \r\n        // 设置图像增强 - 提高清晰度\r\n        this.sendScannerCommand('vSetImageEnhance(true)')\r\n        \r\n        // 先检查目录是否存在，不存在则创建\r\n        this.sendScannerCommand('bDirIsExist(C:\\\\pic\\\\)')\r\n        \r\n        // 延迟一下，确保目录检查完成\r\n        setTimeout(() => {\r\n          // 创建目录（即使目录已存在，这个命令也不会报错）\r\n          this.sendScannerCommand('bCreateDir(C:\\\\pic\\\\)')\r\n          \r\n          // 生成唯一文件名（使用时间戳）\r\n          const timestamp = new Date().getTime()\r\n          const filename = `contract_${timestamp}`\r\n          this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\r\n          console.log('当前照片路径:', this.currentPhotoPath) \r\n          \r\n          // 拍照前提示用户\r\n          this.$message.info('正在拍摄合同，请确保文档完全平整并位于取景框内...')\r\n          \r\n          // 短暂延迟后拍照，给用户时间调整文档位置\r\n          setTimeout(() => {\r\n            // 拍照并保存到本地\r\n            this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\r\n            \r\n            // 清除之前的图像数据，确保不会使用旧数据\r\n            this.currentImageData = null\r\n            \r\n            this.$message.info('合同拍摄中，请稍候...')\r\n            \r\n            // 设置一个标志，表示我们正在等待 Base64Encode 的响应\r\n            this.waitingForBase64 = true\r\n            \r\n            // 延迟一下，确保图片保存完成\r\n            setTimeout(() => {\r\n              // 使用 Base64Encode 命令获取高质量的 base64 图像数据\r\n              this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)\r\n              \r\n              // 设置超时，确保即使没有收到 Base64Encode 的响应，也会继续处理\r\n              setTimeout(() => {\r\n                if (this.waitingForBase64) {\r\n                  console.log('Base64Encode 响应超时，使用备用方法')\r\n                  this.waitingForBase64 = false\r\n                  \r\n                  // 如果有预览图数据，使用预览图数据\r\n                  if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \r\n                      this.$refs.scannerPreview.src.startsWith('data:image')) {\r\n                    console.log('使用预览图数据')\r\n                    const imageData = this.$refs.scannerPreview.src\r\n                    \r\n                    // 添加到照片列表\r\n                    this.photoList.push({\r\n                      imageData: imageData,\r\n                      path: this.currentPhotoPath,\r\n                      timestamp: new Date().getTime()\r\n                    })\r\n                    \r\n                    // 调用OCR识别\r\n                    this.$message.info('正在识别证件号码...')\r\n                    this.processOcrWithImage(imageData)\r\n                  } else {\r\n                    // 如果没有图像数据，尝试使用文件路径\r\n                    console.log('使用文件路径:', this.currentPhotoPath)\r\n                    \r\n                    // 添加到照片列表（使用空图像数据）\r\n                    this.photoList.push({\r\n                      imageData: '',\r\n                      path: this.currentPhotoPath,\r\n                      timestamp: new Date().getTime()\r\n                    })\r\n                    \r\n                    // 调用OCR识别\r\n                    this.$message.info('正在识别证件号码...')\r\n                    this.processOcrWithImage(this.currentPhotoPath)\r\n                  }\r\n                }\r\n              }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法\r\n              \r\n            }, 1000) // 延迟1秒，确保图片保存完成\r\n          }, 500) // 给用户半秒钟时间调整文档位置\r\n        }, 500) // 延迟500ms，确保目录检查完成\r\n      } catch (error) {\r\n        console.error('扫描合同失败', error)\r\n        this.$message.error('扫描合同失败')\r\n      }\r\n    },\r\n    \r\n    // 模拟模式下使用示例图片\r\n    processWithSimulationImage() {\r\n      this.$message.info('模拟模式：请上传图片或使用高拍仪')\r\n      \r\n      // 设置空白图像\r\n      if (this.$refs.scannerPreview) {\r\n        // 创建一个空白的Canvas\r\n        const canvas = document.createElement('canvas')\r\n        canvas.width = 600\r\n        canvas.height = 400\r\n        const ctx = canvas.getContext('2d')\r\n        \r\n        // 填充浅灰色背景\r\n        ctx.fillStyle = '#f0f0f0'\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height)\r\n        \r\n        // 添加提示文字\r\n        ctx.fillStyle = '#666666'\r\n        ctx.font = '20px Arial'\r\n        ctx.textAlign = 'center'\r\n        ctx.fillText('请上传图片或使用高拍仪', canvas.width / 2, canvas.height / 2)\r\n        \r\n        // 转换为数据URL\r\n        const emptyImageUrl = canvas.toDataURL('image/jpeg')\r\n        this.$refs.scannerPreview.src = emptyImageUrl\r\n        this.contractForm.contractFile = emptyImageUrl\r\n      }\r\n    },\r\n    \r\n    // 发送高拍仪命令\r\n    sendScannerCommand(command) {\r\n      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {\r\n        this.webSocket.send(command)\r\n      } else {\r\n        throw new Error('WebSocket未连接')\r\n      }\r\n    },\r\n    \r\n    // 关闭WebSocket连接\r\n    closeWebSocket() {\r\n      if (this.webSocket) {\r\n        // 先停止高拍仪\r\n        if (this.scannerConnected) {\r\n          try {\r\n            this.webSocket.send('bStopPlay()')\r\n          } catch (e) {\r\n            console.error('停止高拍仪失败', e)\r\n          }\r\n        }\r\n        \r\n        // 关闭连接\r\n        this.webSocket.close()\r\n        this.webSocket = null\r\n        this.scannerConnected = false\r\n      }\r\n    },\r\n    \r\n    // 切换到模拟模式\r\n    switchToSimulationMode() {\r\n      this.simulationMode = true;\r\n      this.scannerConnected = false;\r\n      \r\n      if (this.webSocket) {\r\n        this.manuallyDisconnected = true;\r\n        this.webSocket.close();\r\n        this.webSocket = null;\r\n      }\r\n      \r\n      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动上传图片或使用模拟功能。');\r\n      \r\n      // 清除连接超时\r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n      }\r\n    },\r\n\r\n    // 上传图片处理函数\r\n    handleUploadImage(file) {\r\n      if (file) {\r\n        // 验证文件类型\r\n        const isImage = file.type.indexOf('image/') !== -1;\r\n        if (!isImage) {\r\n          this.$message.error('请上传图片文件!');\r\n          return false;\r\n        }\r\n        \r\n        // 验证文件大小 (限制为10MB)\r\n        const isLt10M = file.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n          this.$message.error('图片大小不能超过10MB!');\r\n          return false;\r\n        }\r\n        \r\n        this.$message.info('正在处理图片，请稍候...');\r\n        \r\n        // 更新预览图并压缩图片\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = (e) => {\r\n          // 压缩图片\r\n          this.compressImage(e.target.result, (compressedDataUrl) => {\r\n            // 更新预览图\r\n            if (this.$refs.scannerPreview) {\r\n              this.$refs.scannerPreview.src = compressedDataUrl;\r\n            }\r\n            \r\n            // 添加到照片列表\r\n            this.photoList.push({\r\n              imageData: compressedDataUrl,\r\n              path: '',\r\n              timestamp: new Date().getTime(),\r\n              isIdCard: false\r\n            });\r\n            \r\n            this.$message.info('图片上传成功，正在识别证件号码...');\r\n            \r\n            // 调用OCR识别\r\n            this.processOcrWithImage(compressedDataUrl);\r\n          });\r\n        };\r\n        \r\n        reader.onerror = (error) => {\r\n          console.error('读取图片文件失败', error);\r\n          this.$message.error('读取图片文件失败');\r\n        };\r\n      }\r\n      return false; // 阻止默认的上传行为\r\n    },\r\n    \r\n    // 压缩图片函数\r\n    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {\r\n      const img = new Image();\r\n      img.src = dataUrl;\r\n      \r\n      img.onload = () => {\r\n        // 创建Canvas\r\n        const canvas = document.createElement('canvas');\r\n        let width = img.width;\r\n        let height = img.height;\r\n        \r\n        // 计算缩放比例\r\n        if (width > height) {\r\n          if (width > maxWidth) {\r\n            height *= maxWidth / width;\r\n            width = maxWidth;\r\n          }\r\n        } else {\r\n          if (height > maxHeight) {\r\n            width *= maxHeight / height;\r\n            height = maxHeight;\r\n          }\r\n        }\r\n        \r\n        // 设置Canvas大小\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n        \r\n        // 绘制图像\r\n        const ctx = canvas.getContext('2d');\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n        \r\n        // 转换为压缩后的DataURL\r\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\r\n        \r\n        // 计算压缩率\r\n        const originalSize = dataUrl.length;\r\n        const compressedSize = compressedDataUrl.length;\r\n        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);\r\n        \r\n        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);\r\n        \r\n        callback(compressedDataUrl);\r\n      };\r\n      \r\n      img.onerror = () => {\r\n        console.error('图片压缩失败');\r\n        callback(dataUrl); // 失败时使用原始图片\r\n      };\r\n    },\r\n    \r\n    // 提交合同\r\n    submitContract() {\r\n      this.$refs.contractForm.validate(async valid => {\r\n        if (valid) {\r\n          if (this.contractForm.attachments.length === 0 && this.photoList.length === 0) {\r\n            this.$message.warning('请先拍摄或上传合同图片');\r\n            return;\r\n          }\r\n          \r\n          if (!this.idCardFound && !this.contractForm.idCardNumber) {\r\n            this.$message.warning('请先识别身份证或手动输入证件号码');\r\n            return;\r\n          }\r\n          \r\n          this.isProcessingUpload = true;\r\n          this.$message.info('正在上传合同，请稍候...');\r\n          \r\n          // 如果有拍摄的照片但还没有生成PDF，先生成PDF\r\n          if (this.photoList.length > 0 && this.contractForm.attachments.length === 0) {\r\n            this.convertImagesToPdf();\r\n            this.$message.info('请等待PDF生成完成后再提交');\r\n            this.isProcessingUpload = false;\r\n            return;\r\n          }\r\n          \r\n          // 打印提交前的contractForm，检查projectSubContractorId是否存在\r\n          console.log('提交前的contractForm:', JSON.stringify(this.contractForm))\r\n          console.log('参建单位ID (projectSubContractorId):', this.contractForm.projectSubContractorId)\r\n          \r\n          // 准备合同信息\r\n          const contract = {\r\n            corpCode: this.contractForm.projectSubContractorId || '',\r\n            corpName: this.contractForm.projectSubContractorName || '',\r\n            idCardType: '01', // 默认身份证\r\n            idNumber: this.contractForm.idCardNumber,\r\n            workerId: this.contractForm.workerId, // 工人ID\r\n            workerName: this.contractForm.workerName, // 工人姓名\r\n            teamNo: this.contractForm.teamCode, // 班组编号 \r\n            teamName: this.contractForm.teamName, // 班组名称\r\n            contractPeriodType: this.getSalaryTypeValue(this.contractForm.contractPeriodType),\r\n            startDate: this.formatDate(this.contractForm.contractStartDate),\r\n            endDate: this.formatDate(this.contractForm.contractEndDate),\r\n            signDate: this.formatDate(this.contractForm.contractSignDate || new Date()),\r\n            unit: this.getSalaryTypeValue(this.contractForm.salaryType), // 按日传1，按月传2，按工程量传3\r\n            unitPrice: Number(this.contractForm.salaryUnitPrice) || 100\r\n          };\r\n          \r\n          // 打印最终的contract对象，检查corpCode是否正确设置\r\n          console.log('最终的contract对象:', JSON.stringify(contract))\r\n          console.log('corpCode值:', contract.corpCode)\r\n          \r\n          // 如果是按工程量计算，添加每xx和计量单位信息\r\n          if (this.contractForm.salaryType === '按工程量') {\r\n            contract.perUnit = this.contractForm.salaryPerUnit || 1;\r\n            contract.unitType = this.contractForm.salaryUnitType || 80;\r\n          }\r\n          \r\n          // 准备附件信息\r\n          const attachments = [];\r\n          \r\n          try {\r\n            // 处理所有附件\r\n            if (this.contractForm.attachments && this.contractForm.attachments.length > 0) {\r\n              for (const attachment of this.contractForm.attachments) {\r\n                // 获取base64数据\r\n                let base64Data = '';\r\n                if (attachment.data) {\r\n                  if (typeof attachment.data === 'string' && attachment.data.startsWith('data:')) {\r\n                    // 已经是base64格式\r\n                    base64Data = attachment.data.split(',')[1]; // 移除\"data:application/pdf;base64,\"前缀\r\n                  } else if (attachment.file) {\r\n                    // 如果有base64属性，直接使用\r\n                    if (attachment.base64) {\r\n                      base64Data = attachment.base64.split(',')[1];\r\n                    } else {\r\n                      // 否则从文件读取\r\n                      base64Data = await this.readFileAsBase64Promise(attachment.file);\r\n                    }\r\n                  }\r\n                }\r\n                \r\n                attachments.push({\r\n                  name: attachment.name,\r\n                  data: base64Data\r\n                });\r\n              }\r\n            }\r\n            \r\n            // 准备请求数据\r\n            const requestData = {\r\n              projectCode: this.contractForm.projectCode,\r\n              contractList: [contract],\r\n              attachments: attachments\r\n            };\r\n            \r\n            // 打印最终的请求数据\r\n            console.log('上传合同的请求数据:', JSON.stringify({\r\n              projectCode: requestData.projectCode,\r\n              contractList: requestData.contractList,\r\n              attachmentsCount: requestData.attachments.length\r\n            }))\r\n            \r\n            // 调用上传API\r\n            uploadContract(requestData)\r\n              .then(response => {\r\n                if (response.code === 0) {\r\n                  this.$message.success('合同上传成功');\r\n                  // 返回上一页\r\n                  this.goBack();\r\n                } else {\r\n                  this.$message.error(response.message || '合同上传失败');\r\n                }\r\n              })\r\n              .catch(error => {\r\n                console.error('合同上传失败:', error);\r\n                this.$message.error('合同上传失败: ' + (error.message || '未知错误'));\r\n              })\r\n              .finally(() => {\r\n                this.isProcessingUpload = false;\r\n              });\r\n          } catch (error) {\r\n            console.error('处理附件失败:', error);\r\n            this.$message.error('处理附件失败: ' + (error.message || '未知错误'));\r\n            this.isProcessingUpload = false;\r\n          }\r\n        } else {\r\n          this.$message.warning('请完善合同信息');\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 读取文件为Base64的Promise版本\r\n    readFileAsBase64Promise(file) {\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onload = e => {\r\n          const base64Data = e.target.result.split(',')[1];\r\n          resolve(base64Data);\r\n        };\r\n        reader.onerror = e => reject(e);\r\n        reader.readAsDataURL(file);\r\n      });\r\n    },\r\n    \r\n    // 获取工资类型对应的值\r\n    getSalaryTypeValue(type) {\r\n      const typeMap = {\r\n        '按天': '1',\r\n        '按月': '2',\r\n        '按工程量': '3',\r\n        '固定期限': '0',\r\n        '无固定期限': '1',\r\n        '以完成一定工作任务为期限': '1'\r\n      };\r\n      return typeMap[type] || '1';\r\n    },\r\n    \r\n    // 获取工资类型对应的文本\r\n    getSalaryTypeText(type) {\r\n      const typeMap = {\r\n        '1': '按天',\r\n        '2': '按月',\r\n        '3': '按工程量',\r\n        '4': '其他方式'\r\n      };\r\n      return typeMap[type] || '按天';\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '';\r\n      const d = new Date(date);\r\n      const year = d.getFullYear();\r\n      const month = String(d.getMonth() + 1).padStart(2, '0');\r\n      const day = String(d.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    \r\n    // 预览附件\r\n    previewAttachment(attachment) {\r\n      if (attachment && attachment.data) {\r\n        // 判断文件类型\r\n        if (attachment.type && attachment.type.includes('image/')) {\r\n          // 图片类型 - 在弹窗中预览\r\n          this.$msgbox({\r\n            title: attachment.name,\r\n            message: `<div style=\"text-align:center;height:100%;width:100%;display:flex;align-items:center;justify-content:center;\">\r\n              <img src=\"${attachment.data}\" style=\"max-width:100%;max-height:100%;object-fit:contain;\" />\r\n            </div>`,\r\n            dangerouslyUseHTMLString: true,\r\n            customClass: 'image-preview-dialog pdf-preview-dialog', // 使用相同的最大化样式\r\n            showCancelButton: false,\r\n            showConfirmButton: false\r\n          });\r\n        } else if (attachment.type === 'application/pdf') {\r\n          try {\r\n            // PDF类型 - 创建Blob URL并使用对象标签预览\r\n            const pdfBlob = this.dataURLtoBlob(attachment.data);\r\n            const blobUrl = URL.createObjectURL(pdfBlob);\r\n            \r\n            // 使用MessageBox组件以最大化方式显示PDF\r\n            this.$msgbox({\r\n              title: attachment.name,\r\n              message: `<div style=\"height:100%;width:100%;\">\r\n                <object \r\n                  data=\"${blobUrl}\" \r\n                  type=\"application/pdf\" \r\n                  width=\"100%\" \r\n                  height=\"100%\"\r\n                  style=\"width:100%;height:100%;\">\r\n                    <p>您的浏览器不支持PDF预览，请 \r\n                      <a href=\"${blobUrl}\" download=\"${attachment.name}\">点击下载</a>\r\n                    </p>\r\n                </object>\r\n              </div>`,\r\n              dangerouslyUseHTMLString: true,\r\n              customClass: 'pdf-preview-dialog',\r\n              showCancelButton: false,\r\n              showConfirmButton: false,\r\n              beforeClose: (action, instance, done) => {\r\n                // 关闭对话框时释放blob URL\r\n                URL.revokeObjectURL(blobUrl);\r\n                done();\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error('预览PDF失败:', error);\r\n            this.$message.error('无法预览PDF文件: ' + error.message);\r\n            \r\n            // 如果有原始文件对象，尝试直接下载\r\n            if (attachment.file) {\r\n              const url = URL.createObjectURL(attachment.file);\r\n              const link = document.createElement('a');\r\n              link.href = url;\r\n              link.download = attachment.name;\r\n              link.click();\r\n              setTimeout(() => URL.revokeObjectURL(url), 100);\r\n            }\r\n          }\r\n        } else {\r\n          // 其他类型 - 尝试下载文件\r\n          try {\r\n            const link = document.createElement('a');\r\n            \r\n            // 检查是否是Blob URL\r\n            if (attachment.data instanceof Blob || attachment.data.startsWith('blob:')) {\r\n              link.href = attachment.data;\r\n            } else if (attachment.file) {\r\n              // 如果有原始文件对象，使用它创建URL\r\n              link.href = URL.createObjectURL(attachment.file);\r\n            } else {\r\n              // 尝试将data URL转换为Blob URL\r\n              const blob = this.dataURLtoBlob(attachment.data);\r\n              link.href = URL.createObjectURL(blob);\r\n            }\r\n            \r\n            link.download = attachment.name;\r\n            link.click();\r\n            \r\n            // 如果创建了Blob URL，需要释放\r\n            if (link.href.startsWith('blob:')) {\r\n              setTimeout(() => URL.revokeObjectURL(link.href), 100);\r\n            }\r\n          } catch (error) {\r\n            console.error('下载文件失败:', error);\r\n            this.$message.error('无法下载文件: ' + error.message);\r\n          }\r\n        }\r\n      } else {\r\n        this.$message.warning('无法预览该附件，附件数据不完整');\r\n      }\r\n    },\r\n    \r\n    // 将Data URL转换为Blob对象\r\n    dataURLtoBlob(dataURL) {\r\n      try {\r\n        // 检查是否是有效的data URL格式\r\n        if (!dataURL || typeof dataURL !== 'string' || !dataURL.includes(';base64,')) {\r\n          console.error('无效的Data URL格式:', dataURL);\r\n          throw new Error('无效的Data URL格式');\r\n        }\r\n        \r\n        // 分割Data URL，获取MIME类型和base64数据\r\n        const parts = dataURL.split(';base64,');\r\n        const contentType = parts[0].split(':')[1];\r\n        const raw = window.atob(parts[1]);\r\n        const rawLength = raw.length;\r\n        const uInt8Array = new Uint8Array(rawLength);\r\n        \r\n        // 将base64数据转换为Uint8Array\r\n        for (let i = 0; i < rawLength; ++i) {\r\n          uInt8Array[i] = raw.charCodeAt(i);\r\n        }\r\n        \r\n        return new Blob([uInt8Array], { type: contentType });\r\n      } catch (error) {\r\n        console.error('转换Data URL到Blob失败:', error);\r\n        throw new Error('转换Data URL到Blob失败: ' + error.message);\r\n      }\r\n    },\r\n    \r\n    // 删除附件\r\n    removeAttachment(index) {\r\n      this.$confirm('确定要删除此附件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 释放URL对象\r\n        if (this.contractForm.attachments[index].data) {\r\n          URL.revokeObjectURL(this.contractForm.attachments[index].data);\r\n        }\r\n        // 从数组中删除\r\n        this.contractForm.attachments.splice(index, 1);\r\n        this.$message.success('附件已删除');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 扫描身份证\r\n    scanIdCard() {\r\n      if (!this.scannerConnected && !this.simulationMode) {\r\n        this.$message.warning('请先启动高拍仪')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        if (this.simulationMode) {\r\n          // 模拟模式下，直接调用OCR接口处理示例图片\r\n          this.processOcrWithSimulationImage()\r\n          return\r\n        }\r\n        \r\n        // 设置身份证自动寻边模式\r\n        this.sendScannerCommand('bSetMode(4)')\r\n        \r\n        // 生成唯一文件名（使用时间戳）\r\n        const timestamp = new Date().getTime()\r\n        const filename = `idcard_${timestamp}`\r\n        this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\r\n        console.log('当前照片路径:', this.currentPhotoPath) \r\n        \r\n        // 拍照并保存到本地\r\n        this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\r\n        \r\n        // 识别条码\r\n        this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)\r\n        \r\n        // 调用OCR识别\r\n        setTimeout(() => {\r\n          this.processOcrWithImage(this.currentPhotoPath)\r\n        }, 1000) // 延迟1秒，确保图片保存完成\r\n        \r\n        this.$message.info('正在识别身份证，请稍候...')\r\n      } catch (error) {\r\n        console.error('扫描身份证失败', error)\r\n        this.$message.error('扫描身份证失败')\r\n      }\r\n    },\r\n\r\n    // 处理OCR识别结果\r\n    processOcrWithImage(imagePath) {\r\n      if (this.isProcessingOcr) {\r\n        return\r\n      }\r\n      \r\n      this.isProcessingOcr = true\r\n      this.$message.info('正在进行OCR识别...')\r\n      \r\n      // 准备表单数据\r\n      const formData = new FormData()\r\n      \r\n      // 判断是否是base64格式的图片数据\r\n      if (imagePath.startsWith('data:image')) {\r\n        // 创建文件对象从base64数据\r\n        const base64Data = imagePath.split(',')[1]\r\n        \r\n        // 检查是否是从 Base64Encode 命令获取的高质量图像数据\r\n        const isHighQualityBase64 = this.currentImageData === imagePath && !this.waitingForBase64;\r\n        \r\n        if (isHighQualityBase64) {\r\n          // 如果是高质量 base64 数据，直接使用，不需要额外处理\r\n          console.log('使用高质量 base64 数据，跳过图像处理')\r\n          \r\n          const byteCharacters = atob(base64Data)\r\n          const byteArrays = []\r\n          \r\n          for (let i = 0; i < byteCharacters.length; i++) {\r\n            byteArrays.push(byteCharacters.charCodeAt(i))\r\n          }\r\n          \r\n          const byteArray = new Uint8Array(byteArrays)\r\n          const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n          \r\n          // 创建文件对象\r\n          const fileName = `contract_${new Date().getTime()}.jpg`\r\n          const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n          \r\n          // 添加到表单\r\n          formData.append('image', file)\r\n          console.log('发送高质量 base64 数据进行 OCR 识别')\r\n          \r\n          // 同时保留 base64 数据作为备用\r\n          formData.append('image_base64', imagePath)\r\n          \r\n          // 调用 OCR API\r\n          this.callOcrApi(formData)\r\n        } else {\r\n          // 在发送前处理图像，去除紫色边框和文字标记\r\n          this.preprocessImage(imagePath).then(processedImageData => {\r\n            // 使用处理后的图像数据\r\n            const processedBase64 = processedImageData.split(',')[1]\r\n            const byteCharacters = atob(processedBase64)\r\n            const byteArrays = []\r\n            \r\n            for (let i = 0; i < byteCharacters.length; i++) {\r\n              byteArrays.push(byteCharacters.charCodeAt(i))\r\n            }\r\n            \r\n            const byteArray = new Uint8Array(byteArrays)\r\n            const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n            \r\n            // 创建文件对象\r\n            const fileName = `contract_${new Date().getTime()}.jpg`\r\n            const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n            \r\n            // 添加到表单\r\n            formData.append('image', file)\r\n            console.log('发送处理后的文件对象进行 OCR 识别')\r\n            \r\n            // 同时保留处理后的base64数据作为备用\r\n            formData.append('image_base64', processedImageData)\r\n            \r\n            // 调用OCR API\r\n            this.callOcrApi(formData)\r\n          }).catch(error => {\r\n            console.error('图像预处理失败，使用原始图像:', error)\r\n            \r\n            // 如果处理失败，使用原始图像\r\n            const byteCharacters = atob(base64Data)\r\n            const byteArrays = []\r\n            \r\n            for (let i = 0; i < byteCharacters.length; i++) {\r\n              byteArrays.push(byteCharacters.charCodeAt(i))\r\n            }\r\n            \r\n            const byteArray = new Uint8Array(byteArrays)\r\n            const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n            \r\n            // 创建文件对象\r\n            const fileName = `contract_${new Date().getTime()}.jpg`\r\n            const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n            \r\n            // 添加到表单\r\n            formData.append('image', file)\r\n            console.log('发送原始文件对象进行 OCR 识别')\r\n            \r\n            // 同时保留base64数据作为备用\r\n            formData.append('image_base64', imagePath)\r\n            \r\n            // 调用OCR API\r\n            this.callOcrApi(formData)\r\n          })\r\n        }\r\n      } else {\r\n        // 如果是文件路径，尝试读取文件并上传\r\n        formData.append('image_path', imagePath)\r\n        console.log(`发送图片路径进行OCR识别: ${imagePath}`)\r\n        \r\n        // 调用OCR API\r\n        this.callOcrApi(formData)\r\n      }\r\n    },\r\n    \r\n    // 调用OCR API\r\n    callOcrApi(formData) {\r\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\r\n        .then(response => {\r\n          this.handleOcrResult(response.data)\r\n        })\r\n        .catch(error => {\r\n          console.error('OCR识别失败', error)\r\n          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\r\n        })\r\n        .finally(() => {\r\n          this.isProcessingOcr = false\r\n        })\r\n    },\r\n    \r\n    // 图像预处理函数 - 去除紫色边框和文字标记\r\n    preprocessImage(base64Image) {\r\n      return new Promise((resolve, reject) => {\r\n        try {\r\n          // 创建图像对象\r\n          const img = new Image()\r\n          img.onload = () => {\r\n            // 创建Canvas\r\n            const canvas = document.createElement('canvas')\r\n            canvas.width = img.width\r\n            canvas.height = img.height\r\n            const ctx = canvas.getContext('2d')\r\n            \r\n            // 绘制图像到Canvas\r\n            ctx.drawImage(img, 0, 0)\r\n            \r\n            // 获取图像数据\r\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n            const data = imageData.data\r\n            \r\n            // 处理紫色边框 - 将紫色像素替换为白色\r\n            for (let i = 0; i < data.length; i += 4) {\r\n              const r = data[i]\r\n              const g = data[i + 1]\r\n              const b = data[i + 2]\r\n              \r\n              // 检测紫色范围 (RGB近似值)\r\n              // 紫色通常R和B较高，G较低\r\n              if ((r > 100 && r < 200) && (g < 100) && (b > 100 && b < 200)) {\r\n                // 将紫色替换为白色\r\n                data[i] = 255     // R\r\n                data[i + 1] = 255 // G\r\n                data[i + 2] = 255 // B\r\n              }\r\n            }\r\n            \r\n            // 将处理后的图像数据放回Canvas\r\n            ctx.putImageData(imageData, 0, 0)\r\n            \r\n            // 尝试去除文字标记 (简化处理)\r\n            // 这里使用简单的边缘检测和阈值处理\r\n            // 获取灰度图像\r\n            const grayImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n            const grayData = grayImageData.data\r\n            \r\n            // 转换为灰度图\r\n            for (let i = 0; i < grayData.length; i += 4) {\r\n              const avg = (grayData[i] + grayData[i + 1] + grayData[i + 2]) / 3\r\n              \r\n              // 应用阈值，将可能的文字区域变白\r\n              if (avg < 180) { // 较暗的区域可能是文字\r\n                // 检查周围像素，如果是小区域的暗像素，可能是文字\r\n                // 这是一个简化的处理，实际应用中可能需要更复杂的算法\r\n                grayData[i] = 255     // R\r\n                grayData[i + 1] = 255 // G\r\n                grayData[i + 2] = 255 // B\r\n              }\r\n            }\r\n            \r\n            // 将处理后的图像数据转换为base64\r\n            const processedBase64 = canvas.toDataURL('image/jpeg', 0.95)\r\n            \r\n            console.log('图像预处理完成')\r\n            resolve(processedBase64)\r\n          }\r\n          \r\n          img.onerror = (error) => {\r\n            console.error('图像加载失败:', error)\r\n            reject(error)\r\n          }\r\n          \r\n          // 设置图像源\r\n          img.src = base64Image\r\n        } catch (error) {\r\n          console.error('图像预处理失败:', error)\r\n          reject(error)\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 模拟模式下使用示例图片进行OCR识别\r\n    processOcrWithSimulationImage() {\r\n      this.isProcessingOcr = true\r\n      this.$message.info('模拟模式：正在进行OCR识别...')\r\n      \r\n      // 准备表单数据\r\n      const formData = new FormData()\r\n      formData.append('simulation', 'true')\r\n      \r\n      // 调用OCR API\r\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\r\n        .then(response => {\r\n          this.handleOcrResult(response.data)\r\n        })\r\n        .catch(error => {\r\n          console.error('模拟OCR识别失败', error)\r\n          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))\r\n        })\r\n        .finally(() => {\r\n          this.isProcessingOcr = false\r\n        })\r\n    },\r\n    \r\n    // 处理OCR识别结果\r\n    handleOcrResult(result) {\r\n      if (!result || !result.success) {\r\n        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))\r\n        return\r\n      }\r\n      \r\n      this.$message.success('OCR识别成功')\r\n      console.log('OCR识别结果:', result)\r\n      \r\n      // 更新表单数据\r\n      const ocrData = result.data || {}\r\n      \r\n      // 更新身份证号\r\n      if (ocrData.id_number) {\r\n        this.contractForm.idCardNumber = ocrData.id_number\r\n        // 添加更明显的提示\r\n        this.$notify({\r\n          title: '证件号码识别成功',\r\n          message: ocrData.id_number,\r\n          type: 'success',\r\n          duration: 5000\r\n        })\r\n        \r\n        // 标记已找到身份证\r\n        this.idCardFound = true\r\n        \r\n        // 如果有身份证号，尝试从系统中查询更多信息\r\n        this.searchWorkerByIdCard(ocrData.id_number)\r\n      }\r\n      \r\n      // 更新姓名\r\n      if (ocrData.name) {\r\n        this.contractForm.workerName = ocrData.name\r\n      }\r\n    },\r\n    \r\n    // 验证是否为身份证号\r\n    isIdCardNumber(str) {\r\n      // 简单验证18位或15位身份证号\r\n      const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\r\n      return reg.test(str)\r\n    },\r\n    \r\n    // 根据身份证号查询工人信息\r\n    searchWorkerByIdCard(idCardNumber) {\r\n      if (!idCardNumber) {\r\n        this.$message.warning('证件号码不能为空')\r\n        return\r\n      }\r\n      \r\n      this.isLoadingWorkerInfo = true\r\n      this.$message.info('正在查询工人信息...')\r\n      \r\n      // 调用API根据身份证号查询工人信息\r\n      getWorkerByIdCard(idCardNumber)\r\n        .then(response => {\r\n          if (response.code === 0 && response.data) {\r\n            const workerData = response.data\r\n            \r\n            // 打印API返回的完整数据，查看是否包含参建单位ID相关字段\r\n            console.log('API返回的工人信息:', workerData)\r\n            \r\n            // 更新表单数据\r\n            this.contractForm.workerId = workerData.workId || ''\r\n            this.contractForm.workerName = workerData.workerName || ''\r\n            \r\n            // 获取并保存班组编号和班组名称\r\n            this.contractForm.teamCode = workerData.teamNo || workerData.teamSysNo || ''\r\n            this.contractForm.teamName = workerData.teamName || workerData.teamSysName || ''\r\n            \r\n            // 填充其他工人信息\r\n            this.contractForm.ProjectName = workerData.projectName || ''\r\n            this.contractForm.projectCode = workerData.projectCode || ''\r\n            \r\n            // 尝试从多个可能的字段中获取参建单位ID\r\n            this.contractForm.projectSubContractorId = \r\n              workerData.projectSubContractorId || \r\n              workerData.corpCode || \r\n              workerData.participantCode || \r\n              workerData.corpId || \r\n              workerData.subContractorId || \r\n              '';\r\n            \r\n            console.log('可能的参建单位ID字段:',\r\n              '1.projectSubContractorId=', workerData.projectSubContractorId,\r\n              '2.corpCode=', workerData.corpCode,\r\n              '3.participantCode=', workerData.participantCode,\r\n              '4.corpId=', workerData.corpId,\r\n              '5.subContractorId=', workerData.subContractorId\r\n            );\r\n            \r\n            this.contractForm.projectSubContractorName = workerData.projectSubContractorName || workerData.corpName || ''\r\n            \r\n            // 打印保存后的projectSubContractorId\r\n            console.log('保存的参建单位ID:', this.contractForm.projectSubContractorId)\r\n            \r\n            this.contractForm.contractPeriodType = workerData.contractPeriodType || '固定期限'\r\n            this.contractForm.contractStartDate = workerData.contractStartDate ? new Date(workerData.contractStartDate) : new Date()\r\n            this.contractForm.contractEndDate = workerData.contractEndDate ? new Date(workerData.contractEndDate) : null\r\n            \r\n            // 处理工资类型 - 将数字转换为文本表示\r\n            console.log('原始工资类型值:', workerData.salaryType)\r\n            this.contractForm.salaryType = this.getSalaryTypeText(workerData.salaryType) || '按天'\r\n            console.log('转换后工资类型:', this.contractForm.salaryType)\r\n            \r\n            this.contractForm.salaryUnitPrice = workerData.salaryUnitPrice || ''\r\n            this.contractForm.salaryPerUnit = workerData.salaryPerUnit || ''\r\n            this.contractForm.salaryUnitType = workerData.salaryUnitType || ''\r\n            \r\n            // 根据工资类型更新验证规则\r\n            this.handleSalaryTypeChange(this.contractForm.salaryType)\r\n            \r\n            if (workerData.contractSignDate) {\r\n              this.contractForm.contractSignDate = new Date(workerData.contractSignDate)\r\n            }\r\n            \r\n            this.$message.success('工人信息查询成功')\r\n            \r\n            // 添加更详细的通知\r\n            this.$notify({\r\n              title: '工人信息查询成功',\r\n              message: `已找到工人: ${workerData.workerName}${this.contractForm.teamName ? ', 班组: ' + this.contractForm.teamName : ''}`,\r\n              type: 'success',\r\n              duration: 5000\r\n            })\r\n          } else {\r\n            this.$message.warning(response.msg || '未找到工人信息')\r\n            \r\n            // 如果没有找到，可以保留一些基本信息\r\n            if (this.contractForm.idCardNumber && !this.contractForm.workerName) {\r\n              // 从身份证号提取出生日期和性别信息\r\n              this.extractInfoFromIdCard(idCardNumber)\r\n            }\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error('查询工人信息失败:', error)\r\n          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))\r\n          \r\n          // 如果API调用失败，可以尝试从身份证号提取一些基本信息\r\n          if (this.contractForm.idCardNumber) {\r\n            this.extractInfoFromIdCard(idCardNumber)\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.isLoadingWorkerInfo = false\r\n        })\r\n    },\r\n    \r\n    // 从身份证号提取信息\r\n    extractInfoFromIdCard(idCardNumber) {\r\n      if (!idCardNumber || idCardNumber.length < 18) return\r\n      \r\n      try {\r\n        // 提取性别 (第17位，奇数为男，偶数为女)\r\n        const genderCode = parseInt(idCardNumber.charAt(16))\r\n        const gender = genderCode % 2 === 1 ? '男' : '女'\r\n        \r\n        console.log('从身份证号提取的性别:', gender)\r\n      } catch (e) {\r\n        console.error('从身份证号提取信息失败:', e)\r\n      }\r\n    },\r\n    \r\n    // 解析身份证号码 (示例，实际需要更复杂的OCR库)\r\n    parseIdCardNumber(base64Image) {\r\n      // 这是一个非常简化的示例，实际需要使用专业的OCR库（如Tesseract.js, PaddleOCR等）\r\n      // 这里只是模拟一个简单的解析逻辑\r\n      try {\r\n        const img = new Image();\r\n        img.src = 'data:image/jpeg;base64,' + base64Image;\r\n        img.onload = () => {\r\n          // 在实际应用中，这里会调用OCR库进行识别\r\n          // 例如：PaddleOCR.recognizeText(img.src);\r\n          // 假设识别结果包含身份证号码\r\n          const mockIdCardNumber = '123456789012345678'; // 模拟身份证号码\r\n          return mockIdCardNumber;\r\n        };\r\n      } catch (e) {\r\n        console.error('图片解析失败', e);\r\n        return null;\r\n      }\r\n    },\r\n\r\n    // 获取员工信息\r\n    fetchWorkerInfo(idCardNumber) {\r\n      // 这是一个模拟的API调用，实际需要一个真实的后端接口\r\n      // 例如：axios.get(`${this.apiBaseUrl}/api/workers/idCard/${idCardNumber}`)\r\n      // 假设成功获取到工人信息\r\n      const mockWorker = {\r\n        workerId: '', // 模拟工人ID\r\n        workerName: '', // 模拟工人姓名\r\n        idCardNumber: idCardNumber,\r\n        contractType: '',\r\n        contractSignDate: new Date(),\r\n        remark: ''\r\n      };\r\n\r\n      this.contractForm = { ...mockWorker }; // 更新合同表单\r\n      this.$message.success('已获取到工人信息！');\r\n    },\r\n\r\n    // 删除照片\r\n    removePhoto(index) {\r\n      this.$confirm('确定要删除此照片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.photoList.splice(index, 1);\r\n        this.$message.success('照片已删除');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 刷新工人信息\r\n    refreshWorkerInfo() {\r\n      if (!this.contractForm.idCardNumber) {\r\n        this.$message.warning('请先输入证件号码')\r\n        return\r\n      }\r\n      \r\n      // 重新获取工人信息\r\n      this.searchWorkerByIdCard(this.contractForm.idCardNumber)\r\n    },\r\n\r\n    // 获取计量单位标签\r\n    getUnitTypeLabel(value) {\r\n      const unitTypeMap = {\r\n        '80': '米',\r\n        '81': '平方米',\r\n        '82': '立方米'\r\n      };\r\n      return unitTypeMap[value] || '单位';\r\n    },\r\n    \r\n    // 将图片转换为PDF\r\n    convertImagesToPdf() {\r\n      if (this.photoList.length === 0) {\r\n        this.$message.warning('请先拍摄或上传合同图片');\r\n        return;\r\n      }\r\n\r\n      this.isGeneratingPDF = true;\r\n      this.$message.info('正在生成PDF，请稍候...');\r\n\r\n      // 创建一个新的PDF文档\r\n      const pdf = new jsPDF('p', 'mm', 'a4');\r\n      const promises = [];\r\n      const pageWidth = pdf.internal.pageSize.getWidth();\r\n      const pageHeight = pdf.internal.pageSize.getHeight();\r\n\r\n      // 为每张图片创建一个Promise\r\n      this.photoList.forEach((photo, index) => {\r\n        const promise = new Promise((resolve) => {\r\n          // 优先使用高质量的 base64 图像数据\r\n          if (photo.imageData && photo.imageData.startsWith('data:image')) {\r\n            // 创建一个临时的图片元素\r\n            const img = new Image();\r\n            img.src = photo.imageData;\r\n            \r\n            img.onload = () => {\r\n              // 计算图片在PDF中的尺寸，保持宽高比\r\n              let imgWidth = pageWidth - 20; // 留出10mm边距\r\n              let imgHeight = (img.height * imgWidth) / img.width;\r\n              \r\n              // 如果图片高度超过页面高度，按高度缩放\r\n              if (imgHeight > pageHeight - 20) {\r\n                imgHeight = pageHeight - 20;\r\n                imgWidth = (img.width * imgHeight) / img.height;\r\n              }\r\n              \r\n              // 如果不是第一页，添加新页\r\n              if (index > 0) {\r\n                pdf.addPage();\r\n              }\r\n              \r\n              // 将图片添加到PDF\r\n              pdf.addImage(\r\n                photo.imageData, \r\n                'JPEG', \r\n                (pageWidth - imgWidth) / 2, // 居中显示\r\n                10, // 顶部边距\r\n                imgWidth, \r\n                imgHeight\r\n              );\r\n              \r\n              resolve();\r\n            };\r\n            \r\n            img.onerror = () => {\r\n              this.$message.error(`处理第${index + 1}张图片时出错`);\r\n              resolve(); // 即使出错也继续处理其他图片\r\n            };\r\n          } else if (photo.path && photo.path.startsWith('C:\\\\')) {\r\n            // 如果没有图像数据但有本地路径，尝试获取本地图片的 base64 数据\r\n            console.log(`尝试获取本地图片的 base64 数据: ${photo.path}`);\r\n            \r\n            // 使用 Base64Encode 命令获取图片的 base64 数据\r\n            if (this.scannerConnected) {\r\n              this.sendScannerCommand(`Base64Encode(${photo.path})`);\r\n              \r\n              // 设置一个超时，如果在指定时间内没有收到响应，则跳过该图片\r\n              setTimeout(() => {\r\n                if (!photo.imageData || !photo.imageData.startsWith('data:image')) {\r\n                  this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);\r\n                  resolve();\r\n                }\r\n              }, 3000);\r\n            } else {\r\n              this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);\r\n              resolve();\r\n            }\r\n          } else {\r\n            // 如果既没有图像数据也没有本地路径，跳过该图片\r\n            this.$message.warning(`第${index + 1}张图片没有有效数据，将跳过`);\r\n            resolve();\r\n          }\r\n        });\r\n        \r\n        promises.push(promise);\r\n      });\r\n\r\n      // 当所有图片处理完成后，保存PDF\r\n      Promise.all(promises).then(() => {\r\n        // 生成文件名\r\n        const workerName = this.contractForm.workerName || '未命名';\r\n        const idCardNumber = this.contractForm.idCardNumber || '';\r\n        const timestamp = new Date().getTime();\r\n        const fileName = `${workerName}_${idCardNumber}_合同_${timestamp}.pdf`;\r\n        \r\n        // 获取PDF文件的Blob对象\r\n        const pdfBlob = pdf.output('blob');\r\n        \r\n        // 创建附件对象\r\n        const blobUrl = URL.createObjectURL(pdfBlob);\r\n        const attachment = {\r\n          name: fileName,\r\n          type: 'application/pdf',\r\n          size: pdfBlob.size,\r\n          data: blobUrl,\r\n          file: new File([pdfBlob], fileName, { type: 'application/pdf' })\r\n        };\r\n        \r\n        // 添加到合同附件数组\r\n        this.contractForm.attachments.push(attachment);\r\n        \r\n        // 将生成的PDF设置为合同文件\r\n        this.contractForm.contractFile = attachment;\r\n        \r\n        // 获取PDF的base64数据，用于上传API\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(pdfBlob);\r\n        reader.onloadend = () => {\r\n          const base64data = reader.result;\r\n          // 保存base64数据到附件对象，便于后续上传\r\n          attachment.base64 = base64data;\r\n        };\r\n        \r\n        this.$message.success('PDF生成成功并已添加到合同附件');\r\n        this.isGeneratingPDF = false;\r\n      }).catch(error => {\r\n        console.error('生成PDF失败:', error);\r\n        this.$message.error('生成PDF失败: ' + (error.message || '未知错误'));\r\n        this.isGeneratingPDF = false;\r\n      });\r\n    },\r\n\r\n    // 手动上传附件\r\n    handleAttachmentUpload(file) {\r\n      // Element UI 的 upload 组件传入的是一个包含文件信息的对象\r\n      // 需要从中获取实际的文件对象\r\n      const actualFile = file.raw || file;\r\n      \r\n      if (actualFile) {\r\n        const isLt10M = actualFile.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n          this.$message.error('文件大小不能超过10MB!');\r\n          return false;\r\n        }\r\n\r\n        // 显示上传中提示\r\n        this.$message.info('正在处理文件，请稍候...');\r\n\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(actualFile);\r\n        reader.onload = (e) => {\r\n          // 获取文件类型图标\r\n          const fileIcon = this.getFileTypeIcon(actualFile.type);\r\n          \r\n          let fileData = e.target.result;\r\n          \r\n          // 确保fileData是有效的data URL格式\r\n          if (!fileData || typeof fileData !== 'string' || !fileData.includes(';base64,')) {\r\n            console.warn('文件数据不是有效的data URL格式，将使用Blob URL代替');\r\n            fileData = URL.createObjectURL(actualFile);\r\n          }\r\n          \r\n          // 创建附件对象\r\n          const attachment = {\r\n            name: actualFile.name,\r\n            type: actualFile.type,\r\n            size: actualFile.size,\r\n            data: fileData,\r\n            file: actualFile,\r\n            icon: fileIcon\r\n          };\r\n          \r\n          // 添加到附件列表\r\n          this.contractForm.attachments.push(attachment);\r\n          \r\n          // 如果是PDF，自动设置为合同文件\r\n          if (actualFile.type === 'application/pdf' && !this.contractForm.contractFile) {\r\n            this.contractForm.contractFile = attachment;\r\n            this.$message.success('已自动设置为合同文件');\r\n          } else {\r\n            this.$message.success('附件上传成功');\r\n          }\r\n        };\r\n        reader.onerror = (error) => {\r\n          console.error('读取文件失败', error);\r\n          this.$message.error('读取文件失败');\r\n        };\r\n      }\r\n      return false; // 阻止默认的上传行为\r\n    },\r\n\r\n    // 根据文件类型获取图标\r\n    getFileTypeIcon(fileType) {\r\n      if (fileType.includes('image/')) {\r\n        return 'el-icon-picture';\r\n      } else if (fileType === 'application/pdf') {\r\n        return 'el-icon-document';\r\n      } else if (fileType.includes('word') || fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\r\n        return 'el-icon-document-checked';\r\n      } else if (fileType.includes('excel') || fileType === 'application/vnd.ms-excel' || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {\r\n        return 'el-icon-tickets';\r\n      } else {\r\n        return 'el-icon-document';\r\n      }\r\n    },\r\n\r\n    // 手动上传附件前的验证\r\n    beforeAttachmentUpload(file) {\r\n      // 验证文件大小（限制为10MB）\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!');\r\n        return false;\r\n      }\r\n      \r\n      // 验证文件类型\r\n      const allowedTypes = [\r\n        'application/pdf', \r\n        'application/msword', \r\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n        'application/vnd.ms-excel',\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        'image/jpeg',\r\n        'image/png',\r\n        'image/gif'\r\n      ];\r\n      \r\n      const isAllowedType = allowedTypes.includes(file.type) || file.type.startsWith('image/');\r\n      if (!isAllowedType) {\r\n        this.$message.error('只支持PDF、Word、Excel和图片格式!');\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contract-upload-container {\r\n  padding: 20px;\r\n}\r\n\r\n.contract-upload-container h3 {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  color: #303133;\r\n}\r\n\r\n.scanner-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.scanner-layout {\r\n  display: flex;\r\n  gap: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.scanner-left, .scanner-right {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.scanner-right {\r\n  border-left: 1px solid #ebeef5;\r\n  padding-left: 20px;\r\n  max-height: 700px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.scanner-preview {\r\n  width: 100%;\r\n  height: 400px;\r\n  margin-bottom: 20px;\r\n  border: 1px solid #dcdfe6;\r\n  background-color: #ebeef5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: hidden;\r\n}\r\n\r\n.scanner-preview img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n.scanner-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-button {\r\n  display: inline-block;\r\n  margin-left: 10px;\r\n}\r\n\r\n.scanner-right h4 {\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.photo-actions-top {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.photo-items {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.photo-item {\r\n  width: 180px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.photo-thumbnail {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n}\r\n\r\n.photo-actions {\r\n  padding: 8px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.contract-info-form {\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.id-card-input {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.id-card-input .el-input {\r\n  flex: 1;\r\n}\r\n\r\n.refresh-button {\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.contract-attachments {\r\n  margin-top: 20px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.contract-attachments h4 {\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #ebeef5;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.attachment-actions {\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100px;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  cursor: pointer;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger .el-icon-upload {\r\n  font-size: 24px;\r\n  color: #8c939d;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-attachment .el-upload__tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.no-attachments {\r\n  text-align: center;\r\n  color: #909399;\r\n  padding: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 20px;\r\n  color: #606266;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px; /* Adjust as needed */\r\n}\r\n\r\n/* 图片预览对话框样式已合并到PDF预览对话框样式中 */\r\n\r\n/* PDF预览对话框样式 */\r\n.pdf-preview-dialog {\r\n  width: 95% !important;\r\n  height: 95vh !important;\r\n  margin: 0 auto !important;\r\n  max-width: none !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box {\r\n  width: 95% !important;\r\n  max-width: none !important;\r\n  margin: 0 auto;\r\n  height: 95vh !important;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: fixed;\r\n  top: 2vh;\r\n  left: 2.5%;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__header {\r\n  padding: 10px 20px !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__content {\r\n  padding: 0 !important;\r\n  flex: 1;\r\n  overflow: hidden;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: calc(95vh - 55px) !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__message {\r\n  height: 100% !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__message p {\r\n  height: 100% !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.pdf-preview-dialog object {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n\r\n/* 为了确保遮罩层正确显示 */\r\n.v-modal {\r\n  opacity: 0.6 !important;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .scanner-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .scanner-right {\r\n    border-left: none;\r\n    border-top: 1px solid #ebeef5;\r\n    padding-left: 0;\r\n    padding-top: 20px;\r\n    margin-top: 20px;\r\n  }\r\n  \r\n  .scanner-preview {\r\n    width: 100%;\r\n    height: 300px;\r\n  }\r\n  \r\n  .scanner-controls {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .upload-button {\r\n    margin-left: 0;\r\n    margin-top: 10px;\r\n  }\r\n  \r\n  .id-card-input {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .refresh-button {\r\n    margin-left: 0;\r\n    margin-top: 10px;\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAmPA,OAAAA,KAAA;AACA,SAAAC,gBAAA,EAAAC,iBAAA,EAAAC,cAAA;AACA,OAAAC,KAAA;AACA,OAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,gBAAA;MACAC,YAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,WAAA;QAAA;QACAC,WAAA;QAAA;QACAC,sBAAA;QAAA;QACAC,wBAAA;QAAA;QACAC,QAAA;QAAA;QACAC,QAAA;QAAA;QACAC,kBAAA;QAAA;QACAC,iBAAA,MAAAC,IAAA;QAAA;QACAC,eAAA;QAAA;QACAC,gBAAA,MAAAF,IAAA;QACAG,UAAA;QAAA;QACAC,eAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;QAAA;QACAC,MAAA;QACAC,YAAA;QACAC,WAAA;MACA;MACAC,KAAA;QACAR,gBAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAvB,UAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAtB,YAAA,GACA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,wBAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,kBAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,iBAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,UAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,eAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,cAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,aAAA;QACAC,KAAA;QAAA;QACAC,OAAA;QAAA;QACAC,YAAA;QAAA;QACAC,SAAA;MACA;MACAC,mBAAA;MAAA;MACAC,iBAAA;QAAA;QACAL,KAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAG,gBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,SAAA;MAAA;MACAC,WAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACA;IACA;MAAA5C,QAAA;MAAAC,UAAA;MAAAC;IAAA,SAAA2C,MAAA,CAAAC,KAAA;IACA,IAAA9C,QAAA,IAAAC,UAAA,IAAAC,YAAA;MACA,KAAAH,YAAA,CAAAC,QAAA,GAAAA,QAAA;MACA,KAAAD,YAAA,CAAAE,UAAA,GAAAA,UAAA;MACA,KAAAF,YAAA,CAAAG,YAAA,GAAAA,YAAA;IACA;EACA;EACA6C,QAAA;IACA;IACA,KAAAC,oBAAA;EACA;EACAC,cAAA;IACA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAC,uBAAAC,KAAA;MACA;MACA,KAAAtD,YAAA,CAAAgB,eAAA;MAEA,IAAAsC,KAAA;QACA,KAAAtD,YAAA,CAAAiB,aAAA;QACA,KAAAjB,YAAA,CAAAkB,cAAA;;QAEA;QACA,KAAAqC,IAAA,MAAAjC,KAAA,oBACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA,KAAA8B,IAAA,MAAAjC,KAAA,qBACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;QACA;QACA,KAAA+B,OAAA,MAAAlC,KAAA;QACA,KAAAkC,OAAA,MAAAlC,KAAA;MACA;IACA;IAEA;IACA2B,qBAAA;MACA;QACA;QACA,KAAAvB,cAAA;;QAEA;QACA,KAAA5B,SAAA,OAAA2D,SAAA,MAAA5B,aAAA,CAAAC,KAAA;;QAEA;QACA,KAAAH,iBAAA,GAAA+B,UAAA;UACA,UAAA3D,gBAAA;YACA4D,OAAA,CAAAC,IAAA;YACA,SAAA/B,aAAA,CAAAG,YAAA;cACA,KAAA6B,sBAAA;YACA;cACA,KAAAC,QAAA,CAAAC,KAAA,mCAAAlC,aAAA,CAAAC,KAAA;YACA;UACA;QACA,QAAAD,aAAA,CAAAE,OAAA;QAEA,KAAAjC,SAAA,CAAAkE,MAAA,GAAAC,KAAA;UACAN,OAAA,CAAAO,GAAA;UACA,KAAAnE,gBAAA;UACA,KAAA+D,QAAA,CAAAK,OAAA;UACAC,YAAA,MAAAzC,iBAAA;QACA;QAEA,KAAA7B,SAAA,CAAAuE,OAAA,GAAAJ,KAAA;UACAN,OAAA,CAAAO,GAAA;UACA,KAAAnE,gBAAA;UACA;UACA,UAAA2B,cAAA,UAAAE,oBAAA,SAAAC,aAAA,CAAAG,YAAA;YACA,KAAA6B,sBAAA;UACA;QACA;QAEA,KAAA/D,SAAA,CAAAwE,OAAA,GAAAL,KAAA;UACAN,OAAA,CAAAI,KAAA,qBAAAE,KAAA;UACA,KAAAlE,gBAAA;UACA,SAAA8B,aAAA,CAAAG,YAAA;YACA,KAAA6B,sBAAA;UACA;YACA,KAAAC,QAAA,CAAAC,KAAA,mCAAAlC,aAAA,CAAAC,KAAA;UACA;QACA;QAEA,KAAAhC,SAAA,CAAAyE,SAAA,GAAAN,KAAA;UACA,KAAAO,oBAAA,CAAAP,KAAA;QACA;MACA,SAAAF,KAAA;QACAJ,OAAA,CAAAI,KAAA,sBAAAA,KAAA;QACA,SAAAlC,aAAA,CAAAG,YAAA;UACA,KAAA6B,sBAAA;QACA;UACA,KAAAC,QAAA,CAAAC,KAAA,cAAAA,KAAA,CAAAvC,OAAA;QACA;MACA;IACA;IAEA;IACAgD,qBAAAP,KAAA;MACA,MAAAQ,UAAA;MAEA,IAAAR,KAAA,CAAApE,IAAA,CAAA6E,OAAA;QACA;QACA,MAAAC,OAAA,GAAAV,KAAA,CAAApE,IAAA,CAAA+E,OAAA,6BAAAA,OAAA;QACA,KAAAd,QAAA,CAAAK,OAAA,aAAAQ,OAAA;;QAEA;QACA,SAAAE,cAAA,CAAAF,OAAA;UACA,KAAA3E,YAAA,CAAAG,YAAA,GAAAwE,OAAA;UACA,KAAAG,oBAAA,CAAAH,OAAA;QACA;MACA,WAAAV,KAAA,CAAApE,IAAA,CAAA6E,OAAA;QACA;QACA,MAAAK,MAAA,GAAAd,KAAA,CAAApE,IAAA,CAAA+E,OAAA,sBAAAA,OAAA;QACA,KAAAd,QAAA,CAAAK,OAAA,cAAAY,MAAA;MACA,WAAAd,KAAA,CAAApE,IAAA,CAAA6E,OAAA;QACA;QACA,MAAAM,UAAA,GAAAf,KAAA,CAAApE,IAAA,CAAA+E,OAAA,0BAAAA,OAAA;QACAjB,OAAA,CAAAO,GAAA,2BAAAc,UAAA,CAAAC,MAAA;;QAEA;QACA,KAAArC,gBAAA;QAEA,IAAAoC,UAAA,IAAAA,UAAA,CAAAC,MAAA;UAAA;UACA;UACA,KAAAtC,gBAAA,GAAA8B,UAAA,GAAAO,UAAA;;UAEA;UACA,KAAAzC,SAAA,CAAA2C,IAAA;YACAC,SAAA,OAAAxC,gBAAA;YACAyC,IAAA,OAAAhD,gBAAA;YACAiD,SAAA,MAAAzE,IAAA,GAAA0E,OAAA;UACA;;UAEA;UACA3B,OAAA,CAAAO,GAAA;UACA,KAAAJ,QAAA,CAAAyB,IAAA;UACA,KAAAC,mBAAA,MAAA7C,gBAAA;QACA;UACAgB,OAAA,CAAAI,KAAA;;UAEA;UACA,SAAA0B,KAAA,CAAAC,cAAA,SAAAD,KAAA,CAAAC,cAAA,CAAAC,GAAA,IACA,KAAAF,KAAA,CAAAC,cAAA,CAAAC,GAAA,CAAAC,UAAA;YACAjC,OAAA,CAAAO,GAAA;YACA,MAAAiB,SAAA,QAAAM,KAAA,CAAAC,cAAA,CAAAC,GAAA;;YAEA;YACA,KAAApD,SAAA,CAAA2C,IAAA;cACAC,SAAA,EAAAA,SAAA;cACAC,IAAA,OAAAhD,gBAAA;cACAiD,SAAA,MAAAzE,IAAA,GAAA0E,OAAA;YACA;YAEA,KAAAE,mBAAA,CAAAL,SAAA;UACA;YACAxB,OAAA,CAAAO,GAAA,0BAAA9B,gBAAA;;YAEA;YACA,KAAAG,SAAA,CAAA2C,IAAA;cACAC,SAAA;cACAC,IAAA,OAAAhD,gBAAA;cACAiD,SAAA,MAAAzE,IAAA,GAAA0E,OAAA;YACA;YAEA,KAAAE,mBAAA,MAAApD,gBAAA;UACA;QACA;MACA,WAAA6B,KAAA,CAAApE,IAAA,CAAA6E,OAAA;QACA;QACA,MAAAK,MAAA,GAAAd,KAAA,CAAApE,IAAA,CAAA+E,OAAA,yBAAAA,OAAA;QACAjB,OAAA,CAAAO,GAAA,YAAAa,MAAA;QACA;QACA,IAAAA,MAAA;UACApB,OAAA,CAAAO,GAAA;QACA;MACA,WAAAD,KAAA,CAAApE,IAAA,CAAA6E,OAAA;QACA;QACA,MAAAK,MAAA,GAAAd,KAAA,CAAApE,IAAA,CAAA+E,OAAA,wBAAAA,OAAA;QACAjB,OAAA,CAAAO,GAAA,YAAAa,MAAA;QACA;QACA,IAAAA,MAAA;UACApB,OAAA,CAAAO,GAAA;QACA;UACAP,OAAA,CAAAC,IAAA;QACA;MACA,WAAAK,KAAA,CAAApE,IAAA,CAAA6E,OAAA,8BAAAT,KAAA,CAAApE,IAAA,CAAA6E,OAAA;QACA;QACAf,OAAA,CAAAO,GAAA,gBAAAD,KAAA,CAAApE,IAAA;MACA,WAAAoE,KAAA,CAAApE,IAAA,CAAA+F,UAAA,YAAA3B,KAAA,CAAApE,IAAA,CAAAoF,MAAA,WAAAhB,KAAA,CAAApE,IAAA,CAAAgG,QAAA;QACA;QACA;QACA,SAAAJ,KAAA,CAAAC,cAAA;UACA;YACA;YACA,MAAAI,QAAA,GAAA7B,KAAA,CAAApE,IAAA,CAAAkG,SAAA;YACAC,MAAA,CAAAC,IAAA,CAAAH,QAAA;;YAEA;YACA,MAAAI,OAAA,GAAAzB,UAAA,GAAAR,KAAA,CAAApE,IAAA;YACA,KAAA4F,KAAA,CAAAC,cAAA,CAAAC,GAAA,GAAAO,OAAA;;YAEA;YACA,KAAAvD,gBAAA,GAAAuD,OAAA;;YAEA;YACA,KAAAlG,YAAA,CAAAoB,YAAA,GAAA8E,OAAA;YACAvC,OAAA,CAAAO,GAAA,iBAAAD,KAAA,CAAApE,IAAA,CAAAoF,MAAA;UACA,SAAAkB,CAAA;YACAxC,OAAA,CAAAI,KAAA,wBAAAoC,CAAA;UACA;QACA;MACA;QACA;QACAxC,OAAA,CAAAO,GAAA,aAAAD,KAAA,CAAApE,IAAA;MACA;IACA;IAEA;IACAuG,aAAA;MACA,UAAArG,gBAAA;QACA,KAAAkD,oBAAA;QACA;MACA;MAEA;QACA;QACA,KAAAoD,kBAAA;QACA;QACA,KAAAA,kBAAA;QACA;QACA,KAAAA,kBAAA;QACA,KAAAvC,QAAA,CAAAK,OAAA;MACA,SAAAJ,KAAA;QACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAuC,YAAA;MACA,UAAAvG,gBAAA;QACA;MACA;MAEA;QACA,KAAAsG,kBAAA;QACA,KAAAvC,QAAA,CAAAK,OAAA;MACA,SAAAJ,KAAA;QACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAwC,aAAA;MACA,UAAAxG,gBAAA,UAAA2B,cAAA;QACA,KAAAoC,QAAA,CAAA0C,OAAA;QACA;MACA;MAEA;QACA,SAAA9E,cAAA;UACA;UACA,KAAA+E,0BAAA;UACA;UACA/C,UAAA;YACA,KAAAgD,6BAAA;UACA;UACA;QACA;;QAEA;QACA;QACA,KAAAL,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA,KAAAA,kBAAA;;QAEA;QACA3C,UAAA;UACA;UACA,KAAA2C,kBAAA;;UAEA;UACA,MAAAhB,SAAA,OAAAzE,IAAA,GAAA0E,OAAA;UACA,MAAAqB,QAAA,eAAAtB,SAAA;UACA,KAAAjD,gBAAA,eAAAuE,QAAA;UACAhD,OAAA,CAAAO,GAAA,iBAAA9B,gBAAA;;UAEA;UACA,KAAA0B,QAAA,CAAAyB,IAAA;;UAEA;UACA7B,UAAA;YACA;YACA,KAAA2C,kBAAA,uBAAAM,QAAA;;YAEA;YACA,KAAAhE,gBAAA;YAEA,KAAAmB,QAAA,CAAAyB,IAAA;;YAEA;YACA,KAAA3C,gBAAA;;YAEA;YACAc,UAAA;cACA;cACA,KAAA2C,kBAAA,sBAAAjE,gBAAA;;cAEA;cACAsB,UAAA;gBACA,SAAAd,gBAAA;kBACAe,OAAA,CAAAO,GAAA;kBACA,KAAAtB,gBAAA;;kBAEA;kBACA,SAAA6C,KAAA,CAAAC,cAAA,SAAAD,KAAA,CAAAC,cAAA,CAAAC,GAAA,IACA,KAAAF,KAAA,CAAAC,cAAA,CAAAC,GAAA,CAAAC,UAAA;oBACAjC,OAAA,CAAAO,GAAA;oBACA,MAAAiB,SAAA,QAAAM,KAAA,CAAAC,cAAA,CAAAC,GAAA;;oBAEA;oBACA,KAAApD,SAAA,CAAA2C,IAAA;sBACAC,SAAA,EAAAA,SAAA;sBACAC,IAAA,OAAAhD,gBAAA;sBACAiD,SAAA,MAAAzE,IAAA,GAAA0E,OAAA;oBACA;;oBAEA;oBACA,KAAAxB,QAAA,CAAAyB,IAAA;oBACA,KAAAC,mBAAA,CAAAL,SAAA;kBACA;oBACA;oBACAxB,OAAA,CAAAO,GAAA,iBAAA9B,gBAAA;;oBAEA;oBACA,KAAAG,SAAA,CAAA2C,IAAA;sBACAC,SAAA;sBACAC,IAAA,OAAAhD,gBAAA;sBACAiD,SAAA,MAAAzE,IAAA,GAAA0E,OAAA;oBACA;;oBAEA;oBACA,KAAAxB,QAAA,CAAAyB,IAAA;oBACA,KAAAC,mBAAA,MAAApD,gBAAA;kBACA;gBACA;cACA;YAEA;UACA;QACA;MACA,SAAA2B,KAAA;QACAJ,OAAA,CAAAI,KAAA,WAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA0C,2BAAA;MACA,KAAA3C,QAAA,CAAAyB,IAAA;;MAEA;MACA,SAAAE,KAAA,CAAAC,cAAA;QACA;QACA,MAAAkB,MAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,MAAA,CAAAG,KAAA;QACAH,MAAA,CAAAI,MAAA;QACA,MAAAC,GAAA,GAAAL,MAAA,CAAAM,UAAA;;QAEA;QACAD,GAAA,CAAAE,SAAA;QACAF,GAAA,CAAAG,QAAA,OAAAR,MAAA,CAAAG,KAAA,EAAAH,MAAA,CAAAI,MAAA;;QAEA;QACAC,GAAA,CAAAE,SAAA;QACAF,GAAA,CAAAI,IAAA;QACAJ,GAAA,CAAAK,SAAA;QACAL,GAAA,CAAAM,QAAA,gBAAAX,MAAA,CAAAG,KAAA,MAAAH,MAAA,CAAAI,MAAA;;QAEA;QACA,MAAAQ,aAAA,GAAAZ,MAAA,CAAAa,SAAA;QACA,KAAAhC,KAAA,CAAAC,cAAA,CAAAC,GAAA,GAAA6B,aAAA;QACA,KAAAxH,YAAA,CAAAoB,YAAA,GAAAoG,aAAA;MACA;IACA;IAEA;IACAnB,mBAAAqB,OAAA;MACA,SAAA5H,SAAA,SAAAA,SAAA,CAAA6H,UAAA,KAAAlE,SAAA,CAAAmE,IAAA;QACA,KAAA9H,SAAA,CAAA+H,IAAA,CAAAH,OAAA;MACA;QACA,UAAAI,KAAA;MACA;IACA;IAEA;IACA3E,eAAA;MACA,SAAArD,SAAA;QACA;QACA,SAAAC,gBAAA;UACA;YACA,KAAAD,SAAA,CAAA+H,IAAA;UACA,SAAA1B,CAAA;YACAxC,OAAA,CAAAI,KAAA,YAAAoC,CAAA;UACA;QACA;;QAEA;QACA,KAAArG,SAAA,CAAAiI,KAAA;QACA,KAAAjI,SAAA;QACA,KAAAC,gBAAA;MACA;IACA;IAEA;IACA8D,uBAAA;MACA,KAAAnC,cAAA;MACA,KAAA3B,gBAAA;MAEA,SAAAD,SAAA;QACA,KAAA8B,oBAAA;QACA,KAAA9B,SAAA,CAAAiI,KAAA;QACA,KAAAjI,SAAA;MACA;MAEA,KAAAgE,QAAA,CAAA0C,OAAA;;MAEA;MACA,SAAA7E,iBAAA;QACAyC,YAAA,MAAAzC,iBAAA;MACA;IACA;IAEA;IACAqG,kBAAAC,IAAA;MACA,IAAAA,IAAA;QACA;QACA,MAAAC,OAAA,GAAAD,IAAA,CAAAE,IAAA,CAAAzD,OAAA;QACA,KAAAwD,OAAA;UACA,KAAApE,QAAA,CAAAC,KAAA;UACA;QACA;;QAEA;QACA,MAAAqE,OAAA,GAAAH,IAAA,CAAAI,IAAA;QACA,KAAAD,OAAA;UACA,KAAAtE,QAAA,CAAAC,KAAA;UACA;QACA;QAEA,KAAAD,QAAA,CAAAyB,IAAA;;QAEA;QACA,MAAA+C,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,aAAA,CAAAP,IAAA;QACAK,MAAA,CAAAG,MAAA,GAAAtC,CAAA;UACA;UACA,KAAAuC,aAAA,CAAAvC,CAAA,CAAAwC,MAAA,CAAA5D,MAAA,EAAA6D,iBAAA;YACA;YACA,SAAAnD,KAAA,CAAAC,cAAA;cACA,KAAAD,KAAA,CAAAC,cAAA,CAAAC,GAAA,GAAAiD,iBAAA;YACA;;YAEA;YACA,KAAArG,SAAA,CAAA2C,IAAA;cACAC,SAAA,EAAAyD,iBAAA;cACAxD,IAAA;cACAC,SAAA,MAAAzE,IAAA,GAAA0E,OAAA;cACAuD,QAAA;YACA;YAEA,KAAA/E,QAAA,CAAAyB,IAAA;;YAEA;YACA,KAAAC,mBAAA,CAAAoD,iBAAA;UACA;QACA;QAEAN,MAAA,CAAAhE,OAAA,GAAAP,KAAA;UACAJ,OAAA,CAAAI,KAAA,aAAAA,KAAA;UACA,KAAAD,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACA2E,cAAAI,OAAA,EAAAC,QAAA,EAAAC,QAAA,SAAAC,SAAA,SAAAC,OAAA;MACA,MAAAC,GAAA,OAAAC,KAAA;MACAD,GAAA,CAAAxD,GAAA,GAAAmD,OAAA;MAEAK,GAAA,CAAAV,MAAA;QACA;QACA,MAAA7B,MAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAC,KAAA,GAAAoC,GAAA,CAAApC,KAAA;QACA,IAAAC,MAAA,GAAAmC,GAAA,CAAAnC,MAAA;;QAEA;QACA,IAAAD,KAAA,GAAAC,MAAA;UACA,IAAAD,KAAA,GAAAiC,QAAA;YACAhC,MAAA,IAAAgC,QAAA,GAAAjC,KAAA;YACAA,KAAA,GAAAiC,QAAA;UACA;QACA;UACA,IAAAhC,MAAA,GAAAiC,SAAA;YACAlC,KAAA,IAAAkC,SAAA,GAAAjC,MAAA;YACAA,MAAA,GAAAiC,SAAA;UACA;QACA;;QAEA;QACArC,MAAA,CAAAG,KAAA,GAAAA,KAAA;QACAH,MAAA,CAAAI,MAAA,GAAAA,MAAA;;QAEA;QACA,MAAAC,GAAA,GAAAL,MAAA,CAAAM,UAAA;QACAD,GAAA,CAAAoC,SAAA,CAAAF,GAAA,QAAApC,KAAA,EAAAC,MAAA;;QAEA;QACA,MAAA4B,iBAAA,GAAAhC,MAAA,CAAAa,SAAA,eAAAyB,OAAA;;QAEA;QACA,MAAAI,YAAA,GAAAR,OAAA,CAAA7D,MAAA;QACA,MAAAsE,cAAA,GAAAX,iBAAA,CAAA3D,MAAA;QACA,MAAAuE,gBAAA,KAAAF,YAAA,GAAAC,cAAA,IAAAD,YAAA,QAAAG,OAAA;QAEA9F,OAAA,CAAAO,GAAA,iBAAAoF,YAAA,gBAAAG,OAAA,iBAAAF,cAAA,gBAAAE,OAAA,cAAAD,gBAAA;QAEAT,QAAA,CAAAH,iBAAA;MACA;MAEAO,GAAA,CAAA7E,OAAA;QACAX,OAAA,CAAAI,KAAA;QACAgF,QAAA,CAAAD,OAAA;MACA;IACA;IAEA;IACAY,eAAA;MACA,KAAAjE,KAAA,CAAAzF,YAAA,CAAA2J,QAAA,OAAAC,KAAA;QACA,IAAAA,KAAA;UACA,SAAA5J,YAAA,CAAAqB,WAAA,CAAA4D,MAAA,eAAA1C,SAAA,CAAA0C,MAAA;YACA,KAAAnB,QAAA,CAAA0C,OAAA;YACA;UACA;UAEA,UAAAhE,WAAA,UAAAxC,YAAA,CAAAG,YAAA;YACA,KAAA2D,QAAA,CAAA0C,OAAA;YACA;UACA;UAEA,KAAAnE,kBAAA;UACA,KAAAyB,QAAA,CAAAyB,IAAA;;UAEA;UACA,SAAAhD,SAAA,CAAA0C,MAAA,aAAAjF,YAAA,CAAAqB,WAAA,CAAA4D,MAAA;YACA,KAAA4E,kBAAA;YACA,KAAA/F,QAAA,CAAAyB,IAAA;YACA,KAAAlD,kBAAA;YACA;UACA;;UAEA;UACAsB,OAAA,CAAAO,GAAA,sBAAA4F,IAAA,CAAAC,SAAA,MAAA/J,YAAA;UACA2D,OAAA,CAAAO,GAAA,0CAAAlE,YAAA,CAAAM,sBAAA;;UAEA;UACA,MAAA0J,QAAA;YACAC,QAAA,OAAAjK,YAAA,CAAAM,sBAAA;YACA4J,QAAA,OAAAlK,YAAA,CAAAO,wBAAA;YACA4J,UAAA;YAAA;YACAC,QAAA,OAAApK,YAAA,CAAAG,YAAA;YACAF,QAAA,OAAAD,YAAA,CAAAC,QAAA;YAAA;YACAC,UAAA,OAAAF,YAAA,CAAAE,UAAA;YAAA;YACAmK,MAAA,OAAArK,YAAA,CAAAQ,QAAA;YAAA;YACAC,QAAA,OAAAT,YAAA,CAAAS,QAAA;YAAA;YACAC,kBAAA,OAAA4J,kBAAA,MAAAtK,YAAA,CAAAU,kBAAA;YACA6J,SAAA,OAAAC,UAAA,MAAAxK,YAAA,CAAAW,iBAAA;YACA8J,OAAA,OAAAD,UAAA,MAAAxK,YAAA,CAAAa,eAAA;YACA6J,QAAA,OAAAF,UAAA,MAAAxK,YAAA,CAAAc,gBAAA,QAAAF,IAAA;YACA+J,IAAA,OAAAL,kBAAA,MAAAtK,YAAA,CAAAe,UAAA;YAAA;YACA6J,SAAA,EAAAC,MAAA,MAAA7K,YAAA,CAAAgB,eAAA;UACA;;UAEA;UACA2C,OAAA,CAAAO,GAAA,mBAAA4F,IAAA,CAAAC,SAAA,CAAAC,QAAA;UACArG,OAAA,CAAAO,GAAA,eAAA8F,QAAA,CAAAC,QAAA;;UAEA;UACA,SAAAjK,YAAA,CAAAe,UAAA;YACAiJ,QAAA,CAAAc,OAAA,QAAA9K,YAAA,CAAAiB,aAAA;YACA+I,QAAA,CAAAe,QAAA,QAAA/K,YAAA,CAAAkB,cAAA;UACA;;UAEA;UACA,MAAAG,WAAA;UAEA;YACA;YACA,SAAArB,YAAA,CAAAqB,WAAA,SAAArB,YAAA,CAAAqB,WAAA,CAAA4D,MAAA;cACA,WAAA+F,UAAA,SAAAhL,YAAA,CAAAqB,WAAA;gBACA;gBACA,IAAA2D,UAAA;gBACA,IAAAgG,UAAA,CAAAnL,IAAA;kBACA,WAAAmL,UAAA,CAAAnL,IAAA,iBAAAmL,UAAA,CAAAnL,IAAA,CAAA+F,UAAA;oBACA;oBACAZ,UAAA,GAAAgG,UAAA,CAAAnL,IAAA,CAAAoL,KAAA;kBACA,WAAAD,UAAA,CAAA/C,IAAA;oBACA;oBACA,IAAA+C,UAAA,CAAAE,MAAA;sBACAlG,UAAA,GAAAgG,UAAA,CAAAE,MAAA,CAAAD,KAAA;oBACA;sBACA;sBACAjG,UAAA,cAAAmG,uBAAA,CAAAH,UAAA,CAAA/C,IAAA;oBACA;kBACA;gBACA;gBAEA5G,WAAA,CAAA6D,IAAA;kBACAtF,IAAA,EAAAoL,UAAA,CAAApL,IAAA;kBACAC,IAAA,EAAAmF;gBACA;cACA;YACA;;YAEA;YACA,MAAAoG,WAAA;cACA/K,WAAA,OAAAL,YAAA,CAAAK,WAAA;cACAgL,YAAA,GAAArB,QAAA;cACA3I,WAAA,EAAAA;YACA;;YAEA;YACAsC,OAAA,CAAAO,GAAA,eAAA4F,IAAA,CAAAC,SAAA;cACA1J,WAAA,EAAA+K,WAAA,CAAA/K,WAAA;cACAgL,YAAA,EAAAD,WAAA,CAAAC,YAAA;cACAC,gBAAA,EAAAF,WAAA,CAAA/J,WAAA,CAAA4D;YACA;;YAEA;YACAxF,cAAA,CAAA2L,WAAA,EACAG,IAAA,CAAAC,QAAA;cACA,IAAAA,QAAA,CAAAC,IAAA;gBACA,KAAA3H,QAAA,CAAAK,OAAA;gBACA;gBACA,KAAAuH,MAAA;cACA;gBACA,KAAA5H,QAAA,CAAAC,KAAA,CAAAyH,QAAA,CAAAhK,OAAA;cACA;YACA,GACAmK,KAAA,CAAA5H,KAAA;cACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;cACA,KAAAD,QAAA,CAAAC,KAAA,eAAAA,KAAA,CAAAvC,OAAA;YACA,GACAoK,OAAA;cACA,KAAAvJ,kBAAA;YACA;UACA,SAAA0B,KAAA;YACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;YACA,KAAAD,QAAA,CAAAC,KAAA,eAAAA,KAAA,CAAAvC,OAAA;YACA,KAAAa,kBAAA;UACA;QACA;UACA,KAAAyB,QAAA,CAAA0C,OAAA;QACA;MACA;IACA;IAEA;IACA2E,wBAAAlD,IAAA;MACA,WAAA4D,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA,MAAAzD,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAG,MAAA,GAAAtC,CAAA;UACA,MAAAnB,UAAA,GAAAmB,CAAA,CAAAwC,MAAA,CAAA5D,MAAA,CAAAkG,KAAA;UACAa,OAAA,CAAA9G,UAAA;QACA;QACAsD,MAAA,CAAAhE,OAAA,GAAA6B,CAAA,IAAA4F,MAAA,CAAA5F,CAAA;QACAmC,MAAA,CAAAE,aAAA,CAAAP,IAAA;MACA;IACA;IAEA;IACAqC,mBAAAnC,IAAA;MACA,MAAA6D,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA7D,IAAA;IACA;IAEA;IACA8D,kBAAA9D,IAAA;MACA,MAAA6D,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA7D,IAAA;IACA;IAEA;IACAqC,WAAA0B,IAAA;MACA,KAAAA,IAAA;MACA,MAAAC,CAAA,OAAAvL,IAAA,CAAAsL,IAAA;MACA,MAAAE,IAAA,GAAAD,CAAA,CAAAE,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAJ,CAAA,CAAAK,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAJ,CAAA,CAAAQ,OAAA,IAAAF,QAAA;MACA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA;IACA;IAEA;IACAhB,OAAA;MACA,KAAAkB,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,kBAAA9B,UAAA;MACA,IAAAA,UAAA,IAAAA,UAAA,CAAAnL,IAAA;QACA;QACA,IAAAmL,UAAA,CAAA7C,IAAA,IAAA6C,UAAA,CAAA7C,IAAA,CAAAtC,QAAA;UACA;UACA,KAAAkH,OAAA;YACAC,KAAA,EAAAhC,UAAA,CAAApL,IAAA;YACA4B,OAAA;AACA,0BAAAwJ,UAAA,CAAAnL,IAAA;AACA;YACAoN,wBAAA;YACAC,WAAA;YAAA;YACAC,gBAAA;YACAC,iBAAA;UACA;QACA,WAAApC,UAAA,CAAA7C,IAAA;UACA;YACA;YACA,MAAAkF,OAAA,QAAAC,aAAA,CAAAtC,UAAA,CAAAnL,IAAA;YACA,MAAA0N,OAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,OAAA;;YAEA;YACA,KAAAN,OAAA;cACAC,KAAA,EAAAhC,UAAA,CAAApL,IAAA;cACA4B,OAAA;AACA;AACA,0BAAA+L,OAAA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAAA,OAAA,eAAAvC,UAAA,CAAApL,IAAA;AACA;AACA;AACA;cACAqN,wBAAA;cACAC,WAAA;cACAC,gBAAA;cACAC,iBAAA;cACAM,WAAA,EAAAA,CAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACA;gBACAL,GAAA,CAAAM,eAAA,CAAAP,OAAA;gBACAM,IAAA;cACA;YACA;UACA,SAAA9J,KAAA;YACAJ,OAAA,CAAAI,KAAA,aAAAA,KAAA;YACA,KAAAD,QAAA,CAAAC,KAAA,iBAAAA,KAAA,CAAAvC,OAAA;;YAEA;YACA,IAAAwJ,UAAA,CAAA/C,IAAA;cACA,MAAA8F,GAAA,GAAAP,GAAA,CAAAC,eAAA,CAAAzC,UAAA,CAAA/C,IAAA;cACA,MAAA+F,IAAA,GAAAnH,QAAA,CAAAC,aAAA;cACAkH,IAAA,CAAAC,IAAA,GAAAF,GAAA;cACAC,IAAA,CAAAE,QAAA,GAAAlD,UAAA,CAAApL,IAAA;cACAoO,IAAA,CAAAG,KAAA;cACAzK,UAAA,OAAA8J,GAAA,CAAAM,eAAA,CAAAC,GAAA;YACA;UACA;QACA;UACA;UACA;YACA,MAAAC,IAAA,GAAAnH,QAAA,CAAAC,aAAA;;YAEA;YACA,IAAAkE,UAAA,CAAAnL,IAAA,YAAAuO,IAAA,IAAApD,UAAA,CAAAnL,IAAA,CAAA+F,UAAA;cACAoI,IAAA,CAAAC,IAAA,GAAAjD,UAAA,CAAAnL,IAAA;YACA,WAAAmL,UAAA,CAAA/C,IAAA;cACA;cACA+F,IAAA,CAAAC,IAAA,GAAAT,GAAA,CAAAC,eAAA,CAAAzC,UAAA,CAAA/C,IAAA;YACA;cACA;cACA,MAAAoG,IAAA,QAAAf,aAAA,CAAAtC,UAAA,CAAAnL,IAAA;cACAmO,IAAA,CAAAC,IAAA,GAAAT,GAAA,CAAAC,eAAA,CAAAY,IAAA;YACA;YAEAL,IAAA,CAAAE,QAAA,GAAAlD,UAAA,CAAApL,IAAA;YACAoO,IAAA,CAAAG,KAAA;;YAEA;YACA,IAAAH,IAAA,CAAAC,IAAA,CAAArI,UAAA;cACAlC,UAAA,OAAA8J,GAAA,CAAAM,eAAA,CAAAE,IAAA,CAAAC,IAAA;YACA;UACA,SAAAlK,KAAA;YACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;YACA,KAAAD,QAAA,CAAAC,KAAA,cAAAA,KAAA,CAAAvC,OAAA;UACA;QACA;MACA;QACA,KAAAsC,QAAA,CAAA0C,OAAA;MACA;IACA;IAEA;IACA8G,cAAAgB,OAAA;MACA;QACA;QACA,KAAAA,OAAA,WAAAA,OAAA,kBAAAA,OAAA,CAAAzI,QAAA;UACAlC,OAAA,CAAAI,KAAA,mBAAAuK,OAAA;UACA,UAAAxG,KAAA;QACA;;QAEA;QACA,MAAAyG,KAAA,GAAAD,OAAA,CAAArD,KAAA;QACA,MAAAuD,WAAA,GAAAD,KAAA,IAAAtD,KAAA;QACA,MAAAwD,GAAA,GAAAzI,MAAA,CAAAC,IAAA,CAAAsI,KAAA;QACA,MAAAG,SAAA,GAAAD,GAAA,CAAAxJ,MAAA;QACA,MAAA0J,UAAA,OAAAC,UAAA,CAAAF,SAAA;;QAEA;QACA,SAAAG,CAAA,MAAAA,CAAA,GAAAH,SAAA,IAAAG,CAAA;UACAF,UAAA,CAAAE,CAAA,IAAAJ,GAAA,CAAAK,UAAA,CAAAD,CAAA;QACA;QAEA,WAAAT,IAAA,EAAAO,UAAA;UAAAxG,IAAA,EAAAqG;QAAA;MACA,SAAAzK,KAAA;QACAJ,OAAA,CAAAI,KAAA,uBAAAA,KAAA;QACA,UAAA+D,KAAA,yBAAA/D,KAAA,CAAAvC,OAAA;MACA;IACA;IAEA;IACAuN,iBAAAC,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAhH,IAAA;MACA,GAAAoD,IAAA;QACA;QACA,SAAAvL,YAAA,CAAAqB,WAAA,CAAA2N,KAAA,EAAAnP,IAAA;UACA2N,GAAA,CAAAM,eAAA,MAAA9N,YAAA,CAAAqB,WAAA,CAAA2N,KAAA,EAAAnP,IAAA;QACA;QACA;QACA,KAAAG,YAAA,CAAAqB,WAAA,CAAA+N,MAAA,CAAAJ,KAAA;QACA,KAAAlL,QAAA,CAAAK,OAAA;MACA,GAAAwH,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACA0D,WAAA;MACA,UAAAtP,gBAAA,UAAA2B,cAAA;QACA,KAAAoC,QAAA,CAAA0C,OAAA;QACA;MACA;MAEA;QACA,SAAA9E,cAAA;UACA;UACA,KAAAgF,6BAAA;UACA;QACA;;QAEA;QACA,KAAAL,kBAAA;;QAEA;QACA,MAAAhB,SAAA,OAAAzE,IAAA,GAAA0E,OAAA;QACA,MAAAqB,QAAA,aAAAtB,SAAA;QACA,KAAAjD,gBAAA,eAAAuE,QAAA;QACAhD,OAAA,CAAAO,GAAA,iBAAA9B,gBAAA;;QAEA;QACA,KAAAiE,kBAAA,uBAAAM,QAAA;;QAEA;QACA,KAAAN,kBAAA,8BAAAjE,gBAAA;;QAEA;QACAsB,UAAA;UACA,KAAA8B,mBAAA,MAAApD,gBAAA;QACA;;QAEA,KAAA0B,QAAA,CAAAyB,IAAA;MACA,SAAAxB,KAAA;QACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAyB,oBAAA8J,SAAA;MACA,SAAAhN,eAAA;QACA;MACA;MAEA,KAAAA,eAAA;MACA,KAAAwB,QAAA,CAAAyB,IAAA;;MAEA;MACA,MAAAgK,QAAA,OAAAC,QAAA;;MAEA;MACA,IAAAF,SAAA,CAAA1J,UAAA;QACA;QACA,MAAAZ,UAAA,GAAAsK,SAAA,CAAArE,KAAA;;QAEA;QACA,MAAAwE,mBAAA,QAAA9M,gBAAA,KAAA2M,SAAA,UAAA1M,gBAAA;QAEA,IAAA6M,mBAAA;UACA;UACA9L,OAAA,CAAAO,GAAA;UAEA,MAAAwL,cAAA,GAAAzJ,IAAA,CAAAjB,UAAA;UACA,MAAA2K,UAAA;UAEA,SAAAd,CAAA,MAAAA,CAAA,GAAAa,cAAA,CAAAzK,MAAA,EAAA4J,CAAA;YACAc,UAAA,CAAAzK,IAAA,CAAAwK,cAAA,CAAAZ,UAAA,CAAAD,CAAA;UACA;UAEA,MAAAe,SAAA,OAAAhB,UAAA,CAAAe,UAAA;UACA,MAAAtB,IAAA,OAAAD,IAAA,EAAAwB,SAAA;YAAAzH,IAAA;UAAA;;UAEA;UACA,MAAA0H,QAAA,mBAAAjP,IAAA,GAAA0E,OAAA;UACA,MAAA2C,IAAA,OAAA6H,IAAA,EAAAzB,IAAA,GAAAwB,QAAA;YAAA1H,IAAA;UAAA;;UAEA;UACAoH,QAAA,CAAAQ,MAAA,UAAA9H,IAAA;UACAtE,OAAA,CAAAO,GAAA;;UAEA;UACAqL,QAAA,CAAAQ,MAAA,iBAAAT,SAAA;;UAEA;UACA,KAAAU,UAAA,CAAAT,QAAA;QACA;UACA;UACA,KAAAU,eAAA,CAAAX,SAAA,EAAA/D,IAAA,CAAA2E,kBAAA;YACA;YACA,MAAAC,eAAA,GAAAD,kBAAA,CAAAjF,KAAA;YACA,MAAAyE,cAAA,GAAAzJ,IAAA,CAAAkK,eAAA;YACA,MAAAR,UAAA;YAEA,SAAAd,CAAA,MAAAA,CAAA,GAAAa,cAAA,CAAAzK,MAAA,EAAA4J,CAAA;cACAc,UAAA,CAAAzK,IAAA,CAAAwK,cAAA,CAAAZ,UAAA,CAAAD,CAAA;YACA;YAEA,MAAAe,SAAA,OAAAhB,UAAA,CAAAe,UAAA;YACA,MAAAtB,IAAA,OAAAD,IAAA,EAAAwB,SAAA;cAAAzH,IAAA;YAAA;;YAEA;YACA,MAAA0H,QAAA,mBAAAjP,IAAA,GAAA0E,OAAA;YACA,MAAA2C,IAAA,OAAA6H,IAAA,EAAAzB,IAAA,GAAAwB,QAAA;cAAA1H,IAAA;YAAA;;YAEA;YACAoH,QAAA,CAAAQ,MAAA,UAAA9H,IAAA;YACAtE,OAAA,CAAAO,GAAA;;YAEA;YACAqL,QAAA,CAAAQ,MAAA,iBAAAG,kBAAA;;YAEA;YACA,KAAAF,UAAA,CAAAT,QAAA;UACA,GAAA5D,KAAA,CAAA5H,KAAA;YACAJ,OAAA,CAAAI,KAAA,oBAAAA,KAAA;;YAEA;YACA,MAAA2L,cAAA,GAAAzJ,IAAA,CAAAjB,UAAA;YACA,MAAA2K,UAAA;YAEA,SAAAd,CAAA,MAAAA,CAAA,GAAAa,cAAA,CAAAzK,MAAA,EAAA4J,CAAA;cACAc,UAAA,CAAAzK,IAAA,CAAAwK,cAAA,CAAAZ,UAAA,CAAAD,CAAA;YACA;YAEA,MAAAe,SAAA,OAAAhB,UAAA,CAAAe,UAAA;YACA,MAAAtB,IAAA,OAAAD,IAAA,EAAAwB,SAAA;cAAAzH,IAAA;YAAA;;YAEA;YACA,MAAA0H,QAAA,mBAAAjP,IAAA,GAAA0E,OAAA;YACA,MAAA2C,IAAA,OAAA6H,IAAA,EAAAzB,IAAA,GAAAwB,QAAA;cAAA1H,IAAA;YAAA;;YAEA;YACAoH,QAAA,CAAAQ,MAAA,UAAA9H,IAAA;YACAtE,OAAA,CAAAO,GAAA;;YAEA;YACAqL,QAAA,CAAAQ,MAAA,iBAAAT,SAAA;;YAEA;YACA,KAAAU,UAAA,CAAAT,QAAA;UACA;QACA;MACA;QACA;QACAA,QAAA,CAAAQ,MAAA,eAAAT,SAAA;QACA3L,OAAA,CAAAO,GAAA,mBAAAoL,SAAA;;QAEA;QACA,KAAAU,UAAA,CAAAT,QAAA;MACA;IACA;IAEA;IACAS,WAAAT,QAAA;MACAjQ,KAAA,CAAA8Q,IAAA,MAAAvO,aAAA,CAAAI,SAAA,EAAAsN,QAAA,EACAhE,IAAA,CAAAC,QAAA;QACA,KAAA6E,eAAA,CAAA7E,QAAA,CAAA3L,IAAA;MACA,GACA8L,KAAA,CAAA5H,KAAA;QAAA,IAAAuM,eAAA;QACA3M,OAAA,CAAAI,KAAA,YAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,kBAAAuM,eAAA,GAAAvM,KAAA,CAAAyH,QAAA,cAAA8E,eAAA,gBAAAA,eAAA,GAAAA,eAAA,CAAAzQ,IAAA,cAAAyQ,eAAA,uBAAAA,eAAA,CAAA9O,OAAA,KAAAuC,KAAA,CAAAvC,OAAA;MACA,GACAoK,OAAA;QACA,KAAAtJ,eAAA;MACA;IACA;IAEA;IACA2N,gBAAAM,WAAA;MACA,WAAA1E,OAAA,EAAAC,OAAA,EAAAC,MAAA;QACA;UACA;UACA,MAAA5C,GAAA,OAAAC,KAAA;UACAD,GAAA,CAAAV,MAAA;YACA;YACA,MAAA7B,MAAA,GAAAC,QAAA,CAAAC,aAAA;YACAF,MAAA,CAAAG,KAAA,GAAAoC,GAAA,CAAApC,KAAA;YACAH,MAAA,CAAAI,MAAA,GAAAmC,GAAA,CAAAnC,MAAA;YACA,MAAAC,GAAA,GAAAL,MAAA,CAAAM,UAAA;;YAEA;YACAD,GAAA,CAAAoC,SAAA,CAAAF,GAAA;;YAEA;YACA,MAAAhE,SAAA,GAAA8B,GAAA,CAAAuJ,YAAA,OAAA5J,MAAA,CAAAG,KAAA,EAAAH,MAAA,CAAAI,MAAA;YACA,MAAAnH,IAAA,GAAAsF,SAAA,CAAAtF,IAAA;;YAEA;YACA,SAAAgP,CAAA,MAAAA,CAAA,GAAAhP,IAAA,CAAAoF,MAAA,EAAA4J,CAAA;cACA,MAAA4B,CAAA,GAAA5Q,IAAA,CAAAgP,CAAA;cACA,MAAA6B,CAAA,GAAA7Q,IAAA,CAAAgP,CAAA;cACA,MAAA8B,CAAA,GAAA9Q,IAAA,CAAAgP,CAAA;;cAEA;cACA;cACA,IAAA4B,CAAA,UAAAA,CAAA,UAAAC,CAAA,UAAAC,CAAA,UAAAA,CAAA;gBACA;gBACA9Q,IAAA,CAAAgP,CAAA;gBACAhP,IAAA,CAAAgP,CAAA;gBACAhP,IAAA,CAAAgP,CAAA;cACA;YACA;;YAEA;YACA5H,GAAA,CAAA2J,YAAA,CAAAzL,SAAA;;YAEA;YACA;YACA;YACA,MAAA0L,aAAA,GAAA5J,GAAA,CAAAuJ,YAAA,OAAA5J,MAAA,CAAAG,KAAA,EAAAH,MAAA,CAAAI,MAAA;YACA,MAAA8J,QAAA,GAAAD,aAAA,CAAAhR,IAAA;;YAEA;YACA,SAAAgP,CAAA,MAAAA,CAAA,GAAAiC,QAAA,CAAA7L,MAAA,EAAA4J,CAAA;cACA,MAAAkC,GAAA,IAAAD,QAAA,CAAAjC,CAAA,IAAAiC,QAAA,CAAAjC,CAAA,QAAAiC,QAAA,CAAAjC,CAAA;;cAEA;cACA,IAAAkC,GAAA;gBAAA;gBACA;gBACA;gBACAD,QAAA,CAAAjC,CAAA;gBACAiC,QAAA,CAAAjC,CAAA;gBACAiC,QAAA,CAAAjC,CAAA;cACA;YACA;;YAEA;YACA,MAAAsB,eAAA,GAAAvJ,MAAA,CAAAa,SAAA;YAEA9D,OAAA,CAAAO,GAAA;YACA4H,OAAA,CAAAqE,eAAA;UACA;UAEAhH,GAAA,CAAA7E,OAAA,GAAAP,KAAA;YACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;YACAgI,MAAA,CAAAhI,KAAA;UACA;;UAEA;UACAoF,GAAA,CAAAxD,GAAA,GAAA4K,WAAA;QACA,SAAAxM,KAAA;UACAJ,OAAA,CAAAI,KAAA,aAAAA,KAAA;UACAgI,MAAA,CAAAhI,KAAA;QACA;MACA;IACA;IAEA;IACA2C,8BAAA;MACA,KAAApE,eAAA;MACA,KAAAwB,QAAA,CAAAyB,IAAA;;MAEA;MACA,MAAAgK,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAQ,MAAA;;MAEA;MACAzQ,KAAA,CAAA8Q,IAAA,MAAAvO,aAAA,CAAAI,SAAA,EAAAsN,QAAA,EACAhE,IAAA,CAAAC,QAAA;QACA,KAAA6E,eAAA,CAAA7E,QAAA,CAAA3L,IAAA;MACA,GACA8L,KAAA,CAAA5H,KAAA;QAAA,IAAAiN,gBAAA;QACArN,OAAA,CAAAI,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,oBAAAiN,gBAAA,GAAAjN,KAAA,CAAAyH,QAAA,cAAAwF,gBAAA,gBAAAA,gBAAA,GAAAA,gBAAA,CAAAnR,IAAA,cAAAmR,gBAAA,uBAAAA,gBAAA,CAAAxP,OAAA,KAAAuC,KAAA,CAAAvC,OAAA;MACA,GACAoK,OAAA;QACA,KAAAtJ,eAAA;MACA;IACA;IAEA;IACA+N,gBAAAtL,MAAA;MACA,KAAAA,MAAA,KAAAA,MAAA,CAAAZ,OAAA;QACA,KAAAL,QAAA,CAAAC,KAAA,iBAAAgB,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAvD,OAAA;QACA;MACA;MAEA,KAAAsC,QAAA,CAAAK,OAAA;MACAR,OAAA,CAAAO,GAAA,aAAAa,MAAA;;MAEA;MACA,MAAAkM,OAAA,GAAAlM,MAAA,CAAAlF,IAAA;;MAEA;MACA,IAAAoR,OAAA,CAAAC,SAAA;QACA,KAAAlR,YAAA,CAAAG,YAAA,GAAA8Q,OAAA,CAAAC,SAAA;QACA;QACA,KAAAC,OAAA;UACAnE,KAAA;UACAxL,OAAA,EAAAyP,OAAA,CAAAC,SAAA;UACA/I,IAAA;UACAiJ,QAAA;QACA;;QAEA;QACA,KAAA5O,WAAA;;QAEA;QACA,KAAAsC,oBAAA,CAAAmM,OAAA,CAAAC,SAAA;MACA;;MAEA;MACA,IAAAD,OAAA,CAAArR,IAAA;QACA,KAAAI,YAAA,CAAAE,UAAA,GAAA+Q,OAAA,CAAArR,IAAA;MACA;IACA;IAEA;IACAiF,eAAAwM,GAAA;MACA;MACA,MAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;IAEA;IACAvM,qBAAA3E,YAAA;MACA,KAAAA,YAAA;QACA,KAAA2D,QAAA,CAAA0C,OAAA;QACA;MACA;MAEA,KAAA/D,mBAAA;MACA,KAAAqB,QAAA,CAAAyB,IAAA;;MAEA;MACA/F,iBAAA,CAAAW,YAAA,EACAoL,IAAA,CAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAA3L,IAAA;UACA,MAAA2R,UAAA,GAAAhG,QAAA,CAAA3L,IAAA;;UAEA;UACA8D,OAAA,CAAAO,GAAA,gBAAAsN,UAAA;;UAEA;UACA,KAAAxR,YAAA,CAAAC,QAAA,GAAAuR,UAAA,CAAAC,MAAA;UACA,KAAAzR,YAAA,CAAAE,UAAA,GAAAsR,UAAA,CAAAtR,UAAA;;UAEA;UACA,KAAAF,YAAA,CAAAQ,QAAA,GAAAgR,UAAA,CAAAnH,MAAA,IAAAmH,UAAA,CAAAE,SAAA;UACA,KAAA1R,YAAA,CAAAS,QAAA,GAAA+Q,UAAA,CAAA/Q,QAAA,IAAA+Q,UAAA,CAAAG,WAAA;;UAEA;UACA,KAAA3R,YAAA,CAAAI,WAAA,GAAAoR,UAAA,CAAAI,WAAA;UACA,KAAA5R,YAAA,CAAAK,WAAA,GAAAmR,UAAA,CAAAnR,WAAA;;UAEA;UACA,KAAAL,YAAA,CAAAM,sBAAA,GACAkR,UAAA,CAAAlR,sBAAA,IACAkR,UAAA,CAAAvH,QAAA,IACAuH,UAAA,CAAAK,eAAA,IACAL,UAAA,CAAAM,MAAA,IACAN,UAAA,CAAAO,eAAA,IACA;UAEApO,OAAA,CAAAO,GAAA,iBACA,6BAAAsN,UAAA,CAAAlR,sBAAA,EACA,eAAAkR,UAAA,CAAAvH,QAAA,EACA,sBAAAuH,UAAA,CAAAK,eAAA,EACA,aAAAL,UAAA,CAAAM,MAAA,EACA,sBAAAN,UAAA,CAAAO,eACA;UAEA,KAAA/R,YAAA,CAAAO,wBAAA,GAAAiR,UAAA,CAAAjR,wBAAA,IAAAiR,UAAA,CAAAtH,QAAA;;UAEA;UACAvG,OAAA,CAAAO,GAAA,oBAAAlE,YAAA,CAAAM,sBAAA;UAEA,KAAAN,YAAA,CAAAU,kBAAA,GAAA8Q,UAAA,CAAA9Q,kBAAA;UACA,KAAAV,YAAA,CAAAW,iBAAA,GAAA6Q,UAAA,CAAA7Q,iBAAA,OAAAC,IAAA,CAAA4Q,UAAA,CAAA7Q,iBAAA,QAAAC,IAAA;UACA,KAAAZ,YAAA,CAAAa,eAAA,GAAA2Q,UAAA,CAAA3Q,eAAA,OAAAD,IAAA,CAAA4Q,UAAA,CAAA3Q,eAAA;;UAEA;UACA8C,OAAA,CAAAO,GAAA,aAAAsN,UAAA,CAAAzQ,UAAA;UACA,KAAAf,YAAA,CAAAe,UAAA,QAAAkL,iBAAA,CAAAuF,UAAA,CAAAzQ,UAAA;UACA4C,OAAA,CAAAO,GAAA,kBAAAlE,YAAA,CAAAe,UAAA;UAEA,KAAAf,YAAA,CAAAgB,eAAA,GAAAwQ,UAAA,CAAAxQ,eAAA;UACA,KAAAhB,YAAA,CAAAiB,aAAA,GAAAuQ,UAAA,CAAAvQ,aAAA;UACA,KAAAjB,YAAA,CAAAkB,cAAA,GAAAsQ,UAAA,CAAAtQ,cAAA;;UAEA;UACA,KAAAmC,sBAAA,MAAArD,YAAA,CAAAe,UAAA;UAEA,IAAAyQ,UAAA,CAAA1Q,gBAAA;YACA,KAAAd,YAAA,CAAAc,gBAAA,OAAAF,IAAA,CAAA4Q,UAAA,CAAA1Q,gBAAA;UACA;UAEA,KAAAgD,QAAA,CAAAK,OAAA;;UAEA;UACA,KAAAgN,OAAA;YACAnE,KAAA;YACAxL,OAAA,YAAAgQ,UAAA,CAAAtR,UAAA,QAAAF,YAAA,CAAAS,QAAA,mBAAAT,YAAA,CAAAS,QAAA;YACA0H,IAAA;YACAiJ,QAAA;UACA;QACA;UACA,KAAAtN,QAAA,CAAA0C,OAAA,CAAAgF,QAAA,CAAAwG,GAAA;;UAEA;UACA,SAAAhS,YAAA,CAAAG,YAAA,UAAAH,YAAA,CAAAE,UAAA;YACA;YACA,KAAA+R,qBAAA,CAAA9R,YAAA;UACA;QACA;MACA,GACAwL,KAAA,CAAA5H,KAAA;QACAJ,OAAA,CAAAI,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,iBAAAA,KAAA,CAAAvC,OAAA;;QAEA;QACA,SAAAxB,YAAA,CAAAG,YAAA;UACA,KAAA8R,qBAAA,CAAA9R,YAAA;QACA;MACA,GACAyL,OAAA;QACA,KAAAnJ,mBAAA;MACA;IACA;IAEA;IACAwP,sBAAA9R,YAAA;MACA,KAAAA,YAAA,IAAAA,YAAA,CAAA8E,MAAA;MAEA;QACA;QACA,MAAAiN,UAAA,GAAAC,QAAA,CAAAhS,YAAA,CAAAiS,MAAA;QACA,MAAAC,MAAA,GAAAH,UAAA;QAEAvO,OAAA,CAAAO,GAAA,gBAAAmO,MAAA;MACA,SAAAlM,CAAA;QACAxC,OAAA,CAAAI,KAAA,iBAAAoC,CAAA;MACA;IACA;IAEA;IACAmM,kBAAA/B,WAAA;MACA;MACA;MACA;QACA,MAAApH,GAAA,OAAAC,KAAA;QACAD,GAAA,CAAAxD,GAAA,+BAAA4K,WAAA;QACApH,GAAA,CAAAV,MAAA;UACA;UACA;UACA;UACA,MAAA8J,gBAAA;UACA,OAAAA,gBAAA;QACA;MACA,SAAApM,CAAA;QACAxC,OAAA,CAAAI,KAAA,WAAAoC,CAAA;QACA;MACA;IACA;IAEA;IACAqM,gBAAArS,YAAA;MACA;MACA;MACA;MACA,MAAAsS,UAAA;QACAxS,QAAA;QAAA;QACAC,UAAA;QAAA;QACAC,YAAA,EAAAA,YAAA;QACAuS,YAAA;QACA5R,gBAAA,MAAAF,IAAA;QACAO,MAAA;MACA;MAEA,KAAAnB,YAAA;QAAA,GAAAyS;MAAA;MACA,KAAA3O,QAAA,CAAAK,OAAA;IACA;IAEA;IACAwO,YAAA3D,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAhH,IAAA;MACA,GAAAoD,IAAA;QACA,KAAAhJ,SAAA,CAAA6M,MAAA,CAAAJ,KAAA;QACA,KAAAlL,QAAA,CAAAK,OAAA;MACA,GAAAwH,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAiH,kBAAA;MACA,UAAA5S,YAAA,CAAAG,YAAA;QACA,KAAA2D,QAAA,CAAA0C,OAAA;QACA;MACA;;MAEA;MACA,KAAA1B,oBAAA,MAAA9E,YAAA,CAAAG,YAAA;IACA;IAEA;IACA0S,iBAAAvP,KAAA;MACA,MAAAwP,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAxP,KAAA;IACA;IAEA;IACAuG,mBAAA;MACA,SAAAtH,SAAA,CAAA0C,MAAA;QACA,KAAAnB,QAAA,CAAA0C,OAAA;QACA;MACA;MAEA,KAAA9D,eAAA;MACA,KAAAoB,QAAA,CAAAyB,IAAA;;MAEA;MACA,MAAAwN,GAAA,OAAArT,KAAA;MACA,MAAAsT,QAAA;MACA,MAAAC,SAAA,GAAAF,GAAA,CAAAG,QAAA,CAAAC,QAAA,CAAAC,QAAA;MACA,MAAAC,UAAA,GAAAN,GAAA,CAAAG,QAAA,CAAAC,QAAA,CAAAG,SAAA;;MAEA;MACA,KAAA/Q,SAAA,CAAAgR,OAAA,EAAAC,KAAA,EAAAxE,KAAA;QACA,MAAAyE,OAAA,OAAA5H,OAAA,CAAAC,OAAA;UACA;UACA,IAAA0H,KAAA,CAAArO,SAAA,IAAAqO,KAAA,CAAArO,SAAA,CAAAS,UAAA;YACA;YACA,MAAAuD,GAAA,OAAAC,KAAA;YACAD,GAAA,CAAAxD,GAAA,GAAA6N,KAAA,CAAArO,SAAA;YAEAgE,GAAA,CAAAV,MAAA;cACA;cACA,IAAAiL,QAAA,GAAAT,SAAA;cACA,IAAAU,SAAA,GAAAxK,GAAA,CAAAnC,MAAA,GAAA0M,QAAA,GAAAvK,GAAA,CAAApC,KAAA;;cAEA;cACA,IAAA4M,SAAA,GAAAN,UAAA;gBACAM,SAAA,GAAAN,UAAA;gBACAK,QAAA,GAAAvK,GAAA,CAAApC,KAAA,GAAA4M,SAAA,GAAAxK,GAAA,CAAAnC,MAAA;cACA;;cAEA;cACA,IAAAgI,KAAA;gBACA+D,GAAA,CAAAa,OAAA;cACA;;cAEA;cACAb,GAAA,CAAAc,QAAA,CACAL,KAAA,CAAArO,SAAA,EACA,QACA,CAAA8N,SAAA,GAAAS,QAAA;cAAA;cACA;cAAA;cACAA,QAAA,EACAC,SACA;cAEA7H,OAAA;YACA;YAEA3C,GAAA,CAAA7E,OAAA;cACA,KAAAR,QAAA,CAAAC,KAAA,OAAAiL,KAAA;cACAlD,OAAA;YACA;UACA,WAAA0H,KAAA,CAAApO,IAAA,IAAAoO,KAAA,CAAApO,IAAA,CAAAQ,UAAA;YACA;YACAjC,OAAA,CAAAO,GAAA,yBAAAsP,KAAA,CAAApO,IAAA;;YAEA;YACA,SAAArF,gBAAA;cACA,KAAAsG,kBAAA,iBAAAmN,KAAA,CAAApO,IAAA;;cAEA;cACA1B,UAAA;gBACA,KAAA8P,KAAA,CAAArO,SAAA,KAAAqO,KAAA,CAAArO,SAAA,CAAAS,UAAA;kBACA,KAAA9B,QAAA,CAAA0C,OAAA,SAAAwI,KAAA;kBACAlD,OAAA;gBACA;cACA;YACA;cACA,KAAAhI,QAAA,CAAA0C,OAAA,SAAAwI,KAAA;cACAlD,OAAA;YACA;UACA;YACA;YACA,KAAAhI,QAAA,CAAA0C,OAAA,KAAAwI,KAAA;YACAlD,OAAA;UACA;QACA;QAEAkH,QAAA,CAAA9N,IAAA,CAAAuO,OAAA;MACA;;MAEA;MACA5H,OAAA,CAAAiI,GAAA,CAAAd,QAAA,EAAAzH,IAAA;QACA;QACA,MAAArL,UAAA,QAAAF,YAAA,CAAAE,UAAA;QACA,MAAAC,YAAA,QAAAH,YAAA,CAAAG,YAAA;QACA,MAAAkF,SAAA,OAAAzE,IAAA,GAAA0E,OAAA;QACA,MAAAuK,QAAA,MAAA3P,UAAA,IAAAC,YAAA,OAAAkF,SAAA;;QAEA;QACA,MAAAgI,OAAA,GAAA0F,GAAA,CAAAgB,MAAA;;QAEA;QACA,MAAAxG,OAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,OAAA;QACA,MAAArC,UAAA;UACApL,IAAA,EAAAiQ,QAAA;UACA1H,IAAA;UACAE,IAAA,EAAAgF,OAAA,CAAAhF,IAAA;UACAxI,IAAA,EAAA0N,OAAA;UACAtF,IAAA,MAAA6H,IAAA,EAAAzC,OAAA,GAAAwC,QAAA;YAAA1H,IAAA;UAAA;QACA;;QAEA;QACA,KAAAnI,YAAA,CAAAqB,WAAA,CAAA6D,IAAA,CAAA8F,UAAA;;QAEA;QACA,KAAAhL,YAAA,CAAAoB,YAAA,GAAA4J,UAAA;;QAEA;QACA,MAAA1C,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,aAAA,CAAA6E,OAAA;QACA/E,MAAA,CAAA0L,SAAA;UACA,MAAAC,UAAA,GAAA3L,MAAA,CAAAvD,MAAA;UACA;UACAiG,UAAA,CAAAE,MAAA,GAAA+I,UAAA;QACA;QAEA,KAAAnQ,QAAA,CAAAK,OAAA;QACA,KAAAzB,eAAA;MACA,GAAAiJ,KAAA,CAAA5H,KAAA;QACAJ,OAAA,CAAAI,KAAA,aAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,gBAAAA,KAAA,CAAAvC,OAAA;QACA,KAAAkB,eAAA;MACA;IACA;IAEA;IACAwR,uBAAAjM,IAAA;MACA;MACA;MACA,MAAAkM,UAAA,GAAAlM,IAAA,CAAAwG,GAAA,IAAAxG,IAAA;MAEA,IAAAkM,UAAA;QACA,MAAA/L,OAAA,GAAA+L,UAAA,CAAA9L,IAAA;QACA,KAAAD,OAAA;UACA,KAAAtE,QAAA,CAAAC,KAAA;UACA;QACA;;QAEA;QACA,KAAAD,QAAA,CAAAyB,IAAA;QAEA,MAAA+C,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,aAAA,CAAA2L,UAAA;QACA7L,MAAA,CAAAG,MAAA,GAAAtC,CAAA;UACA;UACA,MAAAiO,QAAA,QAAAC,eAAA,CAAAF,UAAA,CAAAhM,IAAA;UAEA,IAAAmM,QAAA,GAAAnO,CAAA,CAAAwC,MAAA,CAAA5D,MAAA;;UAEA;UACA,KAAAuP,QAAA,WAAAA,QAAA,kBAAAA,QAAA,CAAAzO,QAAA;YACAlC,OAAA,CAAAC,IAAA;YACA0Q,QAAA,GAAA9G,GAAA,CAAAC,eAAA,CAAA0G,UAAA;UACA;;UAEA;UACA,MAAAnJ,UAAA;YACApL,IAAA,EAAAuU,UAAA,CAAAvU,IAAA;YACAuI,IAAA,EAAAgM,UAAA,CAAAhM,IAAA;YACAE,IAAA,EAAA8L,UAAA,CAAA9L,IAAA;YACAxI,IAAA,EAAAyU,QAAA;YACArM,IAAA,EAAAkM,UAAA;YACAI,IAAA,EAAAH;UACA;;UAEA;UACA,KAAApU,YAAA,CAAAqB,WAAA,CAAA6D,IAAA,CAAA8F,UAAA;;UAEA;UACA,IAAAmJ,UAAA,CAAAhM,IAAA,gCAAAnI,YAAA,CAAAoB,YAAA;YACA,KAAApB,YAAA,CAAAoB,YAAA,GAAA4J,UAAA;YACA,KAAAlH,QAAA,CAAAK,OAAA;UACA;YACA,KAAAL,QAAA,CAAAK,OAAA;UACA;QACA;QACAmE,MAAA,CAAAhE,OAAA,GAAAP,KAAA;UACAJ,OAAA,CAAAI,KAAA,WAAAA,KAAA;UACA,KAAAD,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAsQ,gBAAAG,QAAA;MACA,IAAAA,QAAA,CAAA3O,QAAA;QACA;MACA,WAAA2O,QAAA;QACA;MACA,WAAAA,QAAA,CAAA3O,QAAA,YAAA2O,QAAA,6BAAAA,QAAA;QACA;MACA,WAAAA,QAAA,CAAA3O,QAAA,aAAA2O,QAAA,mCAAAA,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC,uBAAAxM,IAAA;MACA;MACA,MAAAG,OAAA,GAAAH,IAAA,CAAAI,IAAA;MACA,KAAAD,OAAA;QACA,KAAAtE,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,MAAA2Q,YAAA,IACA,mBACA,sBACA,2EACA,4BACA,qEACA,cACA,aACA,YACA;MAEA,MAAAC,aAAA,GAAAD,YAAA,CAAA7O,QAAA,CAAAoC,IAAA,CAAAE,IAAA,KAAAF,IAAA,CAAAE,IAAA,CAAAvC,UAAA;MACA,KAAA+O,aAAA;QACA,KAAA7Q,QAAA,CAAAC,KAAA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}