<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.daka.pro.mapper.AttendanceMapper">

    <!-- 获取项目今日考勤人数 -->
    <select id="countTodayByProjectId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM attendance
        WHERE project_code = #{projectId}
        AND attendance_date LIKE CONCAT(#{attendDate}, '%')
    </select>

</mapper> 