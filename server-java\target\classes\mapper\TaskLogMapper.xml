<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daka.pro.mapper.TaskLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.daka.pro.domain.TaskLog">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="task_name" property="taskName" />
        <result column="project" property="project" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="duration" property="duration" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="content" property="content" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, task_name, project, start_time, end_time, duration, status, error_msg, content
    </sql>

    <!-- 分页查询指定任务的执行日志 -->
    <select id="getTaskLogList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sys_task_log
        WHERE task_id = #{taskId}
        ORDER BY start_time DESC
    </select>

</mapper> 