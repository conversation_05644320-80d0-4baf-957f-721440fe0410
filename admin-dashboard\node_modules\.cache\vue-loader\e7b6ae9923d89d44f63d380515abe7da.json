{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractUpload.vue?vue&type=style&index=0&id=4ad2e746&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractUpload.vue", "mtime": 1753669028133}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749542423828}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749542425518}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749542425132}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractUpload.vue"], "names": [], "mappings": ";AAy6DA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "ContractUpload.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\r\n  <div class=\"contract-upload-container\">\r\n    <h3>合同上传</h3>\r\n    \r\n    <div class=\"scanner-container\">\r\n      <div class=\"scanner-layout\">\r\n        <!-- 左侧：高拍仪界面 -->\r\n        <div class=\"scanner-left\">\r\n          <div class=\"scanner-preview\">\r\n            <img id=\"photo\" src=\"\" width=\"600\" height=\"400\" ref=\"scannerPreview\">\r\n          </div>\r\n          \r\n          <div class=\"scanner-controls\">\r\n            <el-button type=\"primary\" @click=\"startScanner\">启动高拍仪</el-button>\r\n            <el-button type=\"success\" @click=\"scanContract\">拍摄合同</el-button>\r\n            <el-button type=\"warning\" @click=\"stopScanner\">停止高拍仪</el-button>\r\n            <el-upload\r\n              class=\"upload-button\"\r\n              action=\"#\"\r\n              :show-file-list=\"false\"\r\n              :before-upload=\"handleUploadImage\">\r\n              <el-button type=\"primary\" icon=\"el-icon-upload\">上传图片</el-button>\r\n            </el-upload>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 右侧：已拍摄照片 -->\r\n        <div class=\"scanner-right\" v-if=\"photoList.length > 0\">\r\n          <h4>已拍摄照片 ({{ photoList.length }})</h4>\r\n          <div class=\"photo-actions-top\">\r\n            <el-button type=\"primary\" @click=\"convertImagesToPdf\" :loading=\"isGeneratingPDF\" icon=\"el-icon-document\">\r\n              生成PDF\r\n            </el-button>\r\n          </div>\r\n          <div class=\"photo-items\">\r\n            <div v-for=\"(photo, index) in photoList\" :key=\"index\" class=\"photo-item\">\r\n              <img :src=\"photo.imageData\" alt=\"拍摄照片\" class=\"photo-thumbnail\">\r\n              <div class=\"photo-actions\">\r\n                <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-delete\" @click=\"removePhoto(index)\">删除</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"contract-info-form\">\r\n      <el-form :model=\"contractForm\" label-width=\"120px\" ref=\"contractForm\" :rules=\"rules\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\r\n              <el-input v-model=\"contractForm.workerName\" placeholder=\"请输入工人姓名\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\r\n              <div class=\"id-card-input\">\r\n                <el-input v-model=\"contractForm.idCardNumber\" placeholder=\"请输入证件号码\"></el-input>\r\n                <el-button type=\"primary\" icon=\"el-icon-refresh\" class=\"refresh-button\" @click=\"refreshWorkerInfo\" :loading=\"isLoadingWorkerInfo\">刷新</el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目名称\" prop=\"ProjectName\">\r\n              <el-input v-model=\"contractForm.ProjectName\" placeholder=\"项目名称\" :disabled=\"true\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目编号\" prop=\"projectCode\">\r\n              <el-input v-model=\"contractForm.projectCode\" placeholder=\"项目编号\" :disabled=\"true\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"参建单位\" prop=\"projectSubContractorName\">\r\n              <el-input v-model=\"contractForm.projectSubContractorName\" placeholder=\"请输入参建单位\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"签订日期\" prop=\"contractSignDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractSignDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同期限类型\" prop=\"contractPeriodType\">\r\n              <el-select v-model=\"contractForm.contractPeriodType\" placeholder=\"请选择合同期限类型\" style=\"width: 100%\">\r\n                <el-option label=\"固定期限\" value=\"固定期限\"></el-option>\r\n                <el-option label=\"无固定期限\" value=\"无固定期限\"></el-option>\r\n                <el-option label=\"以完成一定工作任务为期限\" value=\"以完成一定工作任务为期限\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同开始日期\" prop=\"contractStartDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractStartDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择开始日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同结束日期\" prop=\"contractEndDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractEndDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择结束日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工资类型\" prop=\"salaryType\">\r\n              <el-select v-model=\"contractForm.salaryType\" placeholder=\"请选择工资类型\" style=\"width: 100%\" @change=\"handleSalaryTypeChange\">\r\n                <el-option label=\"按天\" value=\"按天\"></el-option>\r\n                <el-option label=\"按月\" value=\"按月\"></el-option>\r\n                <el-option label=\"按工程量\" value=\"按工程量\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"contractForm.remark\" placeholder=\"请输入备注信息\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <!-- 根据工资类型显示不同的字段 -->\r\n        <el-row :gutter=\"20\" v-if=\"['按天', '按月'].includes(contractForm.salaryType)\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"计量单价\" prop=\"salaryUnitPrice\">\r\n              <el-input v-model=\"contractForm.salaryUnitPrice\" placeholder=\"请输入计量单价\">\r\n                <template slot=\"append\">元/{{contractForm.salaryType === '按天' ? '天' : '月'}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\" v-if=\"contractForm.salaryType === '按工程量'\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"每(工程数量)\" prop=\"salaryPerUnit\">\r\n              <el-input v-model=\"contractForm.salaryPerUnit\" placeholder=\"请输入工程数量\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"计量单位\" prop=\"salaryUnitType\">\r\n              <el-select v-model=\"contractForm.salaryUnitType\" placeholder=\"请选择计量单位\" style=\"width: 100%\">\r\n                <el-option label=\"米\" value=\"80\"></el-option>\r\n                <el-option label=\"平方米\" value=\"81\"></el-option>\r\n                <el-option label=\"立方米\" value=\"82\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"计量单价\" prop=\"salaryUnitPrice\">\r\n              <el-input v-model=\"contractForm.salaryUnitPrice\" placeholder=\"请输入计量单价\">\r\n                <template slot=\"append\">元/{{getUnitTypeLabel(contractForm.salaryUnitType) || '单位'}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 显示合同附件 -->\r\n      <div class=\"contract-attachments\">\r\n        <h4>合同附件</h4>\r\n        \r\n        <!-- 添加手动上传附件按钮 -->\r\n        <div class=\"attachment-actions\">\r\n          <el-upload\r\n            class=\"upload-attachment\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            :on-change=\"handleAttachmentUpload\"\r\n            :before-upload=\"beforeAttachmentUpload\"\r\n            :multiple=\"false\">\r\n            <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">上传附件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持PDF、Word、Excel、图片等格式文件，单个文件不超过10MB</div>\r\n          </el-upload>\r\n        </div>\r\n        \r\n        <el-table v-if=\"contractForm.attachments.length > 0\" :data=\"contractForm.attachments\" style=\"width: 100%\">\r\n          <el-table-column label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"scope.row.icon || 'el-icon-document'\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.name }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"type\" label=\"类型\" width=\"150\"></el-table-column>\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ (scope.row.size / 1024).toFixed(2) }} KB\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button \r\n                size=\"mini\" \r\n                type=\"primary\" \r\n                icon=\"el-icon-view\"\r\n                @click=\"previewAttachment(scope.row)\">预览</el-button>\r\n              <el-button \r\n                size=\"mini\" \r\n                type=\"danger\" \r\n                icon=\"el-icon-delete\"\r\n                @click=\"removeAttachment(scope.$index)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div v-else class=\"no-attachments\">\r\n          暂无附件\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-actions\">\r\n        <el-button type=\"primary\" @click=\"submitContract\">提交合同</el-button>\r\n        <el-button @click=\"goBack\">返回</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { saveContractFile, getWorkerByIdCard, uploadContract } from '@/api/roster'\r\nimport jsPDF from 'jspdf'\r\nimport html2canvas from 'html2canvas'\r\n\r\nexport default {\r\n  name: 'ContractUpload',\r\n  data() {\r\n    return {\r\n      webSocket: null,\r\n      scannerConnected: false,\r\n      contractForm: {\r\n        workerId: '',\r\n        workerName: '',\r\n        idCardNumber: '',\r\n        ProjectName: '', // 项目名称\r\n        projectCode: '', // 项目编号\r\n        projectSubContractorId: '', // 参建单位ID\r\n        projectSubContractorName: '', // 参建单位\r\n        teamCode: '', // 班组编号\r\n        teamName: '', // 班组名称\r\n        contractPeriodType: '固定期限', // 合同期限类型\r\n        contractStartDate: new Date(), // 合同开始日期\r\n        contractEndDate: null, // 合同结束日期\r\n        contractSignDate: new Date(),\r\n        salaryType: '按天', // 工资类型\r\n        salaryUnitPrice: '', // 工资单价\r\n        salaryPerUnit: '', // 每(工程数量)\r\n        salaryUnitType: '', // 计量单位\r\n        remark: '',\r\n        contractFile: null,\r\n        attachments: [] // 合同附件数组\r\n      },\r\n      rules: {\r\n        contractSignDate: [\r\n          { required: true, message: '请选择签订日期', trigger: 'change' }\r\n        ],\r\n        workerName: [\r\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\r\n        ],\r\n        idCardNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' }\r\n        ],\r\n        projectSubContractorName: [\r\n          { required: true, message: '请输入参建单位', trigger: 'blur' }\r\n        ],\r\n        contractPeriodType: [\r\n          { required: true, message: '请选择合同期限类型', trigger: 'change' }\r\n        ],\r\n        contractStartDate: [\r\n          { required: true, message: '请选择合同开始日期', trigger: 'change' }\r\n        ],\r\n        salaryType: [\r\n          { required: true, message: '请选择工资类型', trigger: 'change' }\r\n        ],\r\n        salaryUnitPrice: [\r\n          { required: true, message: '请输入计量单价', trigger: 'blur' }\r\n        ]\r\n      },\r\n      simulationMode: false, // 模拟模式标志\r\n      connectionTimeout: null, // 连接超时\r\n      manuallyDisconnected: false, // 手动断开标志\r\n      scannerConfig: {\r\n        wsUrl: 'ws://localhost:1818', // WebSocket连接地址\r\n        timeout: 3000, // 连接超时时间(毫秒)\r\n        autoSimulate: true, // 连接失败时是否自动切换到模拟模式\r\n        ocrApiUrl: 'http://127.0.0.1:5000/ocr' // OCR API地址\r\n      },\r\n      configDialogVisible: false, // 高拍仪配置对话框可见性\r\n      tempScannerConfig: { // 临时存储的高拍仪配置\r\n        wsUrl: 'ws://localhost:1818',\r\n        timeout: 3000,\r\n        autoSimulate: true,\r\n        ocrApiUrl: 'http://127.0.0.1:5000/ocr'\r\n      },\r\n      currentPhotoPath: '', // 当前拍摄的照片路径\r\n      isProcessingUpload: false, // 是否正在处理上传\r\n      isProcessingOcr: false, // 是否正在处理OCR\r\n      photoList: [], // 拍摄的照片列表\r\n      idCardFound: false, // 是否已识别到身份证\r\n      isLoadingWorkerInfo: false, // 是否正在加载工人信息\r\n      isGeneratingPDF: false, // 是否正在生成PDF\r\n      currentImageData: '', // 当前接收到的图像数据\r\n      waitingForBase64: false // 是否正在等待 Base64Encode 的响应\r\n    }\r\n  },\r\n  created() {\r\n    // 从路由参数获取工人信息\r\n    const { workerId, workerName, idCardNumber } = this.$route.query\r\n    if (workerId && workerName && idCardNumber) {\r\n      this.contractForm.workerId = workerId\r\n      this.contractForm.workerName = workerName\r\n      this.contractForm.idCardNumber = idCardNumber\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化高拍仪WebSocket连接\r\n    this.initScannerWebSocket()\r\n  },\r\n  beforeDestroy() {\r\n    // 组件销毁前关闭WebSocket连接\r\n    this.closeWebSocket()\r\n  },\r\n  methods: {\r\n    // 处理工资类型变更\r\n    handleSalaryTypeChange(value) {\r\n      // 重置相关字段\r\n      this.contractForm.salaryUnitPrice = '';\r\n      \r\n      if (value === '按工程量') {\r\n        this.contractForm.salaryPerUnit = '';\r\n        this.contractForm.salaryUnitType = '';\r\n        \r\n        // 动态添加验证规则\r\n        this.$set(this.rules, 'salaryPerUnit', [\r\n          { required: true, message: '请输入工程数量', trigger: 'blur' }\r\n        ]);\r\n        this.$set(this.rules, 'salaryUnitType', [\r\n          { required: true, message: '请选择计量单位', trigger: 'change' }\r\n        ]);\r\n      } else {\r\n        // 移除验证规则\r\n        this.$delete(this.rules, 'salaryPerUnit');\r\n        this.$delete(this.rules, 'salaryUnitType');\r\n      }\r\n    },\r\n    \r\n    // 初始化高拍仪WebSocket连接\r\n    initScannerWebSocket() {\r\n      try {\r\n        // 添加模拟模式标志\r\n        this.simulationMode = false;\r\n        \r\n        // 尝试连接WebSocket\r\n        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)\r\n        \r\n        // 设置连接超时\r\n        this.connectionTimeout = setTimeout(() => {\r\n          if (!this.scannerConnected) {\r\n            console.warn('高拍仪连接超时，切换到模拟模式');\r\n            if (this.scannerConfig.autoSimulate) {\r\n              this.switchToSimulationMode();\r\n            } else {\r\n              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\r\n            }\r\n          }\r\n        }, this.scannerConfig.timeout);\r\n        \r\n        this.webSocket.onopen = (event) => {\r\n          console.log('高拍仪WebSocket连接成功')\r\n          this.scannerConnected = true\r\n          this.$message.success('高拍仪连接成功')\r\n          clearTimeout(this.connectionTimeout);\r\n        }\r\n        \r\n        this.webSocket.onclose = (event) => {\r\n          console.log('高拍仪WebSocket连接关闭')\r\n          this.scannerConnected = false\r\n          // 如果不是主动关闭，尝试切换到模拟模式\r\n          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {\r\n            this.switchToSimulationMode();\r\n          }\r\n        }\r\n        \r\n        this.webSocket.onerror = (event) => {\r\n          console.error('高拍仪WebSocket连接错误', event)\r\n          this.scannerConnected = false\r\n          if (this.scannerConfig.autoSimulate) {\r\n            this.switchToSimulationMode();\r\n          } else {\r\n            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\r\n          }\r\n        }\r\n        \r\n        this.webSocket.onmessage = (event) => {\r\n          this.handleScannerMessage(event)\r\n        }\r\n      } catch (error) {\r\n        console.error('初始化高拍仪WebSocket失败', error)\r\n        if (this.scannerConfig.autoSimulate) {\r\n          this.switchToSimulationMode();\r\n        } else {\r\n          this.$message.error(`初始化高拍仪失败: ${error.message}`);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理高拍仪消息\r\n    handleScannerMessage(event) {\r\n      const begin_data = \"data:image/jpeg;base64,\"\r\n      \r\n      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {\r\n        // 处理条码识别结果\r\n        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')\r\n        this.$message.success('识别到条码: ' + barcode)\r\n        \r\n        // 如果是身份证号格式，填入表单\r\n        if (this.isIdCardNumber(barcode)) {\r\n          this.contractForm.idCardNumber = barcode\r\n          this.searchWorkerByIdCard(barcode)\r\n        }\r\n      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {\r\n        // 处理保存图片结果\r\n        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')\r\n        this.$message.success('图片保存成功: ' + result)\r\n      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {\r\n        // 处理 Base64Encode 命令返回的 base64 数据\r\n        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')\r\n        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)\r\n        \r\n        // 重置等待标志\r\n        this.waitingForBase64 = false\r\n        \r\n        if (base64Data && base64Data.length > 1000) { // 确保数据有效\r\n          // 保存 base64 数据\r\n          this.currentImageData = begin_data + base64Data\r\n          \r\n          // 添加到照片列表\r\n          this.photoList.push({\r\n            imageData: this.currentImageData,\r\n            path: this.currentPhotoPath,\r\n            timestamp: new Date().getTime()\r\n          })\r\n          \r\n          // 使用高质量 base64 数据进行 OCR 识别\r\n          console.log('使用高质量 base64 数据进行 OCR 识别')\r\n          this.$message.info('正在识别证件号码...')\r\n          this.processOcrWithImage(this.currentImageData)\r\n        } else {\r\n          console.error('获取到的 base64 数据无效或太短')\r\n          \r\n          // 如果 base64 数据无效，尝试使用预览图或文件路径\r\n          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \r\n              this.$refs.scannerPreview.src.startsWith('data:image')) {\r\n            console.log('使用预览图数据进行 OCR 识别')\r\n            const imageData = this.$refs.scannerPreview.src\r\n            \r\n            // 添加到照片列表\r\n            this.photoList.push({\r\n              imageData: imageData,\r\n              path: this.currentPhotoPath,\r\n              timestamp: new Date().getTime()\r\n            })\r\n            \r\n            this.processOcrWithImage(imageData)\r\n          } else {\r\n            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\r\n            \r\n            // 添加到照片列表（使用空图像数据）\r\n            this.photoList.push({\r\n              imageData: '',\r\n              path: this.currentPhotoPath,\r\n              timestamp: new Date().getTime()\r\n            })\r\n            \r\n            this.processOcrWithImage(this.currentPhotoPath)\r\n          }\r\n        }\r\n      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {\r\n        // 处理目录检查结果\r\n        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')\r\n        console.log('目录检查结果:', result)\r\n        // 如果目录不存在，结果为\"0\"，存在则为\"1\"\r\n        if (result === \"0\") {\r\n          console.log('C:\\\\pic\\\\ 目录不存在，将创建')\r\n        }\r\n      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {\r\n        // 处理创建目录结果\r\n        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')\r\n        console.log('创建目录结果:', result)\r\n        // 如果创建成功，结果为\"1\"，失败则为\"0\"\r\n        if (result === \"1\") {\r\n          console.log('C:\\\\pic\\\\ 目录创建成功')\r\n        } else {\r\n          console.warn('C:\\\\pic\\\\ 目录创建失败或已存在')\r\n        }\r\n      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {\r\n        // 处理条码识别命令响应，不作为图像数据处理\r\n        console.log('收到条码识别命令响应:', event.data)\r\n      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {\r\n        // 处理图像数据 - 判断是否为base64图像数据\r\n        // 增加额外检查，确保不是条码数据\r\n        if (this.$refs.scannerPreview) {\r\n          try {\r\n            // 尝试验证是否为有效的base64图像数据\r\n            const testData = event.data.substring(0, 100); // 只取前100个字符测试\r\n            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常\r\n            \r\n            // 确保是完整的base64数据\r\n            const imgData = begin_data + event.data\r\n            this.$refs.scannerPreview.src = imgData\r\n            \r\n            // 保存当前图像数据以备后用\r\n            this.currentImageData = imgData\r\n            \r\n            // 保存当前图像数据到合同文件\r\n            this.contractForm.contractFile = imgData\r\n            console.log('成功保存图像数据，长度:', event.data.length)\r\n          } catch (e) {\r\n            console.error('收到的数据不是有效的base64图像:', e)\r\n          }\r\n        }\r\n      } else {\r\n        // 其他消息，可能是普通文本或命令响应\r\n        console.log('收到高拍仪消息:', event.data)\r\n      }\r\n    },\r\n    \r\n    // 启动高拍仪\r\n    startScanner() {\r\n      if (!this.scannerConnected) {\r\n        this.initScannerWebSocket()\r\n        return\r\n      }\r\n      \r\n      try {\r\n        // 设置分辨率\r\n        this.sendScannerCommand('vSetResolution(8)')\r\n        // 启用去黑边功能\r\n        this.sendScannerCommand('vSetDelHBFlag(true)')\r\n        // 启动主摄像头\r\n        this.sendScannerCommand('bStartPlay()')\r\n        this.$message.success('高拍仪已启动')\r\n      } catch (error) {\r\n        console.error('启动高拍仪失败', error)\r\n        this.$message.error('启动高拍仪失败')\r\n      }\r\n    },\r\n    \r\n    // 停止高拍仪\r\n    stopScanner() {\r\n      if (!this.scannerConnected) {\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.sendScannerCommand('bStopPlay()')\r\n        this.$message.success('高拍仪已停止')\r\n      } catch (error) {\r\n        console.error('停止高拍仪失败', error)\r\n        this.$message.error('停止高拍仪失败')\r\n      }\r\n    },\r\n    \r\n    // 扫描合同\r\n    scanContract() {\r\n      if (!this.scannerConnected && !this.simulationMode) {\r\n        this.$message.warning('请先启动高拍仪')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        if (this.simulationMode) {\r\n          // 模拟模式下，直接使用示例图片\r\n          this.processWithSimulationImage()\r\n          // 自动进行OCR识别\r\n          setTimeout(() => {\r\n            this.processOcrWithSimulationImage()\r\n          }, 1000)\r\n          return\r\n        }\r\n        \r\n        // 设置更高的分辨率 - 使用最高分辨率以确保合同完整清晰\r\n        // 分辨率值: 1=320*240, 2=640*480, 3=800*600, 4=1024*768, 5=1280*1024, 6=1600*1200, 7=2048*1536, 8=2592*1944\r\n        this.sendScannerCommand('vSetResolution(8)')\r\n        \r\n        // 确保启用去黑边功能\r\n        this.sendScannerCommand('vSetDelHBFlag(true)')\r\n        \r\n        // 设置A4文档模式 - 使用文档模式而不是证件模式\r\n        //this.sendScannerCommand('bSetMode(2)')\r\n        \r\n        // 设置自动裁剪模式 - 确保合同完整捕获\r\n        this.sendScannerCommand('vSetAutoCrop(true)')\r\n        \r\n        // 设置图像增强 - 提高清晰度\r\n        this.sendScannerCommand('vSetImageEnhance(true)')\r\n        \r\n        // 先检查目录是否存在，不存在则创建\r\n        this.sendScannerCommand('bDirIsExist(C:\\\\pic\\\\)')\r\n        \r\n        // 延迟一下，确保目录检查完成\r\n        setTimeout(() => {\r\n          // 创建目录（即使目录已存在，这个命令也不会报错）\r\n          this.sendScannerCommand('bCreateDir(C:\\\\pic\\\\)')\r\n          \r\n          // 生成唯一文件名（使用时间戳）\r\n          const timestamp = new Date().getTime()\r\n          const filename = `contract_${timestamp}`\r\n          this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\r\n          console.log('当前照片路径:', this.currentPhotoPath) \r\n          \r\n          // 拍照前提示用户\r\n          this.$message.info('正在拍摄合同，请确保文档完全平整并位于取景框内...')\r\n          \r\n          // 短暂延迟后拍照，给用户时间调整文档位置\r\n          setTimeout(() => {\r\n            // 拍照并保存到本地\r\n            this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\r\n            \r\n            // 清除之前的图像数据，确保不会使用旧数据\r\n            this.currentImageData = null\r\n            \r\n            this.$message.info('合同拍摄中，请稍候...')\r\n            \r\n            // 设置一个标志，表示我们正在等待 Base64Encode 的响应\r\n            this.waitingForBase64 = true\r\n            \r\n            // 延迟一下，确保图片保存完成\r\n            setTimeout(() => {\r\n              // 使用 Base64Encode 命令获取高质量的 base64 图像数据\r\n              this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)\r\n              \r\n              // 设置超时，确保即使没有收到 Base64Encode 的响应，也会继续处理\r\n              setTimeout(() => {\r\n                if (this.waitingForBase64) {\r\n                  console.log('Base64Encode 响应超时，使用备用方法')\r\n                  this.waitingForBase64 = false\r\n                  \r\n                  // 如果有预览图数据，使用预览图数据\r\n                  if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \r\n                      this.$refs.scannerPreview.src.startsWith('data:image')) {\r\n                    console.log('使用预览图数据')\r\n                    const imageData = this.$refs.scannerPreview.src\r\n                    \r\n                    // 添加到照片列表\r\n                    this.photoList.push({\r\n                      imageData: imageData,\r\n                      path: this.currentPhotoPath,\r\n                      timestamp: new Date().getTime()\r\n                    })\r\n                    \r\n                    // 调用OCR识别\r\n                    this.$message.info('正在识别证件号码...')\r\n                    this.processOcrWithImage(imageData)\r\n                  } else {\r\n                    // 如果没有图像数据，尝试使用文件路径\r\n                    console.log('使用文件路径:', this.currentPhotoPath)\r\n                    \r\n                    // 添加到照片列表（使用空图像数据）\r\n                    this.photoList.push({\r\n                      imageData: '',\r\n                      path: this.currentPhotoPath,\r\n                      timestamp: new Date().getTime()\r\n                    })\r\n                    \r\n                    // 调用OCR识别\r\n                    this.$message.info('正在识别证件号码...')\r\n                    this.processOcrWithImage(this.currentPhotoPath)\r\n                  }\r\n                }\r\n              }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法\r\n              \r\n            }, 1000) // 延迟1秒，确保图片保存完成\r\n          }, 500) // 给用户半秒钟时间调整文档位置\r\n        }, 500) // 延迟500ms，确保目录检查完成\r\n      } catch (error) {\r\n        console.error('扫描合同失败', error)\r\n        this.$message.error('扫描合同失败')\r\n      }\r\n    },\r\n    \r\n    // 模拟模式下使用示例图片\r\n    processWithSimulationImage() {\r\n      this.$message.info('模拟模式：请上传图片或使用高拍仪')\r\n      \r\n      // 设置空白图像\r\n      if (this.$refs.scannerPreview) {\r\n        // 创建一个空白的Canvas\r\n        const canvas = document.createElement('canvas')\r\n        canvas.width = 600\r\n        canvas.height = 400\r\n        const ctx = canvas.getContext('2d')\r\n        \r\n        // 填充浅灰色背景\r\n        ctx.fillStyle = '#f0f0f0'\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height)\r\n        \r\n        // 添加提示文字\r\n        ctx.fillStyle = '#666666'\r\n        ctx.font = '20px Arial'\r\n        ctx.textAlign = 'center'\r\n        ctx.fillText('请上传图片或使用高拍仪', canvas.width / 2, canvas.height / 2)\r\n        \r\n        // 转换为数据URL\r\n        const emptyImageUrl = canvas.toDataURL('image/jpeg')\r\n        this.$refs.scannerPreview.src = emptyImageUrl\r\n        this.contractForm.contractFile = emptyImageUrl\r\n      }\r\n    },\r\n    \r\n    // 发送高拍仪命令\r\n    sendScannerCommand(command) {\r\n      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {\r\n        this.webSocket.send(command)\r\n      } else {\r\n        throw new Error('WebSocket未连接')\r\n      }\r\n    },\r\n    \r\n    // 关闭WebSocket连接\r\n    closeWebSocket() {\r\n      if (this.webSocket) {\r\n        // 先停止高拍仪\r\n        if (this.scannerConnected) {\r\n          try {\r\n            this.webSocket.send('bStopPlay()')\r\n          } catch (e) {\r\n            console.error('停止高拍仪失败', e)\r\n          }\r\n        }\r\n        \r\n        // 关闭连接\r\n        this.webSocket.close()\r\n        this.webSocket = null\r\n        this.scannerConnected = false\r\n      }\r\n    },\r\n    \r\n    // 切换到模拟模式\r\n    switchToSimulationMode() {\r\n      this.simulationMode = true;\r\n      this.scannerConnected = false;\r\n      \r\n      if (this.webSocket) {\r\n        this.manuallyDisconnected = true;\r\n        this.webSocket.close();\r\n        this.webSocket = null;\r\n      }\r\n      \r\n      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动上传图片或使用模拟功能。');\r\n      \r\n      // 清除连接超时\r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n      }\r\n    },\r\n\r\n    // 上传图片处理函数\r\n    handleUploadImage(file) {\r\n      if (file) {\r\n        // 验证文件类型\r\n        const isImage = file.type.indexOf('image/') !== -1;\r\n        if (!isImage) {\r\n          this.$message.error('请上传图片文件!');\r\n          return false;\r\n        }\r\n        \r\n        // 验证文件大小 (限制为10MB)\r\n        const isLt10M = file.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n          this.$message.error('图片大小不能超过10MB!');\r\n          return false;\r\n        }\r\n        \r\n        this.$message.info('正在处理图片，请稍候...');\r\n        \r\n        // 更新预览图并压缩图片\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = (e) => {\r\n          // 压缩图片\r\n          this.compressImage(e.target.result, (compressedDataUrl) => {\r\n            // 更新预览图\r\n            if (this.$refs.scannerPreview) {\r\n              this.$refs.scannerPreview.src = compressedDataUrl;\r\n            }\r\n            \r\n            // 添加到照片列表\r\n            this.photoList.push({\r\n              imageData: compressedDataUrl,\r\n              path: '',\r\n              timestamp: new Date().getTime(),\r\n              isIdCard: false\r\n            });\r\n            \r\n            this.$message.info('图片上传成功，正在识别证件号码...');\r\n            \r\n            // 调用OCR识别\r\n            this.processOcrWithImage(compressedDataUrl);\r\n          });\r\n        };\r\n        \r\n        reader.onerror = (error) => {\r\n          console.error('读取图片文件失败', error);\r\n          this.$message.error('读取图片文件失败');\r\n        };\r\n      }\r\n      return false; // 阻止默认的上传行为\r\n    },\r\n    \r\n    // 压缩图片函数\r\n    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {\r\n      const img = new Image();\r\n      img.src = dataUrl;\r\n      \r\n      img.onload = () => {\r\n        // 创建Canvas\r\n        const canvas = document.createElement('canvas');\r\n        let width = img.width;\r\n        let height = img.height;\r\n        \r\n        // 计算缩放比例\r\n        if (width > height) {\r\n          if (width > maxWidth) {\r\n            height *= maxWidth / width;\r\n            width = maxWidth;\r\n          }\r\n        } else {\r\n          if (height > maxHeight) {\r\n            width *= maxHeight / height;\r\n            height = maxHeight;\r\n          }\r\n        }\r\n        \r\n        // 设置Canvas大小\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n        \r\n        // 绘制图像\r\n        const ctx = canvas.getContext('2d');\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n        \r\n        // 转换为压缩后的DataURL\r\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\r\n        \r\n        // 计算压缩率\r\n        const originalSize = dataUrl.length;\r\n        const compressedSize = compressedDataUrl.length;\r\n        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);\r\n        \r\n        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);\r\n        \r\n        callback(compressedDataUrl);\r\n      };\r\n      \r\n      img.onerror = () => {\r\n        console.error('图片压缩失败');\r\n        callback(dataUrl); // 失败时使用原始图片\r\n      };\r\n    },\r\n    \r\n    // 提交合同\r\n    submitContract() {\r\n      this.$refs.contractForm.validate(async valid => {\r\n        if (valid) {\r\n          if (this.contractForm.attachments.length === 0 && this.photoList.length === 0) {\r\n            this.$message.warning('请先拍摄或上传合同图片');\r\n            return;\r\n          }\r\n          \r\n          if (!this.idCardFound && !this.contractForm.idCardNumber) {\r\n            this.$message.warning('请先识别身份证或手动输入证件号码');\r\n            return;\r\n          }\r\n          \r\n          this.isProcessingUpload = true;\r\n          this.$message.info('正在上传合同，请稍候...');\r\n          \r\n          // 如果有拍摄的照片但还没有生成PDF，先生成PDF\r\n          if (this.photoList.length > 0 && this.contractForm.attachments.length === 0) {\r\n            this.convertImagesToPdf();\r\n            this.$message.info('请等待PDF生成完成后再提交');\r\n            this.isProcessingUpload = false;\r\n            return;\r\n          }\r\n          \r\n          // 打印提交前的contractForm，检查projectSubContractorId是否存在\r\n          console.log('提交前的contractForm:', JSON.stringify(this.contractForm))\r\n          console.log('参建单位ID (projectSubContractorId):', this.contractForm.projectSubContractorId)\r\n          \r\n          // 准备合同信息\r\n          const contract = {\r\n            corpCode: this.contractForm.projectSubContractorId || '',\r\n            corpName: this.contractForm.projectSubContractorName || '',\r\n            idCardType: '01', // 默认身份证\r\n            idNumber: this.contractForm.idCardNumber,\r\n            workerId: this.contractForm.workerId, // 工人ID\r\n            workerName: this.contractForm.workerName, // 工人姓名\r\n            teamNo: this.contractForm.teamCode, // 班组编号 \r\n            teamName: this.contractForm.teamName, // 班组名称\r\n            contractPeriodType: this.getSalaryTypeValue(this.contractForm.contractPeriodType),\r\n            startDate: this.formatDate(this.contractForm.contractStartDate),\r\n            endDate: this.formatDate(this.contractForm.contractEndDate),\r\n            signDate: this.formatDate(this.contractForm.contractSignDate || new Date()),\r\n            unit: this.getSalaryTypeValue(this.contractForm.salaryType), // 按日传1，按月传2，按工程量传3\r\n            unitPrice: Number(this.contractForm.salaryUnitPrice) || 100\r\n          };\r\n          \r\n          // 打印最终的contract对象，检查corpCode是否正确设置\r\n          console.log('最终的contract对象:', JSON.stringify(contract))\r\n          console.log('corpCode值:', contract.corpCode)\r\n          \r\n          // 如果是按工程量计算，添加每xx和计量单位信息\r\n          if (this.contractForm.salaryType === '按工程量') {\r\n            contract.perUnit = this.contractForm.salaryPerUnit || 1;\r\n            contract.unitType = this.contractForm.salaryUnitType || 80;\r\n          }\r\n          \r\n          // 准备附件信息\r\n          const attachments = [];\r\n          \r\n          try {\r\n            // 处理所有附件\r\n            if (this.contractForm.attachments && this.contractForm.attachments.length > 0) {\r\n              for (const attachment of this.contractForm.attachments) {\r\n                // 获取base64数据\r\n                let base64Data = '';\r\n                if (attachment.data) {\r\n                  if (typeof attachment.data === 'string' && attachment.data.startsWith('data:')) {\r\n                    // 已经是base64格式\r\n                    base64Data = attachment.data.split(',')[1]; // 移除\"data:application/pdf;base64,\"前缀\r\n                  } else if (attachment.file) {\r\n                    // 如果有base64属性，直接使用\r\n                    if (attachment.base64) {\r\n                      base64Data = attachment.base64.split(',')[1];\r\n                    } else {\r\n                      // 否则从文件读取\r\n                      base64Data = await this.readFileAsBase64Promise(attachment.file);\r\n                    }\r\n                  }\r\n                }\r\n                \r\n                attachments.push({\r\n                  name: attachment.name,\r\n                  data: base64Data\r\n                });\r\n              }\r\n            }\r\n            \r\n            // 准备请求数据\r\n            const requestData = {\r\n              projectCode: this.contractForm.projectCode,\r\n              contractList: [contract],\r\n              attachments: attachments\r\n            };\r\n            \r\n            // 打印最终的请求数据\r\n            console.log('上传合同的请求数据:', JSON.stringify({\r\n              projectCode: requestData.projectCode,\r\n              contractList: requestData.contractList,\r\n              attachmentsCount: requestData.attachments.length\r\n            }))\r\n            \r\n            // 调用上传API\r\n            uploadContract(requestData)\r\n              .then(response => {\r\n                if (response.code === 0) {\r\n                  this.$message.success('合同上传成功');\r\n                  // 返回上一页\r\n                  this.goBack();\r\n                } else {\r\n                  this.$message.error(response.message || '合同上传失败');\r\n                }\r\n              })\r\n              .catch(error => {\r\n                console.error('合同上传失败:', error);\r\n                this.$message.error('合同上传失败: ' + (error.message || '未知错误'));\r\n              })\r\n              .finally(() => {\r\n                this.isProcessingUpload = false;\r\n              });\r\n          } catch (error) {\r\n            console.error('处理附件失败:', error);\r\n            this.$message.error('处理附件失败: ' + (error.message || '未知错误'));\r\n            this.isProcessingUpload = false;\r\n          }\r\n        } else {\r\n          this.$message.warning('请完善合同信息');\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 读取文件为Base64的Promise版本\r\n    readFileAsBase64Promise(file) {\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onload = e => {\r\n          const base64Data = e.target.result.split(',')[1];\r\n          resolve(base64Data);\r\n        };\r\n        reader.onerror = e => reject(e);\r\n        reader.readAsDataURL(file);\r\n      });\r\n    },\r\n    \r\n    // 获取工资类型对应的值\r\n    getSalaryTypeValue(type) {\r\n      const typeMap = {\r\n        '按天': '1',\r\n        '按月': '2',\r\n        '按工程量': '3',\r\n        '固定期限': '0',\r\n        '无固定期限': '1',\r\n        '以完成一定工作任务为期限': '1'\r\n      };\r\n      return typeMap[type] || '1';\r\n    },\r\n    \r\n    // 获取工资类型对应的文本\r\n    getSalaryTypeText(type) {\r\n      const typeMap = {\r\n        '1': '按天',\r\n        '2': '按月',\r\n        '3': '按工程量',\r\n        '4': '其他方式'\r\n      };\r\n      return typeMap[type] || '按天';\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '';\r\n      const d = new Date(date);\r\n      const year = d.getFullYear();\r\n      const month = String(d.getMonth() + 1).padStart(2, '0');\r\n      const day = String(d.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    \r\n    // 预览附件\r\n    previewAttachment(attachment) {\r\n      if (attachment && attachment.data) {\r\n        // 判断文件类型\r\n        if (attachment.type && attachment.type.includes('image/')) {\r\n          // 图片类型 - 在弹窗中预览\r\n          this.$msgbox({\r\n            title: attachment.name,\r\n            message: `<div style=\"text-align:center;height:100%;width:100%;display:flex;align-items:center;justify-content:center;\">\r\n              <img src=\"${attachment.data}\" style=\"max-width:100%;max-height:100%;object-fit:contain;\" />\r\n            </div>`,\r\n            dangerouslyUseHTMLString: true,\r\n            customClass: 'image-preview-dialog pdf-preview-dialog', // 使用相同的最大化样式\r\n            showCancelButton: false,\r\n            showConfirmButton: false\r\n          });\r\n        } else if (attachment.type === 'application/pdf') {\r\n          try {\r\n            // PDF类型 - 创建Blob URL并使用对象标签预览\r\n            const pdfBlob = this.dataURLtoBlob(attachment.data);\r\n            const blobUrl = URL.createObjectURL(pdfBlob);\r\n            \r\n            // 使用MessageBox组件以最大化方式显示PDF\r\n            this.$msgbox({\r\n              title: attachment.name,\r\n              message: `<div style=\"height:100%;width:100%;\">\r\n                <object \r\n                  data=\"${blobUrl}\" \r\n                  type=\"application/pdf\" \r\n                  width=\"100%\" \r\n                  height=\"100%\"\r\n                  style=\"width:100%;height:100%;\">\r\n                    <p>您的浏览器不支持PDF预览，请 \r\n                      <a href=\"${blobUrl}\" download=\"${attachment.name}\">点击下载</a>\r\n                    </p>\r\n                </object>\r\n              </div>`,\r\n              dangerouslyUseHTMLString: true,\r\n              customClass: 'pdf-preview-dialog',\r\n              showCancelButton: false,\r\n              showConfirmButton: false,\r\n              beforeClose: (action, instance, done) => {\r\n                // 关闭对话框时释放blob URL\r\n                URL.revokeObjectURL(blobUrl);\r\n                done();\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error('预览PDF失败:', error);\r\n            this.$message.error('无法预览PDF文件: ' + error.message);\r\n            \r\n            // 如果有原始文件对象，尝试直接下载\r\n            if (attachment.file) {\r\n              const url = URL.createObjectURL(attachment.file);\r\n              const link = document.createElement('a');\r\n              link.href = url;\r\n              link.download = attachment.name;\r\n              link.click();\r\n              setTimeout(() => URL.revokeObjectURL(url), 100);\r\n            }\r\n          }\r\n        } else {\r\n          // 其他类型 - 尝试下载文件\r\n          try {\r\n            const link = document.createElement('a');\r\n            \r\n            // 检查是否是Blob URL\r\n            if (attachment.data instanceof Blob || attachment.data.startsWith('blob:')) {\r\n              link.href = attachment.data;\r\n            } else if (attachment.file) {\r\n              // 如果有原始文件对象，使用它创建URL\r\n              link.href = URL.createObjectURL(attachment.file);\r\n            } else {\r\n              // 尝试将data URL转换为Blob URL\r\n              const blob = this.dataURLtoBlob(attachment.data);\r\n              link.href = URL.createObjectURL(blob);\r\n            }\r\n            \r\n            link.download = attachment.name;\r\n            link.click();\r\n            \r\n            // 如果创建了Blob URL，需要释放\r\n            if (link.href.startsWith('blob:')) {\r\n              setTimeout(() => URL.revokeObjectURL(link.href), 100);\r\n            }\r\n          } catch (error) {\r\n            console.error('下载文件失败:', error);\r\n            this.$message.error('无法下载文件: ' + error.message);\r\n          }\r\n        }\r\n      } else {\r\n        this.$message.warning('无法预览该附件，附件数据不完整');\r\n      }\r\n    },\r\n    \r\n    // 将Data URL转换为Blob对象\r\n    dataURLtoBlob(dataURL) {\r\n      try {\r\n        // 检查是否是有效的data URL格式\r\n        if (!dataURL || typeof dataURL !== 'string' || !dataURL.includes(';base64,')) {\r\n          console.error('无效的Data URL格式:', dataURL);\r\n          throw new Error('无效的Data URL格式');\r\n        }\r\n        \r\n        // 分割Data URL，获取MIME类型和base64数据\r\n        const parts = dataURL.split(';base64,');\r\n        const contentType = parts[0].split(':')[1];\r\n        const raw = window.atob(parts[1]);\r\n        const rawLength = raw.length;\r\n        const uInt8Array = new Uint8Array(rawLength);\r\n        \r\n        // 将base64数据转换为Uint8Array\r\n        for (let i = 0; i < rawLength; ++i) {\r\n          uInt8Array[i] = raw.charCodeAt(i);\r\n        }\r\n        \r\n        return new Blob([uInt8Array], { type: contentType });\r\n      } catch (error) {\r\n        console.error('转换Data URL到Blob失败:', error);\r\n        throw new Error('转换Data URL到Blob失败: ' + error.message);\r\n      }\r\n    },\r\n    \r\n    // 删除附件\r\n    removeAttachment(index) {\r\n      this.$confirm('确定要删除此附件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 释放URL对象\r\n        if (this.contractForm.attachments[index].data) {\r\n          URL.revokeObjectURL(this.contractForm.attachments[index].data);\r\n        }\r\n        // 从数组中删除\r\n        this.contractForm.attachments.splice(index, 1);\r\n        this.$message.success('附件已删除');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 扫描身份证\r\n    scanIdCard() {\r\n      if (!this.scannerConnected && !this.simulationMode) {\r\n        this.$message.warning('请先启动高拍仪')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        if (this.simulationMode) {\r\n          // 模拟模式下，直接调用OCR接口处理示例图片\r\n          this.processOcrWithSimulationImage()\r\n          return\r\n        }\r\n        \r\n        // 设置身份证自动寻边模式\r\n        this.sendScannerCommand('bSetMode(4)')\r\n        \r\n        // 生成唯一文件名（使用时间戳）\r\n        const timestamp = new Date().getTime()\r\n        const filename = `idcard_${timestamp}`\r\n        this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\r\n        console.log('当前照片路径:', this.currentPhotoPath) \r\n        \r\n        // 拍照并保存到本地\r\n        this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\r\n        \r\n        // 识别条码\r\n        this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)\r\n        \r\n        // 调用OCR识别\r\n        setTimeout(() => {\r\n          this.processOcrWithImage(this.currentPhotoPath)\r\n        }, 1000) // 延迟1秒，确保图片保存完成\r\n        \r\n        this.$message.info('正在识别身份证，请稍候...')\r\n      } catch (error) {\r\n        console.error('扫描身份证失败', error)\r\n        this.$message.error('扫描身份证失败')\r\n      }\r\n    },\r\n\r\n    // 处理OCR识别结果\r\n    processOcrWithImage(imagePath) {\r\n      if (this.isProcessingOcr) {\r\n        return\r\n      }\r\n      \r\n      this.isProcessingOcr = true\r\n      this.$message.info('正在进行OCR识别...')\r\n      \r\n      // 准备表单数据\r\n      const formData = new FormData()\r\n      \r\n      // 判断是否是base64格式的图片数据\r\n      if (imagePath.startsWith('data:image')) {\r\n        // 创建文件对象从base64数据\r\n        const base64Data = imagePath.split(',')[1]\r\n        \r\n        // 检查是否是从 Base64Encode 命令获取的高质量图像数据\r\n        const isHighQualityBase64 = this.currentImageData === imagePath && !this.waitingForBase64;\r\n        \r\n        if (isHighQualityBase64) {\r\n          // 如果是高质量 base64 数据，直接使用，不需要额外处理\r\n          console.log('使用高质量 base64 数据，跳过图像处理')\r\n          \r\n          const byteCharacters = atob(base64Data)\r\n          const byteArrays = []\r\n          \r\n          for (let i = 0; i < byteCharacters.length; i++) {\r\n            byteArrays.push(byteCharacters.charCodeAt(i))\r\n          }\r\n          \r\n          const byteArray = new Uint8Array(byteArrays)\r\n          const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n          \r\n          // 创建文件对象\r\n          const fileName = `contract_${new Date().getTime()}.jpg`\r\n          const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n          \r\n          // 添加到表单\r\n          formData.append('image', file)\r\n          console.log('发送高质量 base64 数据进行 OCR 识别')\r\n          \r\n          // 同时保留 base64 数据作为备用\r\n          formData.append('image_base64', imagePath)\r\n          \r\n          // 调用 OCR API\r\n          this.callOcrApi(formData)\r\n        } else {\r\n          // 在发送前处理图像，去除紫色边框和文字标记\r\n          this.preprocessImage(imagePath).then(processedImageData => {\r\n            // 使用处理后的图像数据\r\n            const processedBase64 = processedImageData.split(',')[1]\r\n            const byteCharacters = atob(processedBase64)\r\n            const byteArrays = []\r\n            \r\n            for (let i = 0; i < byteCharacters.length; i++) {\r\n              byteArrays.push(byteCharacters.charCodeAt(i))\r\n            }\r\n            \r\n            const byteArray = new Uint8Array(byteArrays)\r\n            const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n            \r\n            // 创建文件对象\r\n            const fileName = `contract_${new Date().getTime()}.jpg`\r\n            const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n            \r\n            // 添加到表单\r\n            formData.append('image', file)\r\n            console.log('发送处理后的文件对象进行 OCR 识别')\r\n            \r\n            // 同时保留处理后的base64数据作为备用\r\n            formData.append('image_base64', processedImageData)\r\n            \r\n            // 调用OCR API\r\n            this.callOcrApi(formData)\r\n          }).catch(error => {\r\n            console.error('图像预处理失败，使用原始图像:', error)\r\n            \r\n            // 如果处理失败，使用原始图像\r\n            const byteCharacters = atob(base64Data)\r\n            const byteArrays = []\r\n            \r\n            for (let i = 0; i < byteCharacters.length; i++) {\r\n              byteArrays.push(byteCharacters.charCodeAt(i))\r\n            }\r\n            \r\n            const byteArray = new Uint8Array(byteArrays)\r\n            const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n            \r\n            // 创建文件对象\r\n            const fileName = `contract_${new Date().getTime()}.jpg`\r\n            const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n            \r\n            // 添加到表单\r\n            formData.append('image', file)\r\n            console.log('发送原始文件对象进行 OCR 识别')\r\n            \r\n            // 同时保留base64数据作为备用\r\n            formData.append('image_base64', imagePath)\r\n            \r\n            // 调用OCR API\r\n            this.callOcrApi(formData)\r\n          })\r\n        }\r\n      } else {\r\n        // 如果是文件路径，尝试读取文件并上传\r\n        formData.append('image_path', imagePath)\r\n        console.log(`发送图片路径进行OCR识别: ${imagePath}`)\r\n        \r\n        // 调用OCR API\r\n        this.callOcrApi(formData)\r\n      }\r\n    },\r\n    \r\n    // 调用OCR API\r\n    callOcrApi(formData) {\r\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\r\n        .then(response => {\r\n          this.handleOcrResult(response.data)\r\n        })\r\n        .catch(error => {\r\n          console.error('OCR识别失败', error)\r\n          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\r\n        })\r\n        .finally(() => {\r\n          this.isProcessingOcr = false\r\n        })\r\n    },\r\n    \r\n    // 图像预处理函数 - 去除紫色边框和文字标记\r\n    preprocessImage(base64Image) {\r\n      return new Promise((resolve, reject) => {\r\n        try {\r\n          // 创建图像对象\r\n          const img = new Image()\r\n          img.onload = () => {\r\n            // 创建Canvas\r\n            const canvas = document.createElement('canvas')\r\n            canvas.width = img.width\r\n            canvas.height = img.height\r\n            const ctx = canvas.getContext('2d')\r\n            \r\n            // 绘制图像到Canvas\r\n            ctx.drawImage(img, 0, 0)\r\n            \r\n            // 获取图像数据\r\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n            const data = imageData.data\r\n            \r\n            // 处理紫色边框 - 将紫色像素替换为白色\r\n            for (let i = 0; i < data.length; i += 4) {\r\n              const r = data[i]\r\n              const g = data[i + 1]\r\n              const b = data[i + 2]\r\n              \r\n              // 检测紫色范围 (RGB近似值)\r\n              // 紫色通常R和B较高，G较低\r\n              if ((r > 100 && r < 200) && (g < 100) && (b > 100 && b < 200)) {\r\n                // 将紫色替换为白色\r\n                data[i] = 255     // R\r\n                data[i + 1] = 255 // G\r\n                data[i + 2] = 255 // B\r\n              }\r\n            }\r\n            \r\n            // 将处理后的图像数据放回Canvas\r\n            ctx.putImageData(imageData, 0, 0)\r\n            \r\n            // 尝试去除文字标记 (简化处理)\r\n            // 这里使用简单的边缘检测和阈值处理\r\n            // 获取灰度图像\r\n            const grayImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n            const grayData = grayImageData.data\r\n            \r\n            // 转换为灰度图\r\n            for (let i = 0; i < grayData.length; i += 4) {\r\n              const avg = (grayData[i] + grayData[i + 1] + grayData[i + 2]) / 3\r\n              \r\n              // 应用阈值，将可能的文字区域变白\r\n              if (avg < 180) { // 较暗的区域可能是文字\r\n                // 检查周围像素，如果是小区域的暗像素，可能是文字\r\n                // 这是一个简化的处理，实际应用中可能需要更复杂的算法\r\n                grayData[i] = 255     // R\r\n                grayData[i + 1] = 255 // G\r\n                grayData[i + 2] = 255 // B\r\n              }\r\n            }\r\n            \r\n            // 将处理后的图像数据转换为base64\r\n            const processedBase64 = canvas.toDataURL('image/jpeg', 0.95)\r\n            \r\n            console.log('图像预处理完成')\r\n            resolve(processedBase64)\r\n          }\r\n          \r\n          img.onerror = (error) => {\r\n            console.error('图像加载失败:', error)\r\n            reject(error)\r\n          }\r\n          \r\n          // 设置图像源\r\n          img.src = base64Image\r\n        } catch (error) {\r\n          console.error('图像预处理失败:', error)\r\n          reject(error)\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 模拟模式下使用示例图片进行OCR识别\r\n    processOcrWithSimulationImage() {\r\n      this.isProcessingOcr = true\r\n      this.$message.info('模拟模式：正在进行OCR识别...')\r\n      \r\n      // 准备表单数据\r\n      const formData = new FormData()\r\n      formData.append('simulation', 'true')\r\n      \r\n      // 调用OCR API\r\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\r\n        .then(response => {\r\n          this.handleOcrResult(response.data)\r\n        })\r\n        .catch(error => {\r\n          console.error('模拟OCR识别失败', error)\r\n          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))\r\n        })\r\n        .finally(() => {\r\n          this.isProcessingOcr = false\r\n        })\r\n    },\r\n    \r\n    // 处理OCR识别结果\r\n    handleOcrResult(result) {\r\n      if (!result || !result.success) {\r\n        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))\r\n        return\r\n      }\r\n      \r\n      this.$message.success('OCR识别成功')\r\n      console.log('OCR识别结果:', result)\r\n      \r\n      // 更新表单数据\r\n      const ocrData = result.data || {}\r\n      \r\n      // 更新身份证号\r\n      if (ocrData.id_number) {\r\n        this.contractForm.idCardNumber = ocrData.id_number\r\n        // 添加更明显的提示\r\n        this.$notify({\r\n          title: '证件号码识别成功',\r\n          message: ocrData.id_number,\r\n          type: 'success',\r\n          duration: 5000\r\n        })\r\n        \r\n        // 标记已找到身份证\r\n        this.idCardFound = true\r\n        \r\n        // 如果有身份证号，尝试从系统中查询更多信息\r\n        this.searchWorkerByIdCard(ocrData.id_number)\r\n      }\r\n      \r\n      // 更新姓名\r\n      if (ocrData.name) {\r\n        this.contractForm.workerName = ocrData.name\r\n      }\r\n    },\r\n    \r\n    // 验证是否为身份证号\r\n    isIdCardNumber(str) {\r\n      // 简单验证18位或15位身份证号\r\n      const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\r\n      return reg.test(str)\r\n    },\r\n    \r\n    // 根据身份证号查询工人信息\r\n    searchWorkerByIdCard(idCardNumber) {\r\n      if (!idCardNumber) {\r\n        this.$message.warning('证件号码不能为空')\r\n        return\r\n      }\r\n      \r\n      this.isLoadingWorkerInfo = true\r\n      this.$message.info('正在查询工人信息...')\r\n      \r\n      // 调用API根据身份证号查询工人信息\r\n      getWorkerByIdCard(idCardNumber)\r\n        .then(response => {\r\n          if (response.code === 0 && response.data) {\r\n            const workerData = response.data\r\n            \r\n            // 打印API返回的完整数据，查看是否包含参建单位ID相关字段\r\n            console.log('API返回的工人信息:', workerData)\r\n            \r\n            // 更新表单数据\r\n            this.contractForm.workerId = workerData.workId || ''\r\n            this.contractForm.workerName = workerData.workerName || ''\r\n            \r\n            // 获取并保存班组编号和班组名称\r\n            this.contractForm.teamCode = workerData.teamNo || workerData.teamSysNo || ''\r\n            this.contractForm.teamName = workerData.teamName || workerData.teamSysName || ''\r\n            \r\n            // 填充其他工人信息\r\n            this.contractForm.ProjectName = workerData.projectName || ''\r\n            this.contractForm.projectCode = workerData.projectCode || ''\r\n            \r\n            // 尝试从多个可能的字段中获取参建单位ID\r\n            this.contractForm.projectSubContractorId = \r\n              workerData.projectSubContractorId || \r\n              workerData.corpCode || \r\n              workerData.participantCode || \r\n              workerData.corpId || \r\n              workerData.subContractorId || \r\n              '';\r\n            \r\n            console.log('可能的参建单位ID字段:',\r\n              '1.projectSubContractorId=', workerData.projectSubContractorId,\r\n              '2.corpCode=', workerData.corpCode,\r\n              '3.participantCode=', workerData.participantCode,\r\n              '4.corpId=', workerData.corpId,\r\n              '5.subContractorId=', workerData.subContractorId\r\n            );\r\n            \r\n            this.contractForm.projectSubContractorName = workerData.projectSubContractorName || workerData.corpName || ''\r\n            \r\n            // 打印保存后的projectSubContractorId\r\n            console.log('保存的参建单位ID:', this.contractForm.projectSubContractorId)\r\n            \r\n            this.contractForm.contractPeriodType = workerData.contractPeriodType || '固定期限'\r\n            this.contractForm.contractStartDate = workerData.contractStartDate ? new Date(workerData.contractStartDate) : new Date()\r\n            this.contractForm.contractEndDate = workerData.contractEndDate ? new Date(workerData.contractEndDate) : null\r\n            \r\n            // 处理工资类型 - 将数字转换为文本表示\r\n            console.log('原始工资类型值:', workerData.salaryType)\r\n            this.contractForm.salaryType = this.getSalaryTypeText(workerData.salaryType) || '按天'\r\n            console.log('转换后工资类型:', this.contractForm.salaryType)\r\n            \r\n            this.contractForm.salaryUnitPrice = workerData.salaryUnitPrice || ''\r\n            this.contractForm.salaryPerUnit = workerData.salaryPerUnit || ''\r\n            this.contractForm.salaryUnitType = workerData.salaryUnitType || ''\r\n            \r\n            // 根据工资类型更新验证规则\r\n            this.handleSalaryTypeChange(this.contractForm.salaryType)\r\n            \r\n            if (workerData.contractSignDate) {\r\n              this.contractForm.contractSignDate = new Date(workerData.contractSignDate)\r\n            }\r\n            \r\n            this.$message.success('工人信息查询成功')\r\n            \r\n            // 添加更详细的通知\r\n            this.$notify({\r\n              title: '工人信息查询成功',\r\n              message: `已找到工人: ${workerData.workerName}${this.contractForm.teamName ? ', 班组: ' + this.contractForm.teamName : ''}`,\r\n              type: 'success',\r\n              duration: 5000\r\n            })\r\n          } else {\r\n            this.$message.warning(response.msg || '未找到工人信息')\r\n            \r\n            // 如果没有找到，可以保留一些基本信息\r\n            if (this.contractForm.idCardNumber && !this.contractForm.workerName) {\r\n              // 从身份证号提取出生日期和性别信息\r\n              this.extractInfoFromIdCard(idCardNumber)\r\n            }\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error('查询工人信息失败:', error)\r\n          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))\r\n          \r\n          // 如果API调用失败，可以尝试从身份证号提取一些基本信息\r\n          if (this.contractForm.idCardNumber) {\r\n            this.extractInfoFromIdCard(idCardNumber)\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.isLoadingWorkerInfo = false\r\n        })\r\n    },\r\n    \r\n    // 从身份证号提取信息\r\n    extractInfoFromIdCard(idCardNumber) {\r\n      if (!idCardNumber || idCardNumber.length < 18) return\r\n      \r\n      try {\r\n        // 提取性别 (第17位，奇数为男，偶数为女)\r\n        const genderCode = parseInt(idCardNumber.charAt(16))\r\n        const gender = genderCode % 2 === 1 ? '男' : '女'\r\n        \r\n        console.log('从身份证号提取的性别:', gender)\r\n      } catch (e) {\r\n        console.error('从身份证号提取信息失败:', e)\r\n      }\r\n    },\r\n    \r\n    // 解析身份证号码 (示例，实际需要更复杂的OCR库)\r\n    parseIdCardNumber(base64Image) {\r\n      // 这是一个非常简化的示例，实际需要使用专业的OCR库（如Tesseract.js, PaddleOCR等）\r\n      // 这里只是模拟一个简单的解析逻辑\r\n      try {\r\n        const img = new Image();\r\n        img.src = 'data:image/jpeg;base64,' + base64Image;\r\n        img.onload = () => {\r\n          // 在实际应用中，这里会调用OCR库进行识别\r\n          // 例如：PaddleOCR.recognizeText(img.src);\r\n          // 假设识别结果包含身份证号码\r\n          const mockIdCardNumber = '123456789012345678'; // 模拟身份证号码\r\n          return mockIdCardNumber;\r\n        };\r\n      } catch (e) {\r\n        console.error('图片解析失败', e);\r\n        return null;\r\n      }\r\n    },\r\n\r\n    // 获取员工信息\r\n    fetchWorkerInfo(idCardNumber) {\r\n      // 这是一个模拟的API调用，实际需要一个真实的后端接口\r\n      // 例如：axios.get(`${this.apiBaseUrl}/api/workers/idCard/${idCardNumber}`)\r\n      // 假设成功获取到工人信息\r\n      const mockWorker = {\r\n        workerId: '', // 模拟工人ID\r\n        workerName: '', // 模拟工人姓名\r\n        idCardNumber: idCardNumber,\r\n        contractType: '',\r\n        contractSignDate: new Date(),\r\n        remark: ''\r\n      };\r\n\r\n      this.contractForm = { ...mockWorker }; // 更新合同表单\r\n      this.$message.success('已获取到工人信息！');\r\n    },\r\n\r\n    // 删除照片\r\n    removePhoto(index) {\r\n      this.$confirm('确定要删除此照片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.photoList.splice(index, 1);\r\n        this.$message.success('照片已删除');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 刷新工人信息\r\n    refreshWorkerInfo() {\r\n      if (!this.contractForm.idCardNumber) {\r\n        this.$message.warning('请先输入证件号码')\r\n        return\r\n      }\r\n      \r\n      // 重新获取工人信息\r\n      this.searchWorkerByIdCard(this.contractForm.idCardNumber)\r\n    },\r\n\r\n    // 获取计量单位标签\r\n    getUnitTypeLabel(value) {\r\n      const unitTypeMap = {\r\n        '80': '米',\r\n        '81': '平方米',\r\n        '82': '立方米'\r\n      };\r\n      return unitTypeMap[value] || '单位';\r\n    },\r\n    \r\n    // 将图片转换为PDF\r\n    convertImagesToPdf() {\r\n      if (this.photoList.length === 0) {\r\n        this.$message.warning('请先拍摄或上传合同图片');\r\n        return;\r\n      }\r\n\r\n      this.isGeneratingPDF = true;\r\n      this.$message.info('正在生成PDF，请稍候...');\r\n\r\n      // 创建一个新的PDF文档\r\n      const pdf = new jsPDF('p', 'mm', 'a4');\r\n      const promises = [];\r\n      const pageWidth = pdf.internal.pageSize.getWidth();\r\n      const pageHeight = pdf.internal.pageSize.getHeight();\r\n\r\n      // 为每张图片创建一个Promise\r\n      this.photoList.forEach((photo, index) => {\r\n        const promise = new Promise((resolve) => {\r\n          // 优先使用高质量的 base64 图像数据\r\n          if (photo.imageData && photo.imageData.startsWith('data:image')) {\r\n            // 创建一个临时的图片元素\r\n            const img = new Image();\r\n            img.src = photo.imageData;\r\n            \r\n            img.onload = () => {\r\n              // 计算图片在PDF中的尺寸，保持宽高比\r\n              let imgWidth = pageWidth - 20; // 留出10mm边距\r\n              let imgHeight = (img.height * imgWidth) / img.width;\r\n              \r\n              // 如果图片高度超过页面高度，按高度缩放\r\n              if (imgHeight > pageHeight - 20) {\r\n                imgHeight = pageHeight - 20;\r\n                imgWidth = (img.width * imgHeight) / img.height;\r\n              }\r\n              \r\n              // 如果不是第一页，添加新页\r\n              if (index > 0) {\r\n                pdf.addPage();\r\n              }\r\n              \r\n              // 将图片添加到PDF\r\n              pdf.addImage(\r\n                photo.imageData, \r\n                'JPEG', \r\n                (pageWidth - imgWidth) / 2, // 居中显示\r\n                10, // 顶部边距\r\n                imgWidth, \r\n                imgHeight\r\n              );\r\n              \r\n              resolve();\r\n            };\r\n            \r\n            img.onerror = () => {\r\n              this.$message.error(`处理第${index + 1}张图片时出错`);\r\n              resolve(); // 即使出错也继续处理其他图片\r\n            };\r\n          } else if (photo.path && photo.path.startsWith('C:\\\\')) {\r\n            // 如果没有图像数据但有本地路径，尝试获取本地图片的 base64 数据\r\n            console.log(`尝试获取本地图片的 base64 数据: ${photo.path}`);\r\n            \r\n            // 使用 Base64Encode 命令获取图片的 base64 数据\r\n            if (this.scannerConnected) {\r\n              this.sendScannerCommand(`Base64Encode(${photo.path})`);\r\n              \r\n              // 设置一个超时，如果在指定时间内没有收到响应，则跳过该图片\r\n              setTimeout(() => {\r\n                if (!photo.imageData || !photo.imageData.startsWith('data:image')) {\r\n                  this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);\r\n                  resolve();\r\n                }\r\n              }, 3000);\r\n            } else {\r\n              this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);\r\n              resolve();\r\n            }\r\n          } else {\r\n            // 如果既没有图像数据也没有本地路径，跳过该图片\r\n            this.$message.warning(`第${index + 1}张图片没有有效数据，将跳过`);\r\n            resolve();\r\n          }\r\n        });\r\n        \r\n        promises.push(promise);\r\n      });\r\n\r\n      // 当所有图片处理完成后，保存PDF\r\n      Promise.all(promises).then(() => {\r\n        // 生成文件名\r\n        const workerName = this.contractForm.workerName || '未命名';\r\n        const idCardNumber = this.contractForm.idCardNumber || '';\r\n        const timestamp = new Date().getTime();\r\n        const fileName = `${workerName}_${idCardNumber}_合同_${timestamp}.pdf`;\r\n        \r\n        // 获取PDF文件的Blob对象\r\n        const pdfBlob = pdf.output('blob');\r\n        \r\n        // 创建附件对象\r\n        const blobUrl = URL.createObjectURL(pdfBlob);\r\n        const attachment = {\r\n          name: fileName,\r\n          type: 'application/pdf',\r\n          size: pdfBlob.size,\r\n          data: blobUrl,\r\n          file: new File([pdfBlob], fileName, { type: 'application/pdf' })\r\n        };\r\n        \r\n        // 添加到合同附件数组\r\n        this.contractForm.attachments.push(attachment);\r\n        \r\n        // 将生成的PDF设置为合同文件\r\n        this.contractForm.contractFile = attachment;\r\n        \r\n        // 获取PDF的base64数据，用于上传API\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(pdfBlob);\r\n        reader.onloadend = () => {\r\n          const base64data = reader.result;\r\n          // 保存base64数据到附件对象，便于后续上传\r\n          attachment.base64 = base64data;\r\n        };\r\n        \r\n        this.$message.success('PDF生成成功并已添加到合同附件');\r\n        this.isGeneratingPDF = false;\r\n      }).catch(error => {\r\n        console.error('生成PDF失败:', error);\r\n        this.$message.error('生成PDF失败: ' + (error.message || '未知错误'));\r\n        this.isGeneratingPDF = false;\r\n      });\r\n    },\r\n\r\n    // 手动上传附件\r\n    handleAttachmentUpload(file) {\r\n      // Element UI 的 upload 组件传入的是一个包含文件信息的对象\r\n      // 需要从中获取实际的文件对象\r\n      const actualFile = file.raw || file;\r\n      \r\n      if (actualFile) {\r\n        const isLt10M = actualFile.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n          this.$message.error('文件大小不能超过10MB!');\r\n          return false;\r\n        }\r\n\r\n        // 显示上传中提示\r\n        this.$message.info('正在处理文件，请稍候...');\r\n\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(actualFile);\r\n        reader.onload = (e) => {\r\n          // 获取文件类型图标\r\n          const fileIcon = this.getFileTypeIcon(actualFile.type);\r\n          \r\n          let fileData = e.target.result;\r\n          \r\n          // 确保fileData是有效的data URL格式\r\n          if (!fileData || typeof fileData !== 'string' || !fileData.includes(';base64,')) {\r\n            console.warn('文件数据不是有效的data URL格式，将使用Blob URL代替');\r\n            fileData = URL.createObjectURL(actualFile);\r\n          }\r\n          \r\n          // 创建附件对象\r\n          const attachment = {\r\n            name: actualFile.name,\r\n            type: actualFile.type,\r\n            size: actualFile.size,\r\n            data: fileData,\r\n            file: actualFile,\r\n            icon: fileIcon\r\n          };\r\n          \r\n          // 添加到附件列表\r\n          this.contractForm.attachments.push(attachment);\r\n          \r\n          // 如果是PDF，自动设置为合同文件\r\n          if (actualFile.type === 'application/pdf' && !this.contractForm.contractFile) {\r\n            this.contractForm.contractFile = attachment;\r\n            this.$message.success('已自动设置为合同文件');\r\n          } else {\r\n            this.$message.success('附件上传成功');\r\n          }\r\n        };\r\n        reader.onerror = (error) => {\r\n          console.error('读取文件失败', error);\r\n          this.$message.error('读取文件失败');\r\n        };\r\n      }\r\n      return false; // 阻止默认的上传行为\r\n    },\r\n\r\n    // 根据文件类型获取图标\r\n    getFileTypeIcon(fileType) {\r\n      if (fileType.includes('image/')) {\r\n        return 'el-icon-picture';\r\n      } else if (fileType === 'application/pdf') {\r\n        return 'el-icon-document';\r\n      } else if (fileType.includes('word') || fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\r\n        return 'el-icon-document-checked';\r\n      } else if (fileType.includes('excel') || fileType === 'application/vnd.ms-excel' || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {\r\n        return 'el-icon-tickets';\r\n      } else {\r\n        return 'el-icon-document';\r\n      }\r\n    },\r\n\r\n    // 手动上传附件前的验证\r\n    beforeAttachmentUpload(file) {\r\n      // 验证文件大小（限制为10MB）\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!');\r\n        return false;\r\n      }\r\n      \r\n      // 验证文件类型\r\n      const allowedTypes = [\r\n        'application/pdf', \r\n        'application/msword', \r\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n        'application/vnd.ms-excel',\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        'image/jpeg',\r\n        'image/png',\r\n        'image/gif'\r\n      ];\r\n      \r\n      const isAllowedType = allowedTypes.includes(file.type) || file.type.startsWith('image/');\r\n      if (!isAllowedType) {\r\n        this.$message.error('只支持PDF、Word、Excel和图片格式!');\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contract-upload-container {\r\n  padding: 20px;\r\n}\r\n\r\n.contract-upload-container h3 {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  color: #303133;\r\n}\r\n\r\n.scanner-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.scanner-layout {\r\n  display: flex;\r\n  gap: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.scanner-left, .scanner-right {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.scanner-right {\r\n  border-left: 1px solid #ebeef5;\r\n  padding-left: 20px;\r\n  max-height: 700px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.scanner-preview {\r\n  width: 100%;\r\n  height: 400px;\r\n  margin-bottom: 20px;\r\n  border: 1px solid #dcdfe6;\r\n  background-color: #ebeef5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: hidden;\r\n}\r\n\r\n.scanner-preview img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n.scanner-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-button {\r\n  display: inline-block;\r\n  margin-left: 10px;\r\n}\r\n\r\n.scanner-right h4 {\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.photo-actions-top {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.photo-items {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.photo-item {\r\n  width: 180px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.photo-thumbnail {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n}\r\n\r\n.photo-actions {\r\n  padding: 8px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.contract-info-form {\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.id-card-input {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.id-card-input .el-input {\r\n  flex: 1;\r\n}\r\n\r\n.refresh-button {\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.contract-attachments {\r\n  margin-top: 20px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.contract-attachments h4 {\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #ebeef5;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.attachment-actions {\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100px;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  cursor: pointer;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger .el-icon-upload {\r\n  font-size: 24px;\r\n  color: #8c939d;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-attachment .el-upload__tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.no-attachments {\r\n  text-align: center;\r\n  color: #909399;\r\n  padding: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 20px;\r\n  color: #606266;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px; /* Adjust as needed */\r\n}\r\n\r\n/* 图片预览对话框样式已合并到PDF预览对话框样式中 */\r\n\r\n/* PDF预览对话框样式 */\r\n.pdf-preview-dialog {\r\n  width: 95% !important;\r\n  height: 95vh !important;\r\n  margin: 0 auto !important;\r\n  max-width: none !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box {\r\n  width: 95% !important;\r\n  max-width: none !important;\r\n  margin: 0 auto;\r\n  height: 95vh !important;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: fixed;\r\n  top: 2vh;\r\n  left: 2.5%;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__header {\r\n  padding: 10px 20px !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__content {\r\n  padding: 0 !important;\r\n  flex: 1;\r\n  overflow: hidden;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: calc(95vh - 55px) !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__message {\r\n  height: 100% !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__message p {\r\n  height: 100% !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.pdf-preview-dialog object {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n\r\n/* 为了确保遮罩层正确显示 */\r\n.v-modal {\r\n  opacity: 0.6 !important;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .scanner-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .scanner-right {\r\n    border-left: none;\r\n    border-top: 1px solid #ebeef5;\r\n    padding-left: 0;\r\n    padding-top: 20px;\r\n    margin-top: 20px;\r\n  }\r\n  \r\n  .scanner-preview {\r\n    width: 100%;\r\n    height: 300px;\r\n  }\r\n  \r\n  .scanner-controls {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .upload-button {\r\n    margin-left: 0;\r\n    margin-top: 10px;\r\n  }\r\n  \r\n  .id-card-input {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .refresh-button {\r\n    margin-left: 0;\r\n    margin-top: 10px;\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}