-- 添加isShow字段到基础任务表
ALTER TABLE `sys_basic_task` ADD COLUMN `is_show` tinyint(1) DEFAULT 1 COMMENT '是否显示：1-显示，0-隐藏（用于控制在定时任务页面是否显示）' AFTER `status`;

-- 更新特定任务的isShow字段
UPDATE `sys_basic_task` SET `is_show` = 0 WHERE `type` IN ('SYNC_ROSTER', 'SYNC_ATTENDANCE', 'SYNC_ATTENDANCE_RATE');

-- 添加同步考勤率任务
INSERT INTO `sys_basic_task` (`id`, `name`, `code`, `type`, `description`, `status`, `is_show`, `create_time`, `update_time`, `handler_class`) 
VALUES (UUID(), '同步考勤率', 'SYNC_ATTENDANCE_RATE', 'SYNC_ATTENDANCE_RATE', '同步项目考勤率数据', 1, 0, NOW(), NOW(), 'com.daka.pro.task.handler.SyncAttendanceRateHandler')
ON DUPLICATE KEY UPDATE `name` = '同步考勤率', `description` = '同步项目考勤率数据', `status` = 1, `is_show` = 0, `update_time` = NOW(), `handler_class` = 'com.daka.pro.task.handler.SyncAttendanceRateHandler'; 