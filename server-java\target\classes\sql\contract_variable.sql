-- 创建合同变量表
CREATE TABLE IF NOT EXISTS `contract_variable` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '变量名称',
  `description` varchar(255) NOT NULL COMMENT '变量描述',
  `example` varchar(255) NOT NULL COMMENT '示例值',
  `category` varchar(50) NOT NULL COMMENT '变量分类',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同变量表';

-- 初始化一些基础变量数据
INSERT INTO `contract_variable` (`id`, `name`, `description`, `example`, `category`, `create_time`, `update_time`, `is_deleted`)
VALUES
  (REPLACE(UUID(),'-',''), '甲方名称', '甲方公司名称', 'XX建筑有限公司', '通用', NOW(), NOW(), 0),
  (REPLACE(UUID(),'-',''), '乙方姓名', '乙方个人姓名', '张三', '人员', NOW(), NOW(), 0),
  (REPLACE(UUID(),'-',''), '签约日期', '合同签约日期', '2023年6月1日', '日期', NOW(), NOW(), 0),
  (REPLACE(UUID(),'-',''), '项目名称', '工程项目名称', 'XX工程项目', '项目', NOW(), NOW(), 0),
  (REPLACE(UUID(),'-',''), '合同编号', '合同唯一编号', 'HT-2023-001', '通用', NOW(), NOW(), 0); 