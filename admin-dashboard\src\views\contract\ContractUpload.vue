<template>
  <div class="contract-upload-container">
    <h3>合同上传</h3>
    
    <div class="scanner-container">
      <div class="scanner-layout">
        <!-- 左侧：高拍仪界面 -->
        <div class="scanner-left">
          <div class="scanner-preview">
            <img id="photo" src="" width="600" height="400" ref="scannerPreview">
          </div>
          
          <div class="scanner-controls">
            <el-button type="primary" @click="startScanner">启动高拍仪</el-button>
            <el-button type="success" @click="scanContract">拍摄合同</el-button>
            <el-button type="warning" @click="stopScanner">停止高拍仪</el-button>
            <el-upload
              class="upload-button"
              action="#"
              :show-file-list="false"
              :before-upload="handleUploadImage">
              <el-button type="primary" icon="el-icon-upload">上传图片</el-button>
            </el-upload>
          </div>
        </div>
        
        <!-- 右侧：已拍摄照片 -->
        <div class="scanner-right" v-if="photoList.length > 0">
          <h4>已拍摄照片 ({{ photoList.length }})</h4>
          <div class="photo-actions-top">
            <el-button type="primary" @click="convertImagesToPdf" :loading="isGeneratingPDF" icon="el-icon-document">
              生成PDF
            </el-button>
          </div>
          <div class="photo-items">
            <div v-for="(photo, index) in photoList" :key="index" class="photo-item">
              <img :src="photo.imageData" alt="拍摄照片" class="photo-thumbnail">
              <div class="photo-actions">
                <el-button type="danger" size="mini" icon="el-icon-delete" @click="removePhoto(index)">删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="contract-info-form">
      <el-form :model="contractForm" label-width="120px" ref="contractForm" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工人姓名" prop="workerName">
              <el-input v-model="contractForm.workerName" placeholder="请输入工人姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="idCardNumber">
              <div class="id-card-input">
                <el-input v-model="contractForm.idCardNumber" placeholder="请输入证件号码"></el-input>
                <el-button type="primary" icon="el-icon-refresh" class="refresh-button" @click="refreshWorkerInfo" :loading="isLoadingWorkerInfo">刷新</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="ProjectName">
              <el-input v-model="contractForm.ProjectName" placeholder="项目名称" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目编号" prop="projectCode">
              <el-input v-model="contractForm.projectCode" placeholder="项目编号" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="参建单位" prop="projectSubContractorName">
              <el-input v-model="contractForm.projectSubContractorName" placeholder="请输入参建单位"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签订日期" prop="contractSignDate">
              <el-date-picker
                v-model="contractForm.contractSignDate"
                type="date"
                placeholder="选择日期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同期限类型" prop="contractPeriodType">
              <el-select v-model="contractForm.contractPeriodType" placeholder="请选择合同期限类型" style="width: 100%">
                <el-option label="固定期限" value="固定期限"></el-option>
                <el-option label="无固定期限" value="无固定期限"></el-option>
                <el-option label="以完成一定工作任务为期限" value="以完成一定工作任务为期限"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同开始日期" prop="contractStartDate">
              <el-date-picker
                v-model="contractForm.contractStartDate"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同结束日期" prop="contractEndDate">
              <el-date-picker
                v-model="contractForm.contractEndDate"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工资类型" prop="salaryType">
              <el-select v-model="contractForm.salaryType" placeholder="请选择工资类型" style="width: 100%" @change="handleSalaryTypeChange">
                <el-option label="按天" value="按天"></el-option>
                <el-option label="按月" value="按月"></el-option>
                <el-option label="按工程量" value="按工程量"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="contractForm.remark" placeholder="请输入备注信息"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 根据工资类型显示不同的字段 -->
        <el-row :gutter="20" v-if="['按天', '按月'].includes(contractForm.salaryType)">
          <el-col :span="12">
            <el-form-item label="计量单价" prop="salaryUnitPrice">
              <el-input v-model="contractForm.salaryUnitPrice" placeholder="请输入计量单价">
                <template slot="append">元/{{contractForm.salaryType === '按天' ? '天' : '月'}}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" v-if="contractForm.salaryType === '按工程量'">
          <el-col :span="8">
            <el-form-item label="每(工程数量)" prop="salaryPerUnit">
              <el-input v-model="contractForm.salaryPerUnit" placeholder="请输入工程数量"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计量单位" prop="salaryUnitType">
              <el-select v-model="contractForm.salaryUnitType" placeholder="请选择计量单位" style="width: 100%">
                <el-option label="米" value="80"></el-option>
                <el-option label="平方米" value="81"></el-option>
                <el-option label="立方米" value="82"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计量单价" prop="salaryUnitPrice">
              <el-input v-model="contractForm.salaryUnitPrice" placeholder="请输入计量单价">
                <template slot="append">元/{{getUnitTypeLabel(contractForm.salaryUnitType) || '单位'}}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 显示合同附件 -->
      <div class="contract-attachments">
        <h4>合同附件</h4>
        
        <!-- 添加手动上传附件按钮 -->
        <div class="attachment-actions">
          <el-upload
            class="upload-attachment"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleAttachmentUpload"
            :before-upload="beforeAttachmentUpload"
            :multiple="false">
            <el-button size="small" type="primary" icon="el-icon-upload">上传附件</el-button>
            <div slot="tip" class="el-upload__tip">支持PDF、Word、Excel、图片等格式文件，单个文件不超过10MB</div>
          </el-upload>
        </div>
        
        <el-table v-if="contractForm.attachments.length > 0" :data="contractForm.attachments" style="width: 100%">
          <el-table-column label="文件名" min-width="200">
            <template slot-scope="scope">
              <div class="file-info">
                <i :class="scope.row.icon || 'el-icon-document'" class="file-icon"></i>
                <span class="file-name">{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="150"></el-table-column>
          <el-table-column prop="size" label="大小" width="100">
            <template slot-scope="scope">
              {{ (scope.row.size / 1024).toFixed(2) }} KB
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                type="primary" 
                icon="el-icon-view"
                @click="previewAttachment(scope.row)">预览</el-button>
              <el-button 
                size="mini" 
                type="danger" 
                icon="el-icon-delete"
                @click="removeAttachment(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-else class="no-attachments">
          暂无附件
        </div>
      </div>
      
      <div class="form-actions">
        <el-button type="primary" @click="submitContract">提交合同</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { saveContractFile, getWorkerByIdCard, uploadContract } from '@/api/roster'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export default {
  name: 'ContractUpload',
  data() {
    return {
      webSocket: null,
      scannerConnected: false,
      contractForm: {
        workerId: '',
        workerName: '',
        idCardNumber: '',
        ProjectName: '', // 项目名称
        projectCode: '', // 项目编号
        projectSubContractorId: '', // 参建单位ID
        projectSubContractorName: '', // 参建单位
        teamCode: '', // 班组编号
        teamName: '', // 班组名称
        contractPeriodType: '固定期限', // 合同期限类型
        contractStartDate: new Date(), // 合同开始日期
        contractEndDate: null, // 合同结束日期
        contractSignDate: new Date(),
        salaryType: '按天', // 工资类型
        salaryUnitPrice: '', // 工资单价
        salaryPerUnit: '', // 每(工程数量)
        salaryUnitType: '', // 计量单位
        remark: '',
        contractFile: null,
        attachments: [] // 合同附件数组
      },
      rules: {
        contractSignDate: [
          { required: true, message: '请选择签订日期', trigger: 'change' }
        ],
        workerName: [
          { required: true, message: '请输入工人姓名', trigger: 'blur' }
        ],
        idCardNumber: [
          { required: true, message: '请输入证件号码', trigger: 'blur' }
        ],
        projectSubContractorName: [
          { required: true, message: '请输入参建单位', trigger: 'blur' }
        ],
        contractPeriodType: [
          { required: true, message: '请选择合同期限类型', trigger: 'change' }
        ],
        contractStartDate: [
          { required: true, message: '请选择合同开始日期', trigger: 'change' }
        ],
        salaryType: [
          { required: true, message: '请选择工资类型', trigger: 'change' }
        ],
        salaryUnitPrice: [
          { required: true, message: '请输入计量单价', trigger: 'blur' }
        ]
      },
      simulationMode: false, // 模拟模式标志
      connectionTimeout: null, // 连接超时
      manuallyDisconnected: false, // 手动断开标志
      scannerConfig: {
        wsUrl: 'ws://localhost:1818', // WebSocket连接地址
        timeout: 3000, // 连接超时时间(毫秒)
        autoSimulate: true, // 连接失败时是否自动切换到模拟模式
        ocrApiUrl: 'http://127.0.0.1:5000/ocr' // OCR API地址
      },
      configDialogVisible: false, // 高拍仪配置对话框可见性
      tempScannerConfig: { // 临时存储的高拍仪配置
        wsUrl: 'ws://localhost:1818',
        timeout: 3000,
        autoSimulate: true,
        ocrApiUrl: 'http://127.0.0.1:5000/ocr'
      },
      currentPhotoPath: '', // 当前拍摄的照片路径
      isProcessingUpload: false, // 是否正在处理上传
      isProcessingOcr: false, // 是否正在处理OCR
      photoList: [], // 拍摄的照片列表
      idCardFound: false, // 是否已识别到身份证
      isLoadingWorkerInfo: false, // 是否正在加载工人信息
      isGeneratingPDF: false, // 是否正在生成PDF
      currentImageData: '', // 当前接收到的图像数据
      waitingForBase64: false // 是否正在等待 Base64Encode 的响应
    }
  },
  created() {
    // 从路由参数获取工人信息
    const { workerId, workerName, idCardNumber } = this.$route.query
    if (workerId && workerName && idCardNumber) {
      this.contractForm.workerId = workerId
      this.contractForm.workerName = workerName
      this.contractForm.idCardNumber = idCardNumber
    }
  },
  mounted() {
    // 初始化高拍仪WebSocket连接
    this.initScannerWebSocket()
  },
  beforeDestroy() {
    // 组件销毁前关闭WebSocket连接
    this.closeWebSocket()
  },
  methods: {
    // 处理工资类型变更
    handleSalaryTypeChange(value) {
      // 重置相关字段
      this.contractForm.salaryUnitPrice = '';
      
      if (value === '按工程量') {
        this.contractForm.salaryPerUnit = '';
        this.contractForm.salaryUnitType = '';
        
        // 动态添加验证规则
        this.$set(this.rules, 'salaryPerUnit', [
          { required: true, message: '请输入工程数量', trigger: 'blur' }
        ]);
        this.$set(this.rules, 'salaryUnitType', [
          { required: true, message: '请选择计量单位', trigger: 'change' }
        ]);
      } else {
        // 移除验证规则
        this.$delete(this.rules, 'salaryPerUnit');
        this.$delete(this.rules, 'salaryUnitType');
      }
    },
    
    // 初始化高拍仪WebSocket连接
    initScannerWebSocket() {
      try {
        // 添加模拟模式标志
        this.simulationMode = false;
        
        // 尝试连接WebSocket
        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)
        
        // 设置连接超时
        this.connectionTimeout = setTimeout(() => {
          if (!this.scannerConnected) {
            console.warn('高拍仪连接超时，切换到模拟模式');
            if (this.scannerConfig.autoSimulate) {
              this.switchToSimulationMode();
            } else {
              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);
            }
          }
        }, this.scannerConfig.timeout);
        
        this.webSocket.onopen = (event) => {
          console.log('高拍仪WebSocket连接成功')
          this.scannerConnected = true
          this.$message.success('高拍仪连接成功')
          clearTimeout(this.connectionTimeout);
        }
        
        this.webSocket.onclose = (event) => {
          console.log('高拍仪WebSocket连接关闭')
          this.scannerConnected = false
          // 如果不是主动关闭，尝试切换到模拟模式
          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {
            this.switchToSimulationMode();
          }
        }
        
        this.webSocket.onerror = (event) => {
          console.error('高拍仪WebSocket连接错误', event)
          this.scannerConnected = false
          if (this.scannerConfig.autoSimulate) {
            this.switchToSimulationMode();
          } else {
            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);
          }
        }
        
        this.webSocket.onmessage = (event) => {
          this.handleScannerMessage(event)
        }
      } catch (error) {
        console.error('初始化高拍仪WebSocket失败', error)
        if (this.scannerConfig.autoSimulate) {
          this.switchToSimulationMode();
        } else {
          this.$message.error(`初始化高拍仪失败: ${error.message}`);
        }
      }
    },
    
    // 处理高拍仪消息
    handleScannerMessage(event) {
      const begin_data = "data:image/jpeg;base64,"
      
      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {
        // 处理条码识别结果
        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')
        this.$message.success('识别到条码: ' + barcode)
        
        // 如果是身份证号格式，填入表单
        if (this.isIdCardNumber(barcode)) {
          this.contractForm.idCardNumber = barcode
          this.searchWorkerByIdCard(barcode)
        }
      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {
        // 处理保存图片结果
        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')
        this.$message.success('图片保存成功: ' + result)
      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {
        // 处理 Base64Encode 命令返回的 base64 数据
        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')
        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)
        
        // 重置等待标志
        this.waitingForBase64 = false
        
        if (base64Data && base64Data.length > 1000) { // 确保数据有效
          // 保存 base64 数据
          this.currentImageData = begin_data + base64Data
          
          // 添加到照片列表
          this.photoList.push({
            imageData: this.currentImageData,
            path: this.currentPhotoPath,
            timestamp: new Date().getTime()
          })
          
          // 使用高质量 base64 数据进行 OCR 识别
          console.log('使用高质量 base64 数据进行 OCR 识别')
          this.$message.info('正在识别证件号码...')
          this.processOcrWithImage(this.currentImageData)
        } else {
          console.error('获取到的 base64 数据无效或太短')
          
          // 如果 base64 数据无效，尝试使用预览图或文件路径
          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && 
              this.$refs.scannerPreview.src.startsWith('data:image')) {
            console.log('使用预览图数据进行 OCR 识别')
            const imageData = this.$refs.scannerPreview.src
            
            // 添加到照片列表
            this.photoList.push({
              imageData: imageData,
              path: this.currentPhotoPath,
              timestamp: new Date().getTime()
            })
            
            this.processOcrWithImage(imageData)
          } else {
            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)
            
            // 添加到照片列表（使用空图像数据）
            this.photoList.push({
              imageData: '',
              path: this.currentPhotoPath,
              timestamp: new Date().getTime()
            })
            
            this.processOcrWithImage(this.currentPhotoPath)
          }
        }
      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {
        // 处理目录检查结果
        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')
        console.log('目录检查结果:', result)
        // 如果目录不存在，结果为"0"，存在则为"1"
        if (result === "0") {
          console.log('C:\\pic\\ 目录不存在，将创建')
        }
      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {
        // 处理创建目录结果
        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')
        console.log('创建目录结果:', result)
        // 如果创建成功，结果为"1"，失败则为"0"
        if (result === "1") {
          console.log('C:\\pic\\ 目录创建成功')
        } else {
          console.warn('C:\\pic\\ 目录创建失败或已存在')
        }
      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {
        // 处理条码识别命令响应，不作为图像数据处理
        console.log('收到条码识别命令响应:', event.data)
      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {
        // 处理图像数据 - 判断是否为base64图像数据
        // 增加额外检查，确保不是条码数据
        if (this.$refs.scannerPreview) {
          try {
            // 尝试验证是否为有效的base64图像数据
            const testData = event.data.substring(0, 100); // 只取前100个字符测试
            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常
            
            // 确保是完整的base64数据
            const imgData = begin_data + event.data
            this.$refs.scannerPreview.src = imgData
            
            // 保存当前图像数据以备后用
            this.currentImageData = imgData
            
            // 保存当前图像数据到合同文件
            this.contractForm.contractFile = imgData
            console.log('成功保存图像数据，长度:', event.data.length)
          } catch (e) {
            console.error('收到的数据不是有效的base64图像:', e)
          }
        }
      } else {
        // 其他消息，可能是普通文本或命令响应
        console.log('收到高拍仪消息:', event.data)
      }
    },
    
    // 启动高拍仪
    startScanner() {
      if (!this.scannerConnected) {
        this.initScannerWebSocket()
        return
      }
      
      try {
        // 设置分辨率
        this.sendScannerCommand('vSetResolution(8)')
        // 启用去黑边功能
        this.sendScannerCommand('vSetDelHBFlag(true)')
        // 启动主摄像头
        this.sendScannerCommand('bStartPlay()')
        this.$message.success('高拍仪已启动')
      } catch (error) {
        console.error('启动高拍仪失败', error)
        this.$message.error('启动高拍仪失败')
      }
    },
    
    // 停止高拍仪
    stopScanner() {
      if (!this.scannerConnected) {
        return
      }
      
      try {
        this.sendScannerCommand('bStopPlay()')
        this.$message.success('高拍仪已停止')
      } catch (error) {
        console.error('停止高拍仪失败', error)
        this.$message.error('停止高拍仪失败')
      }
    },
    
    // 扫描合同
    scanContract() {
      if (!this.scannerConnected && !this.simulationMode) {
        this.$message.warning('请先启动高拍仪')
        return
      }
      
      try {
        if (this.simulationMode) {
          // 模拟模式下，直接使用示例图片
          this.processWithSimulationImage()
          // 自动进行OCR识别
          setTimeout(() => {
            this.processOcrWithSimulationImage()
          }, 1000)
          return
        }
        
        // 设置更高的分辨率 - 使用最高分辨率以确保合同完整清晰
        // 分辨率值: 1=320*240, 2=640*480, 3=800*600, 4=1024*768, 5=1280*1024, 6=1600*1200, 7=2048*1536, 8=2592*1944
        this.sendScannerCommand('vSetResolution(8)')
        
        // 确保启用去黑边功能
        this.sendScannerCommand('vSetDelHBFlag(true)')
        
        // 设置A4文档模式 - 使用文档模式而不是证件模式
        //this.sendScannerCommand('bSetMode(2)')
        
        // 设置自动裁剪模式 - 确保合同完整捕获
        this.sendScannerCommand('vSetAutoCrop(true)')
        
        // 设置图像增强 - 提高清晰度
        this.sendScannerCommand('vSetImageEnhance(true)')
        
        // 先检查目录是否存在，不存在则创建
        this.sendScannerCommand('bDirIsExist(C:\\pic\\)')
        
        // 延迟一下，确保目录检查完成
        setTimeout(() => {
          // 创建目录（即使目录已存在，这个命令也不会报错）
          this.sendScannerCommand('bCreateDir(C:\\pic\\)')
          
          // 生成唯一文件名（使用时间戳）
          const timestamp = new Date().getTime()
          const filename = `contract_${timestamp}`
          this.currentPhotoPath = `C:\\pic\\${filename}.jpg`
          console.log('当前照片路径:', this.currentPhotoPath) 
          
          // 拍照前提示用户
          this.$message.info('正在拍摄合同，请确保文档完全平整并位于取景框内...')
          
          // 短暂延迟后拍照，给用户时间调整文档位置
          setTimeout(() => {
            // 拍照并保存到本地
            this.sendScannerCommand(`bSaveJPG(C:\\pic\\,${filename})`)
            
            // 清除之前的图像数据，确保不会使用旧数据
            this.currentImageData = null
            
            this.$message.info('合同拍摄中，请稍候...')
            
            // 设置一个标志，表示我们正在等待 Base64Encode 的响应
            this.waitingForBase64 = true
            
            // 延迟一下，确保图片保存完成
            setTimeout(() => {
              // 使用 Base64Encode 命令获取高质量的 base64 图像数据
              this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)
              
              // 设置超时，确保即使没有收到 Base64Encode 的响应，也会继续处理
              setTimeout(() => {
                if (this.waitingForBase64) {
                  console.log('Base64Encode 响应超时，使用备用方法')
                  this.waitingForBase64 = false
                  
                  // 如果有预览图数据，使用预览图数据
                  if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && 
                      this.$refs.scannerPreview.src.startsWith('data:image')) {
                    console.log('使用预览图数据')
                    const imageData = this.$refs.scannerPreview.src
                    
                    // 添加到照片列表
                    this.photoList.push({
                      imageData: imageData,
                      path: this.currentPhotoPath,
                      timestamp: new Date().getTime()
                    })
                    
                    // 调用OCR识别
                    this.$message.info('正在识别证件号码...')
                    this.processOcrWithImage(imageData)
                  } else {
                    // 如果没有图像数据，尝试使用文件路径
                    console.log('使用文件路径:', this.currentPhotoPath)
                    
                    // 添加到照片列表（使用空图像数据）
                    this.photoList.push({
                      imageData: '',
                      path: this.currentPhotoPath,
                      timestamp: new Date().getTime()
                    })
                    
                    // 调用OCR识别
                    this.$message.info('正在识别证件号码...')
                    this.processOcrWithImage(this.currentPhotoPath)
                  }
                }
              }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法
              
            }, 1000) // 延迟1秒，确保图片保存完成
          }, 500) // 给用户半秒钟时间调整文档位置
        }, 500) // 延迟500ms，确保目录检查完成
      } catch (error) {
        console.error('扫描合同失败', error)
        this.$message.error('扫描合同失败')
      }
    },
    
    // 模拟模式下使用示例图片
    processWithSimulationImage() {
      this.$message.info('模拟模式：请上传图片或使用高拍仪')
      
      // 设置空白图像
      if (this.$refs.scannerPreview) {
        // 创建一个空白的Canvas
        const canvas = document.createElement('canvas')
        canvas.width = 600
        canvas.height = 400
        const ctx = canvas.getContext('2d')
        
        // 填充浅灰色背景
        ctx.fillStyle = '#f0f0f0'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        // 添加提示文字
        ctx.fillStyle = '#666666'
        ctx.font = '20px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('请上传图片或使用高拍仪', canvas.width / 2, canvas.height / 2)
        
        // 转换为数据URL
        const emptyImageUrl = canvas.toDataURL('image/jpeg')
        this.$refs.scannerPreview.src = emptyImageUrl
        this.contractForm.contractFile = emptyImageUrl
      }
    },
    
    // 发送高拍仪命令
    sendScannerCommand(command) {
      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        this.webSocket.send(command)
      } else {
        throw new Error('WebSocket未连接')
      }
    },
    
    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.webSocket) {
        // 先停止高拍仪
        if (this.scannerConnected) {
          try {
            this.webSocket.send('bStopPlay()')
          } catch (e) {
            console.error('停止高拍仪失败', e)
          }
        }
        
        // 关闭连接
        this.webSocket.close()
        this.webSocket = null
        this.scannerConnected = false
      }
    },
    
    // 切换到模拟模式
    switchToSimulationMode() {
      this.simulationMode = true;
      this.scannerConnected = false;
      
      if (this.webSocket) {
        this.manuallyDisconnected = true;
        this.webSocket.close();
        this.webSocket = null;
      }
      
      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动上传图片或使用模拟功能。');
      
      // 清除连接超时
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
      }
    },

    // 上传图片处理函数
    handleUploadImage(file) {
      if (file) {
        // 验证文件类型
        const isImage = file.type.indexOf('image/') !== -1;
        if (!isImage) {
          this.$message.error('请上传图片文件!');
          return false;
        }
        
        // 验证文件大小 (限制为10MB)
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          this.$message.error('图片大小不能超过10MB!');
          return false;
        }
        
        this.$message.info('正在处理图片，请稍候...');
        
        // 更新预览图并压缩图片
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e) => {
          // 压缩图片
          this.compressImage(e.target.result, (compressedDataUrl) => {
            // 更新预览图
            if (this.$refs.scannerPreview) {
              this.$refs.scannerPreview.src = compressedDataUrl;
            }
            
            // 添加到照片列表
            this.photoList.push({
              imageData: compressedDataUrl,
              path: '',
              timestamp: new Date().getTime(),
              isIdCard: false
            });
            
            this.$message.info('图片上传成功，正在识别证件号码...');
            
            // 调用OCR识别
            this.processOcrWithImage(compressedDataUrl);
          });
        };
        
        reader.onerror = (error) => {
          console.error('读取图片文件失败', error);
          this.$message.error('读取图片文件失败');
        };
      }
      return false; // 阻止默认的上传行为
    },
    
    // 压缩图片函数
    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {
      const img = new Image();
      img.src = dataUrl;
      
      img.onload = () => {
        // 创建Canvas
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        // 计算缩放比例
        if (width > height) {
          if (width > maxWidth) {
            height *= maxWidth / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width *= maxHeight / height;
            height = maxHeight;
          }
        }
        
        // 设置Canvas大小
        canvas.width = width;
        canvas.height = height;
        
        // 绘制图像
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);
        
        // 转换为压缩后的DataURL
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        
        // 计算压缩率
        const originalSize = dataUrl.length;
        const compressedSize = compressedDataUrl.length;
        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
        
        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);
        
        callback(compressedDataUrl);
      };
      
      img.onerror = () => {
        console.error('图片压缩失败');
        callback(dataUrl); // 失败时使用原始图片
      };
    },
    
    // 提交合同
    submitContract() {
      this.$refs.contractForm.validate(async valid => {
        if (valid) {
          if (this.contractForm.attachments.length === 0 && this.photoList.length === 0) {
            this.$message.warning('请先拍摄或上传合同图片');
            return;
          }
          
          if (!this.idCardFound && !this.contractForm.idCardNumber) {
            this.$message.warning('请先识别身份证或手动输入证件号码');
            return;
          }
          
          this.isProcessingUpload = true;
          this.$message.info('正在上传合同，请稍候...');
          
          // 如果有拍摄的照片但还没有生成PDF，先生成PDF
          if (this.photoList.length > 0 && this.contractForm.attachments.length === 0) {
            this.convertImagesToPdf();
            this.$message.info('请等待PDF生成完成后再提交');
            this.isProcessingUpload = false;
            return;
          }
          
          // 打印提交前的contractForm，检查projectSubContractorId是否存在
          console.log('提交前的contractForm:', JSON.stringify(this.contractForm))
          console.log('参建单位ID (projectSubContractorId):', this.contractForm.projectSubContractorId)
          
          // 准备合同信息
          const contract = {
            corpCode: this.contractForm.projectSubContractorId || '',
            corpName: this.contractForm.projectSubContractorName || '',
            idCardType: '01', // 默认身份证
            idNumber: this.contractForm.idCardNumber,
            workerId: this.contractForm.workerId, // 工人ID
            workerName: this.contractForm.workerName, // 工人姓名
            teamNo: this.contractForm.teamCode, // 班组编号 
            teamName: this.contractForm.teamName, // 班组名称
            contractPeriodType: this.getSalaryTypeValue(this.contractForm.contractPeriodType),
            startDate: this.formatDate(this.contractForm.contractStartDate),
            endDate: this.formatDate(this.contractForm.contractEndDate),
            signDate: this.formatDate(this.contractForm.contractSignDate || new Date()),
            unit: this.getSalaryTypeValue(this.contractForm.salaryType), // 按日传1，按月传2，按工程量传3
            unitPrice: Number(this.contractForm.salaryUnitPrice) || 100
          };
          
          // 打印最终的contract对象，检查corpCode是否正确设置
          console.log('最终的contract对象:', JSON.stringify(contract))
          console.log('corpCode值:', contract.corpCode)
          
          // 如果是按工程量计算，添加每xx和计量单位信息
          if (this.contractForm.salaryType === '按工程量') {
            contract.perUnit = this.contractForm.salaryPerUnit || 1;
            contract.unitType = this.contractForm.salaryUnitType || 80;
          }
          
          // 准备附件信息
          const attachments = [];
          
          try {
            // 处理所有附件
            if (this.contractForm.attachments && this.contractForm.attachments.length > 0) {
              for (const attachment of this.contractForm.attachments) {
                // 获取base64数据
                let base64Data = '';
                if (attachment.data) {
                  if (typeof attachment.data === 'string' && attachment.data.startsWith('data:')) {
                    // 已经是base64格式
                    base64Data = attachment.data.split(',')[1]; // 移除"data:application/pdf;base64,"前缀
                  } else if (attachment.file) {
                    // 如果有base64属性，直接使用
                    if (attachment.base64) {
                      base64Data = attachment.base64.split(',')[1];
                    } else {
                      // 否则从文件读取
                      base64Data = await this.readFileAsBase64Promise(attachment.file);
                    }
                  }
                }
                
                attachments.push({
                  name: attachment.name,
                  data: base64Data
                });
              }
            }
            
            // 准备请求数据
            const requestData = {
              projectCode: this.contractForm.projectCode,
              contractList: [contract],
              attachments: attachments
            };
            
            // 打印最终的请求数据
            console.log('上传合同的请求数据:', JSON.stringify({
              projectCode: requestData.projectCode,
              contractList: requestData.contractList,
              attachmentsCount: requestData.attachments.length
            }))
            
            // 调用上传API
            uploadContract(requestData)
              .then(response => {
                if (response.code === 0) {
                  this.$message.success('合同上传成功');
                  // 返回上一页
                  this.goBack();
                } else {
                  this.$message.error(response.message || '合同上传失败');
                }
              })
              .catch(error => {
                console.error('合同上传失败:', error);
                this.$message.error('合同上传失败: ' + (error.message || '未知错误'));
              })
              .finally(() => {
                this.isProcessingUpload = false;
              });
          } catch (error) {
            console.error('处理附件失败:', error);
            this.$message.error('处理附件失败: ' + (error.message || '未知错误'));
            this.isProcessingUpload = false;
          }
        } else {
          this.$message.warning('请完善合同信息');
        }
      });
    },
    
    // 读取文件为Base64的Promise版本
    readFileAsBase64Promise(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          const base64Data = e.target.result.split(',')[1];
          resolve(base64Data);
        };
        reader.onerror = e => reject(e);
        reader.readAsDataURL(file);
      });
    },
    
    // 获取工资类型对应的值
    getSalaryTypeValue(type) {
      const typeMap = {
        '按天': '1',
        '按月': '2',
        '按工程量': '3',
        '固定期限': '0',
        '无固定期限': '1',
        '以完成一定工作任务为期限': '1'
      };
      return typeMap[type] || '1';
    },
    
    // 获取工资类型对应的文本
    getSalaryTypeText(type) {
      const typeMap = {
        '1': '按天',
        '2': '按月',
        '3': '按工程量',
        '4': '其他方式'
      };
      return typeMap[type] || '按天';
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 预览附件
    previewAttachment(attachment) {
      if (attachment && attachment.data) {
        // 判断文件类型
        if (attachment.type && attachment.type.includes('image/')) {
          // 图片类型 - 在弹窗中预览
          this.$msgbox({
            title: attachment.name,
            message: `<div style="text-align:center;height:100%;width:100%;display:flex;align-items:center;justify-content:center;">
              <img src="${attachment.data}" style="max-width:100%;max-height:100%;object-fit:contain;" />
            </div>`,
            dangerouslyUseHTMLString: true,
            customClass: 'image-preview-dialog pdf-preview-dialog', // 使用相同的最大化样式
            showCancelButton: false,
            showConfirmButton: false
          });
        } else if (attachment.type === 'application/pdf') {
          try {
            // PDF类型 - 创建Blob URL并使用对象标签预览
            const pdfBlob = this.dataURLtoBlob(attachment.data);
            const blobUrl = URL.createObjectURL(pdfBlob);
            
            // 使用MessageBox组件以最大化方式显示PDF
            this.$msgbox({
              title: attachment.name,
              message: `<div style="height:100%;width:100%;">
                <object 
                  data="${blobUrl}" 
                  type="application/pdf" 
                  width="100%" 
                  height="100%"
                  style="width:100%;height:100%;">
                    <p>您的浏览器不支持PDF预览，请 
                      <a href="${blobUrl}" download="${attachment.name}">点击下载</a>
                    </p>
                </object>
              </div>`,
              dangerouslyUseHTMLString: true,
              customClass: 'pdf-preview-dialog',
              showCancelButton: false,
              showConfirmButton: false,
              beforeClose: (action, instance, done) => {
                // 关闭对话框时释放blob URL
                URL.revokeObjectURL(blobUrl);
                done();
              }
            });
          } catch (error) {
            console.error('预览PDF失败:', error);
            this.$message.error('无法预览PDF文件: ' + error.message);
            
            // 如果有原始文件对象，尝试直接下载
            if (attachment.file) {
              const url = URL.createObjectURL(attachment.file);
              const link = document.createElement('a');
              link.href = url;
              link.download = attachment.name;
              link.click();
              setTimeout(() => URL.revokeObjectURL(url), 100);
            }
          }
        } else {
          // 其他类型 - 尝试下载文件
          try {
            const link = document.createElement('a');
            
            // 检查是否是Blob URL
            if (attachment.data instanceof Blob || attachment.data.startsWith('blob:')) {
              link.href = attachment.data;
            } else if (attachment.file) {
              // 如果有原始文件对象，使用它创建URL
              link.href = URL.createObjectURL(attachment.file);
            } else {
              // 尝试将data URL转换为Blob URL
              const blob = this.dataURLtoBlob(attachment.data);
              link.href = URL.createObjectURL(blob);
            }
            
            link.download = attachment.name;
            link.click();
            
            // 如果创建了Blob URL，需要释放
            if (link.href.startsWith('blob:')) {
              setTimeout(() => URL.revokeObjectURL(link.href), 100);
            }
          } catch (error) {
            console.error('下载文件失败:', error);
            this.$message.error('无法下载文件: ' + error.message);
          }
        }
      } else {
        this.$message.warning('无法预览该附件，附件数据不完整');
      }
    },
    
    // 将Data URL转换为Blob对象
    dataURLtoBlob(dataURL) {
      try {
        // 检查是否是有效的data URL格式
        if (!dataURL || typeof dataURL !== 'string' || !dataURL.includes(';base64,')) {
          console.error('无效的Data URL格式:', dataURL);
          throw new Error('无效的Data URL格式');
        }
        
        // 分割Data URL，获取MIME类型和base64数据
        const parts = dataURL.split(';base64,');
        const contentType = parts[0].split(':')[1];
        const raw = window.atob(parts[1]);
        const rawLength = raw.length;
        const uInt8Array = new Uint8Array(rawLength);
        
        // 将base64数据转换为Uint8Array
        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i);
        }
        
        return new Blob([uInt8Array], { type: contentType });
      } catch (error) {
        console.error('转换Data URL到Blob失败:', error);
        throw new Error('转换Data URL到Blob失败: ' + error.message);
      }
    },
    
    // 删除附件
    removeAttachment(index) {
      this.$confirm('确定要删除此附件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 释放URL对象
        if (this.contractForm.attachments[index].data) {
          URL.revokeObjectURL(this.contractForm.attachments[index].data);
        }
        // 从数组中删除
        this.contractForm.attachments.splice(index, 1);
        this.$message.success('附件已删除');
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 扫描身份证
    scanIdCard() {
      if (!this.scannerConnected && !this.simulationMode) {
        this.$message.warning('请先启动高拍仪')
        return
      }
      
      try {
        if (this.simulationMode) {
          // 模拟模式下，直接调用OCR接口处理示例图片
          this.processOcrWithSimulationImage()
          return
        }
        
        // 设置身份证自动寻边模式
        this.sendScannerCommand('bSetMode(4)')
        
        // 生成唯一文件名（使用时间戳）
        const timestamp = new Date().getTime()
        const filename = `idcard_${timestamp}`
        this.currentPhotoPath = `C:\\pic\\${filename}.jpg`
        console.log('当前照片路径:', this.currentPhotoPath) 
        
        // 拍照并保存到本地
        this.sendScannerCommand(`bSaveJPG(C:\\pic\\,${filename})`)
        
        // 识别条码
        this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)
        
        // 调用OCR识别
        setTimeout(() => {
          this.processOcrWithImage(this.currentPhotoPath)
        }, 1000) // 延迟1秒，确保图片保存完成
        
        this.$message.info('正在识别身份证，请稍候...')
      } catch (error) {
        console.error('扫描身份证失败', error)
        this.$message.error('扫描身份证失败')
      }
    },

    // 处理OCR识别结果
    processOcrWithImage(imagePath) {
      if (this.isProcessingOcr) {
        return
      }
      
      this.isProcessingOcr = true
      this.$message.info('正在进行OCR识别...')
      
      // 准备表单数据
      const formData = new FormData()
      
      // 判断是否是base64格式的图片数据
      if (imagePath.startsWith('data:image')) {
        // 创建文件对象从base64数据
        const base64Data = imagePath.split(',')[1]
        
        // 检查是否是从 Base64Encode 命令获取的高质量图像数据
        const isHighQualityBase64 = this.currentImageData === imagePath && !this.waitingForBase64;
        
        if (isHighQualityBase64) {
          // 如果是高质量 base64 数据，直接使用，不需要额外处理
          console.log('使用高质量 base64 数据，跳过图像处理')
          
          const byteCharacters = atob(base64Data)
          const byteArrays = []
          
          for (let i = 0; i < byteCharacters.length; i++) {
            byteArrays.push(byteCharacters.charCodeAt(i))
          }
          
          const byteArray = new Uint8Array(byteArrays)
          const blob = new Blob([byteArray], { type: 'image/jpeg' })
          
          // 创建文件对象
          const fileName = `contract_${new Date().getTime()}.jpg`
          const file = new File([blob], fileName, { type: 'image/jpeg' })
          
          // 添加到表单
          formData.append('image', file)
          console.log('发送高质量 base64 数据进行 OCR 识别')
          
          // 同时保留 base64 数据作为备用
          formData.append('image_base64', imagePath)
          
          // 调用 OCR API
          this.callOcrApi(formData)
        } else {
          // 在发送前处理图像，去除紫色边框和文字标记
          this.preprocessImage(imagePath).then(processedImageData => {
            // 使用处理后的图像数据
            const processedBase64 = processedImageData.split(',')[1]
            const byteCharacters = atob(processedBase64)
            const byteArrays = []
            
            for (let i = 0; i < byteCharacters.length; i++) {
              byteArrays.push(byteCharacters.charCodeAt(i))
            }
            
            const byteArray = new Uint8Array(byteArrays)
            const blob = new Blob([byteArray], { type: 'image/jpeg' })
            
            // 创建文件对象
            const fileName = `contract_${new Date().getTime()}.jpg`
            const file = new File([blob], fileName, { type: 'image/jpeg' })
            
            // 添加到表单
            formData.append('image', file)
            console.log('发送处理后的文件对象进行 OCR 识别')
            
            // 同时保留处理后的base64数据作为备用
            formData.append('image_base64', processedImageData)
            
            // 调用OCR API
            this.callOcrApi(formData)
          }).catch(error => {
            console.error('图像预处理失败，使用原始图像:', error)
            
            // 如果处理失败，使用原始图像
            const byteCharacters = atob(base64Data)
            const byteArrays = []
            
            for (let i = 0; i < byteCharacters.length; i++) {
              byteArrays.push(byteCharacters.charCodeAt(i))
            }
            
            const byteArray = new Uint8Array(byteArrays)
            const blob = new Blob([byteArray], { type: 'image/jpeg' })
            
            // 创建文件对象
            const fileName = `contract_${new Date().getTime()}.jpg`
            const file = new File([blob], fileName, { type: 'image/jpeg' })
            
            // 添加到表单
            formData.append('image', file)
            console.log('发送原始文件对象进行 OCR 识别')
            
            // 同时保留base64数据作为备用
            formData.append('image_base64', imagePath)
            
            // 调用OCR API
            this.callOcrApi(formData)
          })
        }
      } else {
        // 如果是文件路径，尝试读取文件并上传
        formData.append('image_path', imagePath)
        console.log(`发送图片路径进行OCR识别: ${imagePath}`)
        
        // 调用OCR API
        this.callOcrApi(formData)
      }
    },
    
    // 调用OCR API
    callOcrApi(formData) {
      axios.post(this.scannerConfig.ocrApiUrl, formData)
        .then(response => {
          this.handleOcrResult(response.data)
        })
        .catch(error => {
          console.error('OCR识别失败', error)
          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))
        })
        .finally(() => {
          this.isProcessingOcr = false
        })
    },
    
    // 图像预处理函数 - 去除紫色边框和文字标记
    preprocessImage(base64Image) {
      return new Promise((resolve, reject) => {
        try {
          // 创建图像对象
          const img = new Image()
          img.onload = () => {
            // 创建Canvas
            const canvas = document.createElement('canvas')
            canvas.width = img.width
            canvas.height = img.height
            const ctx = canvas.getContext('2d')
            
            // 绘制图像到Canvas
            ctx.drawImage(img, 0, 0)
            
            // 获取图像数据
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
            const data = imageData.data
            
            // 处理紫色边框 - 将紫色像素替换为白色
            for (let i = 0; i < data.length; i += 4) {
              const r = data[i]
              const g = data[i + 1]
              const b = data[i + 2]
              
              // 检测紫色范围 (RGB近似值)
              // 紫色通常R和B较高，G较低
              if ((r > 100 && r < 200) && (g < 100) && (b > 100 && b < 200)) {
                // 将紫色替换为白色
                data[i] = 255     // R
                data[i + 1] = 255 // G
                data[i + 2] = 255 // B
              }
            }
            
            // 将处理后的图像数据放回Canvas
            ctx.putImageData(imageData, 0, 0)
            
            // 尝试去除文字标记 (简化处理)
            // 这里使用简单的边缘检测和阈值处理
            // 获取灰度图像
            const grayImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
            const grayData = grayImageData.data
            
            // 转换为灰度图
            for (let i = 0; i < grayData.length; i += 4) {
              const avg = (grayData[i] + grayData[i + 1] + grayData[i + 2]) / 3
              
              // 应用阈值，将可能的文字区域变白
              if (avg < 180) { // 较暗的区域可能是文字
                // 检查周围像素，如果是小区域的暗像素，可能是文字
                // 这是一个简化的处理，实际应用中可能需要更复杂的算法
                grayData[i] = 255     // R
                grayData[i + 1] = 255 // G
                grayData[i + 2] = 255 // B
              }
            }
            
            // 将处理后的图像数据转换为base64
            const processedBase64 = canvas.toDataURL('image/jpeg', 0.95)
            
            console.log('图像预处理完成')
            resolve(processedBase64)
          }
          
          img.onerror = (error) => {
            console.error('图像加载失败:', error)
            reject(error)
          }
          
          // 设置图像源
          img.src = base64Image
        } catch (error) {
          console.error('图像预处理失败:', error)
          reject(error)
        }
      })
    },
    
    // 模拟模式下使用示例图片进行OCR识别
    processOcrWithSimulationImage() {
      this.isProcessingOcr = true
      this.$message.info('模拟模式：正在进行OCR识别...')
      
      // 准备表单数据
      const formData = new FormData()
      formData.append('simulation', 'true')
      
      // 调用OCR API
      axios.post(this.scannerConfig.ocrApiUrl, formData)
        .then(response => {
          this.handleOcrResult(response.data)
        })
        .catch(error => {
          console.error('模拟OCR识别失败', error)
          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))
        })
        .finally(() => {
          this.isProcessingOcr = false
        })
    },
    
    // 处理OCR识别结果
    handleOcrResult(result) {
      if (!result || !result.success) {
        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))
        return
      }
      
      this.$message.success('OCR识别成功')
      console.log('OCR识别结果:', result)
      
      // 更新表单数据
      const ocrData = result.data || {}
      
      // 更新身份证号
      if (ocrData.id_number) {
        this.contractForm.idCardNumber = ocrData.id_number
        // 添加更明显的提示
        this.$notify({
          title: '证件号码识别成功',
          message: ocrData.id_number,
          type: 'success',
          duration: 5000
        })
        
        // 标记已找到身份证
        this.idCardFound = true
        
        // 如果有身份证号，尝试从系统中查询更多信息
        this.searchWorkerByIdCard(ocrData.id_number)
      }
      
      // 更新姓名
      if (ocrData.name) {
        this.contractForm.workerName = ocrData.name
      }
    },
    
    // 验证是否为身份证号
    isIdCardNumber(str) {
      // 简单验证18位或15位身份证号
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      return reg.test(str)
    },
    
    // 根据身份证号查询工人信息
    searchWorkerByIdCard(idCardNumber) {
      if (!idCardNumber) {
        this.$message.warning('证件号码不能为空')
        return
      }
      
      this.isLoadingWorkerInfo = true
      this.$message.info('正在查询工人信息...')
      
      // 调用API根据身份证号查询工人信息
      getWorkerByIdCard(idCardNumber)
        .then(response => {
          if (response.code === 0 && response.data) {
            const workerData = response.data
            
            // 打印API返回的完整数据，查看是否包含参建单位ID相关字段
            console.log('API返回的工人信息:', workerData)
            
            // 更新表单数据
            this.contractForm.workerId = workerData.workId || ''
            this.contractForm.workerName = workerData.workerName || ''
            
            // 获取并保存班组编号和班组名称
            this.contractForm.teamCode = workerData.teamNo || workerData.teamSysNo || ''
            this.contractForm.teamName = workerData.teamName || workerData.teamSysName || ''
            
            // 填充其他工人信息
            this.contractForm.ProjectName = workerData.projectName || ''
            this.contractForm.projectCode = workerData.projectCode || ''
            
            // 尝试从多个可能的字段中获取参建单位ID
            this.contractForm.projectSubContractorId = 
              workerData.projectSubContractorId || 
              workerData.corpCode || 
              workerData.participantCode || 
              workerData.corpId || 
              workerData.subContractorId || 
              '';
            
            console.log('可能的参建单位ID字段:',
              '1.projectSubContractorId=', workerData.projectSubContractorId,
              '2.corpCode=', workerData.corpCode,
              '3.participantCode=', workerData.participantCode,
              '4.corpId=', workerData.corpId,
              '5.subContractorId=', workerData.subContractorId
            );
            
            this.contractForm.projectSubContractorName = workerData.projectSubContractorName || workerData.corpName || ''
            
            // 打印保存后的projectSubContractorId
            console.log('保存的参建单位ID:', this.contractForm.projectSubContractorId)
            
            this.contractForm.contractPeriodType = workerData.contractPeriodType || '固定期限'
            this.contractForm.contractStartDate = workerData.contractStartDate ? new Date(workerData.contractStartDate) : new Date()
            this.contractForm.contractEndDate = workerData.contractEndDate ? new Date(workerData.contractEndDate) : null
            
            // 处理工资类型 - 将数字转换为文本表示
            console.log('原始工资类型值:', workerData.salaryType)
            this.contractForm.salaryType = this.getSalaryTypeText(workerData.salaryType) || '按天'
            console.log('转换后工资类型:', this.contractForm.salaryType)
            
            this.contractForm.salaryUnitPrice = workerData.salaryUnitPrice || ''
            this.contractForm.salaryPerUnit = workerData.salaryPerUnit || ''
            this.contractForm.salaryUnitType = workerData.salaryUnitType || ''
            
            // 根据工资类型更新验证规则
            this.handleSalaryTypeChange(this.contractForm.salaryType)
            
            if (workerData.contractSignDate) {
              this.contractForm.contractSignDate = new Date(workerData.contractSignDate)
            }
            
            this.$message.success('工人信息查询成功')
            
            // 添加更详细的通知
            this.$notify({
              title: '工人信息查询成功',
              message: `已找到工人: ${workerData.workerName}${this.contractForm.teamName ? ', 班组: ' + this.contractForm.teamName : ''}`,
              type: 'success',
              duration: 5000
            })
          } else {
            this.$message.warning(response.msg || '未找到工人信息')
            
            // 如果没有找到，可以保留一些基本信息
            if (this.contractForm.idCardNumber && !this.contractForm.workerName) {
              // 从身份证号提取出生日期和性别信息
              this.extractInfoFromIdCard(idCardNumber)
            }
          }
        })
        .catch(error => {
          console.error('查询工人信息失败:', error)
          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))
          
          // 如果API调用失败，可以尝试从身份证号提取一些基本信息
          if (this.contractForm.idCardNumber) {
            this.extractInfoFromIdCard(idCardNumber)
          }
        })
        .finally(() => {
          this.isLoadingWorkerInfo = false
        })
    },
    
    // 从身份证号提取信息
    extractInfoFromIdCard(idCardNumber) {
      if (!idCardNumber || idCardNumber.length < 18) return
      
      try {
        // 提取性别 (第17位，奇数为男，偶数为女)
        const genderCode = parseInt(idCardNumber.charAt(16))
        const gender = genderCode % 2 === 1 ? '男' : '女'
        
        console.log('从身份证号提取的性别:', gender)
      } catch (e) {
        console.error('从身份证号提取信息失败:', e)
      }
    },
    
    // 解析身份证号码 (示例，实际需要更复杂的OCR库)
    parseIdCardNumber(base64Image) {
      // 这是一个非常简化的示例，实际需要使用专业的OCR库（如Tesseract.js, PaddleOCR等）
      // 这里只是模拟一个简单的解析逻辑
      try {
        const img = new Image();
        img.src = 'data:image/jpeg;base64,' + base64Image;
        img.onload = () => {
          // 在实际应用中，这里会调用OCR库进行识别
          // 例如：PaddleOCR.recognizeText(img.src);
          // 假设识别结果包含身份证号码
          const mockIdCardNumber = '123456789012345678'; // 模拟身份证号码
          return mockIdCardNumber;
        };
      } catch (e) {
        console.error('图片解析失败', e);
        return null;
      }
    },

    // 获取员工信息
    fetchWorkerInfo(idCardNumber) {
      // 这是一个模拟的API调用，实际需要一个真实的后端接口
      // 例如：axios.get(`${this.apiBaseUrl}/api/workers/idCard/${idCardNumber}`)
      // 假设成功获取到工人信息
      const mockWorker = {
        workerId: '', // 模拟工人ID
        workerName: '', // 模拟工人姓名
        idCardNumber: idCardNumber,
        contractType: '',
        contractSignDate: new Date(),
        remark: ''
      };

      this.contractForm = { ...mockWorker }; // 更新合同表单
      this.$message.success('已获取到工人信息！');
    },

    // 删除照片
    removePhoto(index) {
      this.$confirm('确定要删除此照片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.photoList.splice(index, 1);
        this.$message.success('照片已删除');
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 刷新工人信息
    refreshWorkerInfo() {
      if (!this.contractForm.idCardNumber) {
        this.$message.warning('请先输入证件号码')
        return
      }
      
      // 重新获取工人信息
      this.searchWorkerByIdCard(this.contractForm.idCardNumber)
    },

    // 获取计量单位标签
    getUnitTypeLabel(value) {
      const unitTypeMap = {
        '80': '米',
        '81': '平方米',
        '82': '立方米'
      };
      return unitTypeMap[value] || '单位';
    },
    
    // 将图片转换为PDF
    convertImagesToPdf() {
      if (this.photoList.length === 0) {
        this.$message.warning('请先拍摄或上传合同图片');
        return;
      }

      this.isGeneratingPDF = true;
      this.$message.info('正在生成PDF，请稍候...');

      // 创建一个新的PDF文档
      const pdf = new jsPDF('p', 'mm', 'a4');
      const promises = [];
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // 为每张图片创建一个Promise
      this.photoList.forEach((photo, index) => {
        const promise = new Promise((resolve) => {
          // 优先使用高质量的 base64 图像数据
          if (photo.imageData && photo.imageData.startsWith('data:image')) {
            // 创建一个临时的图片元素
            const img = new Image();
            img.src = photo.imageData;
            
            img.onload = () => {
              // 计算图片在PDF中的尺寸，保持宽高比
              let imgWidth = pageWidth - 20; // 留出10mm边距
              let imgHeight = (img.height * imgWidth) / img.width;
              
              // 如果图片高度超过页面高度，按高度缩放
              if (imgHeight > pageHeight - 20) {
                imgHeight = pageHeight - 20;
                imgWidth = (img.width * imgHeight) / img.height;
              }
              
              // 如果不是第一页，添加新页
              if (index > 0) {
                pdf.addPage();
              }
              
              // 将图片添加到PDF
              pdf.addImage(
                photo.imageData, 
                'JPEG', 
                (pageWidth - imgWidth) / 2, // 居中显示
                10, // 顶部边距
                imgWidth, 
                imgHeight
              );
              
              resolve();
            };
            
            img.onerror = () => {
              this.$message.error(`处理第${index + 1}张图片时出错`);
              resolve(); // 即使出错也继续处理其他图片
            };
          } else if (photo.path && photo.path.startsWith('C:\\')) {
            // 如果没有图像数据但有本地路径，尝试获取本地图片的 base64 数据
            console.log(`尝试获取本地图片的 base64 数据: ${photo.path}`);
            
            // 使用 Base64Encode 命令获取图片的 base64 数据
            if (this.scannerConnected) {
              this.sendScannerCommand(`Base64Encode(${photo.path})`);
              
              // 设置一个超时，如果在指定时间内没有收到响应，则跳过该图片
              setTimeout(() => {
                if (!photo.imageData || !photo.imageData.startsWith('data:image')) {
                  this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);
                  resolve();
                }
              }, 3000);
            } else {
              this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);
              resolve();
            }
          } else {
            // 如果既没有图像数据也没有本地路径，跳过该图片
            this.$message.warning(`第${index + 1}张图片没有有效数据，将跳过`);
            resolve();
          }
        });
        
        promises.push(promise);
      });

      // 当所有图片处理完成后，保存PDF
      Promise.all(promises).then(() => {
        // 生成文件名
        const workerName = this.contractForm.workerName || '未命名';
        const idCardNumber = this.contractForm.idCardNumber || '';
        const timestamp = new Date().getTime();
        const fileName = `${workerName}_${idCardNumber}_合同_${timestamp}.pdf`;
        
        // 获取PDF文件的Blob对象
        const pdfBlob = pdf.output('blob');
        
        // 创建附件对象
        const blobUrl = URL.createObjectURL(pdfBlob);
        const attachment = {
          name: fileName,
          type: 'application/pdf',
          size: pdfBlob.size,
          data: blobUrl,
          file: new File([pdfBlob], fileName, { type: 'application/pdf' })
        };
        
        // 添加到合同附件数组
        this.contractForm.attachments.push(attachment);
        
        // 将生成的PDF设置为合同文件
        this.contractForm.contractFile = attachment;
        
        // 获取PDF的base64数据，用于上传API
        const reader = new FileReader();
        reader.readAsDataURL(pdfBlob);
        reader.onloadend = () => {
          const base64data = reader.result;
          // 保存base64数据到附件对象，便于后续上传
          attachment.base64 = base64data;
        };
        
        this.$message.success('PDF生成成功并已添加到合同附件');
        this.isGeneratingPDF = false;
      }).catch(error => {
        console.error('生成PDF失败:', error);
        this.$message.error('生成PDF失败: ' + (error.message || '未知错误'));
        this.isGeneratingPDF = false;
      });
    },

    // 手动上传附件
    handleAttachmentUpload(file) {
      // Element UI 的 upload 组件传入的是一个包含文件信息的对象
      // 需要从中获取实际的文件对象
      const actualFile = file.raw || file;
      
      if (actualFile) {
        const isLt10M = actualFile.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          this.$message.error('文件大小不能超过10MB!');
          return false;
        }

        // 显示上传中提示
        this.$message.info('正在处理文件，请稍候...');

        const reader = new FileReader();
        reader.readAsDataURL(actualFile);
        reader.onload = (e) => {
          // 获取文件类型图标
          const fileIcon = this.getFileTypeIcon(actualFile.type);
          
          let fileData = e.target.result;
          
          // 确保fileData是有效的data URL格式
          if (!fileData || typeof fileData !== 'string' || !fileData.includes(';base64,')) {
            console.warn('文件数据不是有效的data URL格式，将使用Blob URL代替');
            fileData = URL.createObjectURL(actualFile);
          }
          
          // 创建附件对象
          const attachment = {
            name: actualFile.name,
            type: actualFile.type,
            size: actualFile.size,
            data: fileData,
            file: actualFile,
            icon: fileIcon
          };
          
          // 添加到附件列表
          this.contractForm.attachments.push(attachment);
          
          // 如果是PDF，自动设置为合同文件
          if (actualFile.type === 'application/pdf' && !this.contractForm.contractFile) {
            this.contractForm.contractFile = attachment;
            this.$message.success('已自动设置为合同文件');
          } else {
            this.$message.success('附件上传成功');
          }
        };
        reader.onerror = (error) => {
          console.error('读取文件失败', error);
          this.$message.error('读取文件失败');
        };
      }
      return false; // 阻止默认的上传行为
    },

    // 根据文件类型获取图标
    getFileTypeIcon(fileType) {
      if (fileType.includes('image/')) {
        return 'el-icon-picture';
      } else if (fileType === 'application/pdf') {
        return 'el-icon-document';
      } else if (fileType.includes('word') || fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        return 'el-icon-document-checked';
      } else if (fileType.includes('excel') || fileType === 'application/vnd.ms-excel' || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        return 'el-icon-tickets';
      } else {
        return 'el-icon-document';
      }
    },

    // 手动上传附件前的验证
    beforeAttachmentUpload(file) {
      // 验证文件大小（限制为10MB）
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!');
        return false;
      }
      
      // 验证文件类型
      const allowedTypes = [
        'application/pdf', 
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'image/gif'
      ];
      
      const isAllowedType = allowedTypes.includes(file.type) || file.type.startsWith('image/');
      if (!isAllowedType) {
        this.$message.error('只支持PDF、Word、Excel和图片格式!');
        return false;
      }
      
      return true;
    }
  }
}
</script>

<style scoped>
.contract-upload-container {
  padding: 20px;
}

.contract-upload-container h3 {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
}

.scanner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9fafc;
}

.scanner-layout {
  display: flex;
  gap: 20px;
  width: 100%;
}

.scanner-left, .scanner-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scanner-right {
  border-left: 1px solid #ebeef5;
  padding-left: 20px;
  max-height: 700px;
  overflow-y: auto;
}

.scanner-preview {
  width: 100%;
  height: 400px;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  background-color: #ebeef5;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.scanner-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.scanner-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.upload-button {
  display: inline-block;
  margin-left: 10px;
}

.scanner-right h4 {
  margin-bottom: 15px;
  color: #303133;
}

.photo-actions-top {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.photo-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  overflow-y: auto;
}

.photo-item {
  width: 180px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.photo-thumbnail {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.photo-actions {
  padding: 8px;
  display: flex;
  justify-content: center;
  background-color: #f5f7fa;
}

.contract-info-form {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.id-card-input {
  display: flex;
  align-items: center;
}

.id-card-input .el-input {
  flex: 1;
}

.refresh-button {
  flex-shrink: 0;
  margin-left: 10px;
}

.contract-attachments {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9fafc;
}

.contract-attachments h4 {
  margin-bottom: 15px;
  color: #303133;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.attachment-actions {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}

.upload-attachment .el-upload-dragger {
  width: 100%;
  height: 100px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  text-align: center;
  line-height: 100px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-attachment .el-upload-dragger:hover {
  border-color: #409eff;
}

.upload-attachment .el-upload-dragger .el-icon-upload {
  font-size: 24px;
  color: #8c939d;
  margin-bottom: 10px;
}

.upload-attachment .el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.no-attachments {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 20px;
  color: #606266;
}

.file-name {
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px; /* Adjust as needed */
}

/* 图片预览对话框样式已合并到PDF预览对话框样式中 */

/* PDF预览对话框样式 */
.pdf-preview-dialog {
  width: 95% !important;
  height: 95vh !important;
  margin: 0 auto !important;
  max-width: none !important;
}

.pdf-preview-dialog .el-message-box {
  width: 95% !important;
  max-width: none !important;
  margin: 0 auto;
  height: 95vh !important;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 2vh;
  left: 2.5%;
}

.pdf-preview-dialog .el-message-box__header {
  padding: 10px 20px !important;
}

.pdf-preview-dialog .el-message-box__content {
  padding: 0 !important;
  flex: 1;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(95vh - 55px) !important;
}

.pdf-preview-dialog .el-message-box__message {
  height: 100% !important;
  padding: 0 !important;
}

.pdf-preview-dialog .el-message-box__message p {
  height: 100% !important;
  margin: 0 !important;
}

.pdf-preview-dialog object {
  width: 100%;
  height: 100%;
  border: none;
}

/* 为了确保遮罩层正确显示 */
.v-modal {
  opacity: 0.6 !important;
}

@media (max-width: 768px) {
  .scanner-layout {
    flex-direction: column;
  }
  
  .scanner-right {
    border-left: none;
    border-top: 1px solid #ebeef5;
    padding-left: 0;
    padding-top: 20px;
    margin-top: 20px;
  }
  
  .scanner-preview {
    width: 100%;
    height: 300px;
  }
  
  .scanner-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .upload-button {
    margin-left: 0;
    margin-top: 10px;
  }
  
  .id-card-input {
    flex-direction: column;
    align-items: stretch;
  }
  
  .refresh-button {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
}
</style>
