{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractUpload.vue", "mtime": 1753669028133}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractUpload.vue"], "names": [], "mappings": ";AAmPA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ContractUpload.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\r\n  <div class=\"contract-upload-container\">\r\n    <h3>合同上传</h3>\r\n    \r\n    <div class=\"scanner-container\">\r\n      <div class=\"scanner-layout\">\r\n        <!-- 左侧：高拍仪界面 -->\r\n        <div class=\"scanner-left\">\r\n          <div class=\"scanner-preview\">\r\n            <img id=\"photo\" src=\"\" width=\"600\" height=\"400\" ref=\"scannerPreview\">\r\n          </div>\r\n          \r\n          <div class=\"scanner-controls\">\r\n            <el-button type=\"primary\" @click=\"startScanner\">启动高拍仪</el-button>\r\n            <el-button type=\"success\" @click=\"scanContract\">拍摄合同</el-button>\r\n            <el-button type=\"warning\" @click=\"stopScanner\">停止高拍仪</el-button>\r\n            <el-upload\r\n              class=\"upload-button\"\r\n              action=\"#\"\r\n              :show-file-list=\"false\"\r\n              :before-upload=\"handleUploadImage\">\r\n              <el-button type=\"primary\" icon=\"el-icon-upload\">上传图片</el-button>\r\n            </el-upload>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 右侧：已拍摄照片 -->\r\n        <div class=\"scanner-right\" v-if=\"photoList.length > 0\">\r\n          <h4>已拍摄照片 ({{ photoList.length }})</h4>\r\n          <div class=\"photo-actions-top\">\r\n            <el-button type=\"primary\" @click=\"convertImagesToPdf\" :loading=\"isGeneratingPDF\" icon=\"el-icon-document\">\r\n              生成PDF\r\n            </el-button>\r\n          </div>\r\n          <div class=\"photo-items\">\r\n            <div v-for=\"(photo, index) in photoList\" :key=\"index\" class=\"photo-item\">\r\n              <img :src=\"photo.imageData\" alt=\"拍摄照片\" class=\"photo-thumbnail\">\r\n              <div class=\"photo-actions\">\r\n                <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-delete\" @click=\"removePhoto(index)\">删除</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"contract-info-form\">\r\n      <el-form :model=\"contractForm\" label-width=\"120px\" ref=\"contractForm\" :rules=\"rules\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\r\n              <el-input v-model=\"contractForm.workerName\" placeholder=\"请输入工人姓名\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\r\n              <div class=\"id-card-input\">\r\n                <el-input v-model=\"contractForm.idCardNumber\" placeholder=\"请输入证件号码\"></el-input>\r\n                <el-button type=\"primary\" icon=\"el-icon-refresh\" class=\"refresh-button\" @click=\"refreshWorkerInfo\" :loading=\"isLoadingWorkerInfo\">刷新</el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目名称\" prop=\"ProjectName\">\r\n              <el-input v-model=\"contractForm.ProjectName\" placeholder=\"项目名称\" :disabled=\"true\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目编号\" prop=\"projectCode\">\r\n              <el-input v-model=\"contractForm.projectCode\" placeholder=\"项目编号\" :disabled=\"true\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"参建单位\" prop=\"projectSubContractorName\">\r\n              <el-input v-model=\"contractForm.projectSubContractorName\" placeholder=\"请输入参建单位\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"签订日期\" prop=\"contractSignDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractSignDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同期限类型\" prop=\"contractPeriodType\">\r\n              <el-select v-model=\"contractForm.contractPeriodType\" placeholder=\"请选择合同期限类型\" style=\"width: 100%\">\r\n                <el-option label=\"固定期限\" value=\"固定期限\"></el-option>\r\n                <el-option label=\"无固定期限\" value=\"无固定期限\"></el-option>\r\n                <el-option label=\"以完成一定工作任务为期限\" value=\"以完成一定工作任务为期限\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同开始日期\" prop=\"contractStartDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractStartDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择开始日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同结束日期\" prop=\"contractEndDate\">\r\n              <el-date-picker\r\n                v-model=\"contractForm.contractEndDate\"\r\n                type=\"date\"\r\n                placeholder=\"选择结束日期\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工资类型\" prop=\"salaryType\">\r\n              <el-select v-model=\"contractForm.salaryType\" placeholder=\"请选择工资类型\" style=\"width: 100%\" @change=\"handleSalaryTypeChange\">\r\n                <el-option label=\"按天\" value=\"按天\"></el-option>\r\n                <el-option label=\"按月\" value=\"按月\"></el-option>\r\n                <el-option label=\"按工程量\" value=\"按工程量\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"contractForm.remark\" placeholder=\"请输入备注信息\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <!-- 根据工资类型显示不同的字段 -->\r\n        <el-row :gutter=\"20\" v-if=\"['按天', '按月'].includes(contractForm.salaryType)\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"计量单价\" prop=\"salaryUnitPrice\">\r\n              <el-input v-model=\"contractForm.salaryUnitPrice\" placeholder=\"请输入计量单价\">\r\n                <template slot=\"append\">元/{{contractForm.salaryType === '按天' ? '天' : '月'}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\" v-if=\"contractForm.salaryType === '按工程量'\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"每(工程数量)\" prop=\"salaryPerUnit\">\r\n              <el-input v-model=\"contractForm.salaryPerUnit\" placeholder=\"请输入工程数量\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"计量单位\" prop=\"salaryUnitType\">\r\n              <el-select v-model=\"contractForm.salaryUnitType\" placeholder=\"请选择计量单位\" style=\"width: 100%\">\r\n                <el-option label=\"米\" value=\"80\"></el-option>\r\n                <el-option label=\"平方米\" value=\"81\"></el-option>\r\n                <el-option label=\"立方米\" value=\"82\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"计量单价\" prop=\"salaryUnitPrice\">\r\n              <el-input v-model=\"contractForm.salaryUnitPrice\" placeholder=\"请输入计量单价\">\r\n                <template slot=\"append\">元/{{getUnitTypeLabel(contractForm.salaryUnitType) || '单位'}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 显示合同附件 -->\r\n      <div class=\"contract-attachments\">\r\n        <h4>合同附件</h4>\r\n        \r\n        <!-- 添加手动上传附件按钮 -->\r\n        <div class=\"attachment-actions\">\r\n          <el-upload\r\n            class=\"upload-attachment\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            :on-change=\"handleAttachmentUpload\"\r\n            :before-upload=\"beforeAttachmentUpload\"\r\n            :multiple=\"false\">\r\n            <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">上传附件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">支持PDF、Word、Excel、图片等格式文件，单个文件不超过10MB</div>\r\n          </el-upload>\r\n        </div>\r\n        \r\n        <el-table v-if=\"contractForm.attachments.length > 0\" :data=\"contractForm.attachments\" style=\"width: 100%\">\r\n          <el-table-column label=\"文件名\" min-width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"file-info\">\r\n                <i :class=\"scope.row.icon || 'el-icon-document'\" class=\"file-icon\"></i>\r\n                <span class=\"file-name\">{{ scope.row.name }}</span>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"type\" label=\"类型\" width=\"150\"></el-table-column>\r\n          <el-table-column prop=\"size\" label=\"大小\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              {{ (scope.row.size / 1024).toFixed(2) }} KB\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button \r\n                size=\"mini\" \r\n                type=\"primary\" \r\n                icon=\"el-icon-view\"\r\n                @click=\"previewAttachment(scope.row)\">预览</el-button>\r\n              <el-button \r\n                size=\"mini\" \r\n                type=\"danger\" \r\n                icon=\"el-icon-delete\"\r\n                @click=\"removeAttachment(scope.$index)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div v-else class=\"no-attachments\">\r\n          暂无附件\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-actions\">\r\n        <el-button type=\"primary\" @click=\"submitContract\">提交合同</el-button>\r\n        <el-button @click=\"goBack\">返回</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\nimport { saveContractFile, getWorkerByIdCard, uploadContract } from '@/api/roster'\r\nimport jsPDF from 'jspdf'\r\nimport html2canvas from 'html2canvas'\r\n\r\nexport default {\r\n  name: 'ContractUpload',\r\n  data() {\r\n    return {\r\n      webSocket: null,\r\n      scannerConnected: false,\r\n      contractForm: {\r\n        workerId: '',\r\n        workerName: '',\r\n        idCardNumber: '',\r\n        ProjectName: '', // 项目名称\r\n        projectCode: '', // 项目编号\r\n        projectSubContractorId: '', // 参建单位ID\r\n        projectSubContractorName: '', // 参建单位\r\n        teamCode: '', // 班组编号\r\n        teamName: '', // 班组名称\r\n        contractPeriodType: '固定期限', // 合同期限类型\r\n        contractStartDate: new Date(), // 合同开始日期\r\n        contractEndDate: null, // 合同结束日期\r\n        contractSignDate: new Date(),\r\n        salaryType: '按天', // 工资类型\r\n        salaryUnitPrice: '', // 工资单价\r\n        salaryPerUnit: '', // 每(工程数量)\r\n        salaryUnitType: '', // 计量单位\r\n        remark: '',\r\n        contractFile: null,\r\n        attachments: [] // 合同附件数组\r\n      },\r\n      rules: {\r\n        contractSignDate: [\r\n          { required: true, message: '请选择签订日期', trigger: 'change' }\r\n        ],\r\n        workerName: [\r\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\r\n        ],\r\n        idCardNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' }\r\n        ],\r\n        projectSubContractorName: [\r\n          { required: true, message: '请输入参建单位', trigger: 'blur' }\r\n        ],\r\n        contractPeriodType: [\r\n          { required: true, message: '请选择合同期限类型', trigger: 'change' }\r\n        ],\r\n        contractStartDate: [\r\n          { required: true, message: '请选择合同开始日期', trigger: 'change' }\r\n        ],\r\n        salaryType: [\r\n          { required: true, message: '请选择工资类型', trigger: 'change' }\r\n        ],\r\n        salaryUnitPrice: [\r\n          { required: true, message: '请输入计量单价', trigger: 'blur' }\r\n        ]\r\n      },\r\n      simulationMode: false, // 模拟模式标志\r\n      connectionTimeout: null, // 连接超时\r\n      manuallyDisconnected: false, // 手动断开标志\r\n      scannerConfig: {\r\n        wsUrl: 'ws://localhost:1818', // WebSocket连接地址\r\n        timeout: 3000, // 连接超时时间(毫秒)\r\n        autoSimulate: true, // 连接失败时是否自动切换到模拟模式\r\n        ocrApiUrl: 'http://127.0.0.1:5000/ocr' // OCR API地址\r\n      },\r\n      configDialogVisible: false, // 高拍仪配置对话框可见性\r\n      tempScannerConfig: { // 临时存储的高拍仪配置\r\n        wsUrl: 'ws://localhost:1818',\r\n        timeout: 3000,\r\n        autoSimulate: true,\r\n        ocrApiUrl: 'http://127.0.0.1:5000/ocr'\r\n      },\r\n      currentPhotoPath: '', // 当前拍摄的照片路径\r\n      isProcessingUpload: false, // 是否正在处理上传\r\n      isProcessingOcr: false, // 是否正在处理OCR\r\n      photoList: [], // 拍摄的照片列表\r\n      idCardFound: false, // 是否已识别到身份证\r\n      isLoadingWorkerInfo: false, // 是否正在加载工人信息\r\n      isGeneratingPDF: false, // 是否正在生成PDF\r\n      currentImageData: '', // 当前接收到的图像数据\r\n      waitingForBase64: false // 是否正在等待 Base64Encode 的响应\r\n    }\r\n  },\r\n  created() {\r\n    // 从路由参数获取工人信息\r\n    const { workerId, workerName, idCardNumber } = this.$route.query\r\n    if (workerId && workerName && idCardNumber) {\r\n      this.contractForm.workerId = workerId\r\n      this.contractForm.workerName = workerName\r\n      this.contractForm.idCardNumber = idCardNumber\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化高拍仪WebSocket连接\r\n    this.initScannerWebSocket()\r\n  },\r\n  beforeDestroy() {\r\n    // 组件销毁前关闭WebSocket连接\r\n    this.closeWebSocket()\r\n  },\r\n  methods: {\r\n    // 处理工资类型变更\r\n    handleSalaryTypeChange(value) {\r\n      // 重置相关字段\r\n      this.contractForm.salaryUnitPrice = '';\r\n      \r\n      if (value === '按工程量') {\r\n        this.contractForm.salaryPerUnit = '';\r\n        this.contractForm.salaryUnitType = '';\r\n        \r\n        // 动态添加验证规则\r\n        this.$set(this.rules, 'salaryPerUnit', [\r\n          { required: true, message: '请输入工程数量', trigger: 'blur' }\r\n        ]);\r\n        this.$set(this.rules, 'salaryUnitType', [\r\n          { required: true, message: '请选择计量单位', trigger: 'change' }\r\n        ]);\r\n      } else {\r\n        // 移除验证规则\r\n        this.$delete(this.rules, 'salaryPerUnit');\r\n        this.$delete(this.rules, 'salaryUnitType');\r\n      }\r\n    },\r\n    \r\n    // 初始化高拍仪WebSocket连接\r\n    initScannerWebSocket() {\r\n      try {\r\n        // 添加模拟模式标志\r\n        this.simulationMode = false;\r\n        \r\n        // 尝试连接WebSocket\r\n        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)\r\n        \r\n        // 设置连接超时\r\n        this.connectionTimeout = setTimeout(() => {\r\n          if (!this.scannerConnected) {\r\n            console.warn('高拍仪连接超时，切换到模拟模式');\r\n            if (this.scannerConfig.autoSimulate) {\r\n              this.switchToSimulationMode();\r\n            } else {\r\n              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\r\n            }\r\n          }\r\n        }, this.scannerConfig.timeout);\r\n        \r\n        this.webSocket.onopen = (event) => {\r\n          console.log('高拍仪WebSocket连接成功')\r\n          this.scannerConnected = true\r\n          this.$message.success('高拍仪连接成功')\r\n          clearTimeout(this.connectionTimeout);\r\n        }\r\n        \r\n        this.webSocket.onclose = (event) => {\r\n          console.log('高拍仪WebSocket连接关闭')\r\n          this.scannerConnected = false\r\n          // 如果不是主动关闭，尝试切换到模拟模式\r\n          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {\r\n            this.switchToSimulationMode();\r\n          }\r\n        }\r\n        \r\n        this.webSocket.onerror = (event) => {\r\n          console.error('高拍仪WebSocket连接错误', event)\r\n          this.scannerConnected = false\r\n          if (this.scannerConfig.autoSimulate) {\r\n            this.switchToSimulationMode();\r\n          } else {\r\n            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\r\n          }\r\n        }\r\n        \r\n        this.webSocket.onmessage = (event) => {\r\n          this.handleScannerMessage(event)\r\n        }\r\n      } catch (error) {\r\n        console.error('初始化高拍仪WebSocket失败', error)\r\n        if (this.scannerConfig.autoSimulate) {\r\n          this.switchToSimulationMode();\r\n        } else {\r\n          this.$message.error(`初始化高拍仪失败: ${error.message}`);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理高拍仪消息\r\n    handleScannerMessage(event) {\r\n      const begin_data = \"data:image/jpeg;base64,\"\r\n      \r\n      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {\r\n        // 处理条码识别结果\r\n        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')\r\n        this.$message.success('识别到条码: ' + barcode)\r\n        \r\n        // 如果是身份证号格式，填入表单\r\n        if (this.isIdCardNumber(barcode)) {\r\n          this.contractForm.idCardNumber = barcode\r\n          this.searchWorkerByIdCard(barcode)\r\n        }\r\n      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {\r\n        // 处理保存图片结果\r\n        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')\r\n        this.$message.success('图片保存成功: ' + result)\r\n      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {\r\n        // 处理 Base64Encode 命令返回的 base64 数据\r\n        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')\r\n        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)\r\n        \r\n        // 重置等待标志\r\n        this.waitingForBase64 = false\r\n        \r\n        if (base64Data && base64Data.length > 1000) { // 确保数据有效\r\n          // 保存 base64 数据\r\n          this.currentImageData = begin_data + base64Data\r\n          \r\n          // 添加到照片列表\r\n          this.photoList.push({\r\n            imageData: this.currentImageData,\r\n            path: this.currentPhotoPath,\r\n            timestamp: new Date().getTime()\r\n          })\r\n          \r\n          // 使用高质量 base64 数据进行 OCR 识别\r\n          console.log('使用高质量 base64 数据进行 OCR 识别')\r\n          this.$message.info('正在识别证件号码...')\r\n          this.processOcrWithImage(this.currentImageData)\r\n        } else {\r\n          console.error('获取到的 base64 数据无效或太短')\r\n          \r\n          // 如果 base64 数据无效，尝试使用预览图或文件路径\r\n          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \r\n              this.$refs.scannerPreview.src.startsWith('data:image')) {\r\n            console.log('使用预览图数据进行 OCR 识别')\r\n            const imageData = this.$refs.scannerPreview.src\r\n            \r\n            // 添加到照片列表\r\n            this.photoList.push({\r\n              imageData: imageData,\r\n              path: this.currentPhotoPath,\r\n              timestamp: new Date().getTime()\r\n            })\r\n            \r\n            this.processOcrWithImage(imageData)\r\n          } else {\r\n            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\r\n            \r\n            // 添加到照片列表（使用空图像数据）\r\n            this.photoList.push({\r\n              imageData: '',\r\n              path: this.currentPhotoPath,\r\n              timestamp: new Date().getTime()\r\n            })\r\n            \r\n            this.processOcrWithImage(this.currentPhotoPath)\r\n          }\r\n        }\r\n      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {\r\n        // 处理目录检查结果\r\n        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')\r\n        console.log('目录检查结果:', result)\r\n        // 如果目录不存在，结果为\"0\"，存在则为\"1\"\r\n        if (result === \"0\") {\r\n          console.log('C:\\\\pic\\\\ 目录不存在，将创建')\r\n        }\r\n      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {\r\n        // 处理创建目录结果\r\n        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')\r\n        console.log('创建目录结果:', result)\r\n        // 如果创建成功，结果为\"1\"，失败则为\"0\"\r\n        if (result === \"1\") {\r\n          console.log('C:\\\\pic\\\\ 目录创建成功')\r\n        } else {\r\n          console.warn('C:\\\\pic\\\\ 目录创建失败或已存在')\r\n        }\r\n      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {\r\n        // 处理条码识别命令响应，不作为图像数据处理\r\n        console.log('收到条码识别命令响应:', event.data)\r\n      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {\r\n        // 处理图像数据 - 判断是否为base64图像数据\r\n        // 增加额外检查，确保不是条码数据\r\n        if (this.$refs.scannerPreview) {\r\n          try {\r\n            // 尝试验证是否为有效的base64图像数据\r\n            const testData = event.data.substring(0, 100); // 只取前100个字符测试\r\n            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常\r\n            \r\n            // 确保是完整的base64数据\r\n            const imgData = begin_data + event.data\r\n            this.$refs.scannerPreview.src = imgData\r\n            \r\n            // 保存当前图像数据以备后用\r\n            this.currentImageData = imgData\r\n            \r\n            // 保存当前图像数据到合同文件\r\n            this.contractForm.contractFile = imgData\r\n            console.log('成功保存图像数据，长度:', event.data.length)\r\n          } catch (e) {\r\n            console.error('收到的数据不是有效的base64图像:', e)\r\n          }\r\n        }\r\n      } else {\r\n        // 其他消息，可能是普通文本或命令响应\r\n        console.log('收到高拍仪消息:', event.data)\r\n      }\r\n    },\r\n    \r\n    // 启动高拍仪\r\n    startScanner() {\r\n      if (!this.scannerConnected) {\r\n        this.initScannerWebSocket()\r\n        return\r\n      }\r\n      \r\n      try {\r\n        // 设置分辨率\r\n        this.sendScannerCommand('vSetResolution(8)')\r\n        // 启用去黑边功能\r\n        this.sendScannerCommand('vSetDelHBFlag(true)')\r\n        // 启动主摄像头\r\n        this.sendScannerCommand('bStartPlay()')\r\n        this.$message.success('高拍仪已启动')\r\n      } catch (error) {\r\n        console.error('启动高拍仪失败', error)\r\n        this.$message.error('启动高拍仪失败')\r\n      }\r\n    },\r\n    \r\n    // 停止高拍仪\r\n    stopScanner() {\r\n      if (!this.scannerConnected) {\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.sendScannerCommand('bStopPlay()')\r\n        this.$message.success('高拍仪已停止')\r\n      } catch (error) {\r\n        console.error('停止高拍仪失败', error)\r\n        this.$message.error('停止高拍仪失败')\r\n      }\r\n    },\r\n    \r\n    // 扫描合同\r\n    scanContract() {\r\n      if (!this.scannerConnected && !this.simulationMode) {\r\n        this.$message.warning('请先启动高拍仪')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        if (this.simulationMode) {\r\n          // 模拟模式下，直接使用示例图片\r\n          this.processWithSimulationImage()\r\n          // 自动进行OCR识别\r\n          setTimeout(() => {\r\n            this.processOcrWithSimulationImage()\r\n          }, 1000)\r\n          return\r\n        }\r\n        \r\n        // 设置更高的分辨率 - 使用最高分辨率以确保合同完整清晰\r\n        // 分辨率值: 1=320*240, 2=640*480, 3=800*600, 4=1024*768, 5=1280*1024, 6=1600*1200, 7=2048*1536, 8=2592*1944\r\n        this.sendScannerCommand('vSetResolution(8)')\r\n        \r\n        // 确保启用去黑边功能\r\n        this.sendScannerCommand('vSetDelHBFlag(true)')\r\n        \r\n        // 设置A4文档模式 - 使用文档模式而不是证件模式\r\n        //this.sendScannerCommand('bSetMode(2)')\r\n        \r\n        // 设置自动裁剪模式 - 确保合同完整捕获\r\n        this.sendScannerCommand('vSetAutoCrop(true)')\r\n        \r\n        // 设置图像增强 - 提高清晰度\r\n        this.sendScannerCommand('vSetImageEnhance(true)')\r\n        \r\n        // 先检查目录是否存在，不存在则创建\r\n        this.sendScannerCommand('bDirIsExist(C:\\\\pic\\\\)')\r\n        \r\n        // 延迟一下，确保目录检查完成\r\n        setTimeout(() => {\r\n          // 创建目录（即使目录已存在，这个命令也不会报错）\r\n          this.sendScannerCommand('bCreateDir(C:\\\\pic\\\\)')\r\n          \r\n          // 生成唯一文件名（使用时间戳）\r\n          const timestamp = new Date().getTime()\r\n          const filename = `contract_${timestamp}`\r\n          this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\r\n          console.log('当前照片路径:', this.currentPhotoPath) \r\n          \r\n          // 拍照前提示用户\r\n          this.$message.info('正在拍摄合同，请确保文档完全平整并位于取景框内...')\r\n          \r\n          // 短暂延迟后拍照，给用户时间调整文档位置\r\n          setTimeout(() => {\r\n            // 拍照并保存到本地\r\n            this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\r\n            \r\n            // 清除之前的图像数据，确保不会使用旧数据\r\n            this.currentImageData = null\r\n            \r\n            this.$message.info('合同拍摄中，请稍候...')\r\n            \r\n            // 设置一个标志，表示我们正在等待 Base64Encode 的响应\r\n            this.waitingForBase64 = true\r\n            \r\n            // 延迟一下，确保图片保存完成\r\n            setTimeout(() => {\r\n              // 使用 Base64Encode 命令获取高质量的 base64 图像数据\r\n              this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)\r\n              \r\n              // 设置超时，确保即使没有收到 Base64Encode 的响应，也会继续处理\r\n              setTimeout(() => {\r\n                if (this.waitingForBase64) {\r\n                  console.log('Base64Encode 响应超时，使用备用方法')\r\n                  this.waitingForBase64 = false\r\n                  \r\n                  // 如果有预览图数据，使用预览图数据\r\n                  if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \r\n                      this.$refs.scannerPreview.src.startsWith('data:image')) {\r\n                    console.log('使用预览图数据')\r\n                    const imageData = this.$refs.scannerPreview.src\r\n                    \r\n                    // 添加到照片列表\r\n                    this.photoList.push({\r\n                      imageData: imageData,\r\n                      path: this.currentPhotoPath,\r\n                      timestamp: new Date().getTime()\r\n                    })\r\n                    \r\n                    // 调用OCR识别\r\n                    this.$message.info('正在识别证件号码...')\r\n                    this.processOcrWithImage(imageData)\r\n                  } else {\r\n                    // 如果没有图像数据，尝试使用文件路径\r\n                    console.log('使用文件路径:', this.currentPhotoPath)\r\n                    \r\n                    // 添加到照片列表（使用空图像数据）\r\n                    this.photoList.push({\r\n                      imageData: '',\r\n                      path: this.currentPhotoPath,\r\n                      timestamp: new Date().getTime()\r\n                    })\r\n                    \r\n                    // 调用OCR识别\r\n                    this.$message.info('正在识别证件号码...')\r\n                    this.processOcrWithImage(this.currentPhotoPath)\r\n                  }\r\n                }\r\n              }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法\r\n              \r\n            }, 1000) // 延迟1秒，确保图片保存完成\r\n          }, 500) // 给用户半秒钟时间调整文档位置\r\n        }, 500) // 延迟500ms，确保目录检查完成\r\n      } catch (error) {\r\n        console.error('扫描合同失败', error)\r\n        this.$message.error('扫描合同失败')\r\n      }\r\n    },\r\n    \r\n    // 模拟模式下使用示例图片\r\n    processWithSimulationImage() {\r\n      this.$message.info('模拟模式：请上传图片或使用高拍仪')\r\n      \r\n      // 设置空白图像\r\n      if (this.$refs.scannerPreview) {\r\n        // 创建一个空白的Canvas\r\n        const canvas = document.createElement('canvas')\r\n        canvas.width = 600\r\n        canvas.height = 400\r\n        const ctx = canvas.getContext('2d')\r\n        \r\n        // 填充浅灰色背景\r\n        ctx.fillStyle = '#f0f0f0'\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height)\r\n        \r\n        // 添加提示文字\r\n        ctx.fillStyle = '#666666'\r\n        ctx.font = '20px Arial'\r\n        ctx.textAlign = 'center'\r\n        ctx.fillText('请上传图片或使用高拍仪', canvas.width / 2, canvas.height / 2)\r\n        \r\n        // 转换为数据URL\r\n        const emptyImageUrl = canvas.toDataURL('image/jpeg')\r\n        this.$refs.scannerPreview.src = emptyImageUrl\r\n        this.contractForm.contractFile = emptyImageUrl\r\n      }\r\n    },\r\n    \r\n    // 发送高拍仪命令\r\n    sendScannerCommand(command) {\r\n      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {\r\n        this.webSocket.send(command)\r\n      } else {\r\n        throw new Error('WebSocket未连接')\r\n      }\r\n    },\r\n    \r\n    // 关闭WebSocket连接\r\n    closeWebSocket() {\r\n      if (this.webSocket) {\r\n        // 先停止高拍仪\r\n        if (this.scannerConnected) {\r\n          try {\r\n            this.webSocket.send('bStopPlay()')\r\n          } catch (e) {\r\n            console.error('停止高拍仪失败', e)\r\n          }\r\n        }\r\n        \r\n        // 关闭连接\r\n        this.webSocket.close()\r\n        this.webSocket = null\r\n        this.scannerConnected = false\r\n      }\r\n    },\r\n    \r\n    // 切换到模拟模式\r\n    switchToSimulationMode() {\r\n      this.simulationMode = true;\r\n      this.scannerConnected = false;\r\n      \r\n      if (this.webSocket) {\r\n        this.manuallyDisconnected = true;\r\n        this.webSocket.close();\r\n        this.webSocket = null;\r\n      }\r\n      \r\n      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动上传图片或使用模拟功能。');\r\n      \r\n      // 清除连接超时\r\n      if (this.connectionTimeout) {\r\n        clearTimeout(this.connectionTimeout);\r\n      }\r\n    },\r\n\r\n    // 上传图片处理函数\r\n    handleUploadImage(file) {\r\n      if (file) {\r\n        // 验证文件类型\r\n        const isImage = file.type.indexOf('image/') !== -1;\r\n        if (!isImage) {\r\n          this.$message.error('请上传图片文件!');\r\n          return false;\r\n        }\r\n        \r\n        // 验证文件大小 (限制为10MB)\r\n        const isLt10M = file.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n          this.$message.error('图片大小不能超过10MB!');\r\n          return false;\r\n        }\r\n        \r\n        this.$message.info('正在处理图片，请稍候...');\r\n        \r\n        // 更新预览图并压缩图片\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = (e) => {\r\n          // 压缩图片\r\n          this.compressImage(e.target.result, (compressedDataUrl) => {\r\n            // 更新预览图\r\n            if (this.$refs.scannerPreview) {\r\n              this.$refs.scannerPreview.src = compressedDataUrl;\r\n            }\r\n            \r\n            // 添加到照片列表\r\n            this.photoList.push({\r\n              imageData: compressedDataUrl,\r\n              path: '',\r\n              timestamp: new Date().getTime(),\r\n              isIdCard: false\r\n            });\r\n            \r\n            this.$message.info('图片上传成功，正在识别证件号码...');\r\n            \r\n            // 调用OCR识别\r\n            this.processOcrWithImage(compressedDataUrl);\r\n          });\r\n        };\r\n        \r\n        reader.onerror = (error) => {\r\n          console.error('读取图片文件失败', error);\r\n          this.$message.error('读取图片文件失败');\r\n        };\r\n      }\r\n      return false; // 阻止默认的上传行为\r\n    },\r\n    \r\n    // 压缩图片函数\r\n    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {\r\n      const img = new Image();\r\n      img.src = dataUrl;\r\n      \r\n      img.onload = () => {\r\n        // 创建Canvas\r\n        const canvas = document.createElement('canvas');\r\n        let width = img.width;\r\n        let height = img.height;\r\n        \r\n        // 计算缩放比例\r\n        if (width > height) {\r\n          if (width > maxWidth) {\r\n            height *= maxWidth / width;\r\n            width = maxWidth;\r\n          }\r\n        } else {\r\n          if (height > maxHeight) {\r\n            width *= maxHeight / height;\r\n            height = maxHeight;\r\n          }\r\n        }\r\n        \r\n        // 设置Canvas大小\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n        \r\n        // 绘制图像\r\n        const ctx = canvas.getContext('2d');\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n        \r\n        // 转换为压缩后的DataURL\r\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\r\n        \r\n        // 计算压缩率\r\n        const originalSize = dataUrl.length;\r\n        const compressedSize = compressedDataUrl.length;\r\n        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);\r\n        \r\n        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);\r\n        \r\n        callback(compressedDataUrl);\r\n      };\r\n      \r\n      img.onerror = () => {\r\n        console.error('图片压缩失败');\r\n        callback(dataUrl); // 失败时使用原始图片\r\n      };\r\n    },\r\n    \r\n    // 提交合同\r\n    submitContract() {\r\n      this.$refs.contractForm.validate(async valid => {\r\n        if (valid) {\r\n          if (this.contractForm.attachments.length === 0 && this.photoList.length === 0) {\r\n            this.$message.warning('请先拍摄或上传合同图片');\r\n            return;\r\n          }\r\n          \r\n          if (!this.idCardFound && !this.contractForm.idCardNumber) {\r\n            this.$message.warning('请先识别身份证或手动输入证件号码');\r\n            return;\r\n          }\r\n          \r\n          this.isProcessingUpload = true;\r\n          this.$message.info('正在上传合同，请稍候...');\r\n          \r\n          // 如果有拍摄的照片但还没有生成PDF，先生成PDF\r\n          if (this.photoList.length > 0 && this.contractForm.attachments.length === 0) {\r\n            this.convertImagesToPdf();\r\n            this.$message.info('请等待PDF生成完成后再提交');\r\n            this.isProcessingUpload = false;\r\n            return;\r\n          }\r\n          \r\n          // 打印提交前的contractForm，检查projectSubContractorId是否存在\r\n          console.log('提交前的contractForm:', JSON.stringify(this.contractForm))\r\n          console.log('参建单位ID (projectSubContractorId):', this.contractForm.projectSubContractorId)\r\n          \r\n          // 准备合同信息\r\n          const contract = {\r\n            corpCode: this.contractForm.projectSubContractorId || '',\r\n            corpName: this.contractForm.projectSubContractorName || '',\r\n            idCardType: '01', // 默认身份证\r\n            idNumber: this.contractForm.idCardNumber,\r\n            workerId: this.contractForm.workerId, // 工人ID\r\n            workerName: this.contractForm.workerName, // 工人姓名\r\n            teamNo: this.contractForm.teamCode, // 班组编号 \r\n            teamName: this.contractForm.teamName, // 班组名称\r\n            contractPeriodType: this.getSalaryTypeValue(this.contractForm.contractPeriodType),\r\n            startDate: this.formatDate(this.contractForm.contractStartDate),\r\n            endDate: this.formatDate(this.contractForm.contractEndDate),\r\n            signDate: this.formatDate(this.contractForm.contractSignDate || new Date()),\r\n            unit: this.getSalaryTypeValue(this.contractForm.salaryType), // 按日传1，按月传2，按工程量传3\r\n            unitPrice: Number(this.contractForm.salaryUnitPrice) || 100\r\n          };\r\n          \r\n          // 打印最终的contract对象，检查corpCode是否正确设置\r\n          console.log('最终的contract对象:', JSON.stringify(contract))\r\n          console.log('corpCode值:', contract.corpCode)\r\n          \r\n          // 如果是按工程量计算，添加每xx和计量单位信息\r\n          if (this.contractForm.salaryType === '按工程量') {\r\n            contract.perUnit = this.contractForm.salaryPerUnit || 1;\r\n            contract.unitType = this.contractForm.salaryUnitType || 80;\r\n          }\r\n          \r\n          // 准备附件信息\r\n          const attachments = [];\r\n          \r\n          try {\r\n            // 处理所有附件\r\n            if (this.contractForm.attachments && this.contractForm.attachments.length > 0) {\r\n              for (const attachment of this.contractForm.attachments) {\r\n                // 获取base64数据\r\n                let base64Data = '';\r\n                if (attachment.data) {\r\n                  if (typeof attachment.data === 'string' && attachment.data.startsWith('data:')) {\r\n                    // 已经是base64格式\r\n                    base64Data = attachment.data.split(',')[1]; // 移除\"data:application/pdf;base64,\"前缀\r\n                  } else if (attachment.file) {\r\n                    // 如果有base64属性，直接使用\r\n                    if (attachment.base64) {\r\n                      base64Data = attachment.base64.split(',')[1];\r\n                    } else {\r\n                      // 否则从文件读取\r\n                      base64Data = await this.readFileAsBase64Promise(attachment.file);\r\n                    }\r\n                  }\r\n                }\r\n                \r\n                attachments.push({\r\n                  name: attachment.name,\r\n                  data: base64Data\r\n                });\r\n              }\r\n            }\r\n            \r\n            // 准备请求数据\r\n            const requestData = {\r\n              projectCode: this.contractForm.projectCode,\r\n              contractList: [contract],\r\n              attachments: attachments\r\n            };\r\n            \r\n            // 打印最终的请求数据\r\n            console.log('上传合同的请求数据:', JSON.stringify({\r\n              projectCode: requestData.projectCode,\r\n              contractList: requestData.contractList,\r\n              attachmentsCount: requestData.attachments.length\r\n            }))\r\n            \r\n            // 调用上传API\r\n            uploadContract(requestData)\r\n              .then(response => {\r\n                if (response.code === 0) {\r\n                  this.$message.success('合同上传成功');\r\n                  // 返回上一页\r\n                  this.goBack();\r\n                } else {\r\n                  this.$message.error(response.message || '合同上传失败');\r\n                }\r\n              })\r\n              .catch(error => {\r\n                console.error('合同上传失败:', error);\r\n                this.$message.error('合同上传失败: ' + (error.message || '未知错误'));\r\n              })\r\n              .finally(() => {\r\n                this.isProcessingUpload = false;\r\n              });\r\n          } catch (error) {\r\n            console.error('处理附件失败:', error);\r\n            this.$message.error('处理附件失败: ' + (error.message || '未知错误'));\r\n            this.isProcessingUpload = false;\r\n          }\r\n        } else {\r\n          this.$message.warning('请完善合同信息');\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 读取文件为Base64的Promise版本\r\n    readFileAsBase64Promise(file) {\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onload = e => {\r\n          const base64Data = e.target.result.split(',')[1];\r\n          resolve(base64Data);\r\n        };\r\n        reader.onerror = e => reject(e);\r\n        reader.readAsDataURL(file);\r\n      });\r\n    },\r\n    \r\n    // 获取工资类型对应的值\r\n    getSalaryTypeValue(type) {\r\n      const typeMap = {\r\n        '按天': '1',\r\n        '按月': '2',\r\n        '按工程量': '3',\r\n        '固定期限': '0',\r\n        '无固定期限': '1',\r\n        '以完成一定工作任务为期限': '1'\r\n      };\r\n      return typeMap[type] || '1';\r\n    },\r\n    \r\n    // 获取工资类型对应的文本\r\n    getSalaryTypeText(type) {\r\n      const typeMap = {\r\n        '1': '按天',\r\n        '2': '按月',\r\n        '3': '按工程量',\r\n        '4': '其他方式'\r\n      };\r\n      return typeMap[type] || '按天';\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '';\r\n      const d = new Date(date);\r\n      const year = d.getFullYear();\r\n      const month = String(d.getMonth() + 1).padStart(2, '0');\r\n      const day = String(d.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    \r\n    // 预览附件\r\n    previewAttachment(attachment) {\r\n      if (attachment && attachment.data) {\r\n        // 判断文件类型\r\n        if (attachment.type && attachment.type.includes('image/')) {\r\n          // 图片类型 - 在弹窗中预览\r\n          this.$msgbox({\r\n            title: attachment.name,\r\n            message: `<div style=\"text-align:center;height:100%;width:100%;display:flex;align-items:center;justify-content:center;\">\r\n              <img src=\"${attachment.data}\" style=\"max-width:100%;max-height:100%;object-fit:contain;\" />\r\n            </div>`,\r\n            dangerouslyUseHTMLString: true,\r\n            customClass: 'image-preview-dialog pdf-preview-dialog', // 使用相同的最大化样式\r\n            showCancelButton: false,\r\n            showConfirmButton: false\r\n          });\r\n        } else if (attachment.type === 'application/pdf') {\r\n          try {\r\n            // PDF类型 - 创建Blob URL并使用对象标签预览\r\n            const pdfBlob = this.dataURLtoBlob(attachment.data);\r\n            const blobUrl = URL.createObjectURL(pdfBlob);\r\n            \r\n            // 使用MessageBox组件以最大化方式显示PDF\r\n            this.$msgbox({\r\n              title: attachment.name,\r\n              message: `<div style=\"height:100%;width:100%;\">\r\n                <object \r\n                  data=\"${blobUrl}\" \r\n                  type=\"application/pdf\" \r\n                  width=\"100%\" \r\n                  height=\"100%\"\r\n                  style=\"width:100%;height:100%;\">\r\n                    <p>您的浏览器不支持PDF预览，请 \r\n                      <a href=\"${blobUrl}\" download=\"${attachment.name}\">点击下载</a>\r\n                    </p>\r\n                </object>\r\n              </div>`,\r\n              dangerouslyUseHTMLString: true,\r\n              customClass: 'pdf-preview-dialog',\r\n              showCancelButton: false,\r\n              showConfirmButton: false,\r\n              beforeClose: (action, instance, done) => {\r\n                // 关闭对话框时释放blob URL\r\n                URL.revokeObjectURL(blobUrl);\r\n                done();\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error('预览PDF失败:', error);\r\n            this.$message.error('无法预览PDF文件: ' + error.message);\r\n            \r\n            // 如果有原始文件对象，尝试直接下载\r\n            if (attachment.file) {\r\n              const url = URL.createObjectURL(attachment.file);\r\n              const link = document.createElement('a');\r\n              link.href = url;\r\n              link.download = attachment.name;\r\n              link.click();\r\n              setTimeout(() => URL.revokeObjectURL(url), 100);\r\n            }\r\n          }\r\n        } else {\r\n          // 其他类型 - 尝试下载文件\r\n          try {\r\n            const link = document.createElement('a');\r\n            \r\n            // 检查是否是Blob URL\r\n            if (attachment.data instanceof Blob || attachment.data.startsWith('blob:')) {\r\n              link.href = attachment.data;\r\n            } else if (attachment.file) {\r\n              // 如果有原始文件对象，使用它创建URL\r\n              link.href = URL.createObjectURL(attachment.file);\r\n            } else {\r\n              // 尝试将data URL转换为Blob URL\r\n              const blob = this.dataURLtoBlob(attachment.data);\r\n              link.href = URL.createObjectURL(blob);\r\n            }\r\n            \r\n            link.download = attachment.name;\r\n            link.click();\r\n            \r\n            // 如果创建了Blob URL，需要释放\r\n            if (link.href.startsWith('blob:')) {\r\n              setTimeout(() => URL.revokeObjectURL(link.href), 100);\r\n            }\r\n          } catch (error) {\r\n            console.error('下载文件失败:', error);\r\n            this.$message.error('无法下载文件: ' + error.message);\r\n          }\r\n        }\r\n      } else {\r\n        this.$message.warning('无法预览该附件，附件数据不完整');\r\n      }\r\n    },\r\n    \r\n    // 将Data URL转换为Blob对象\r\n    dataURLtoBlob(dataURL) {\r\n      try {\r\n        // 检查是否是有效的data URL格式\r\n        if (!dataURL || typeof dataURL !== 'string' || !dataURL.includes(';base64,')) {\r\n          console.error('无效的Data URL格式:', dataURL);\r\n          throw new Error('无效的Data URL格式');\r\n        }\r\n        \r\n        // 分割Data URL，获取MIME类型和base64数据\r\n        const parts = dataURL.split(';base64,');\r\n        const contentType = parts[0].split(':')[1];\r\n        const raw = window.atob(parts[1]);\r\n        const rawLength = raw.length;\r\n        const uInt8Array = new Uint8Array(rawLength);\r\n        \r\n        // 将base64数据转换为Uint8Array\r\n        for (let i = 0; i < rawLength; ++i) {\r\n          uInt8Array[i] = raw.charCodeAt(i);\r\n        }\r\n        \r\n        return new Blob([uInt8Array], { type: contentType });\r\n      } catch (error) {\r\n        console.error('转换Data URL到Blob失败:', error);\r\n        throw new Error('转换Data URL到Blob失败: ' + error.message);\r\n      }\r\n    },\r\n    \r\n    // 删除附件\r\n    removeAttachment(index) {\r\n      this.$confirm('确定要删除此附件吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 释放URL对象\r\n        if (this.contractForm.attachments[index].data) {\r\n          URL.revokeObjectURL(this.contractForm.attachments[index].data);\r\n        }\r\n        // 从数组中删除\r\n        this.contractForm.attachments.splice(index, 1);\r\n        this.$message.success('附件已删除');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 扫描身份证\r\n    scanIdCard() {\r\n      if (!this.scannerConnected && !this.simulationMode) {\r\n        this.$message.warning('请先启动高拍仪')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        if (this.simulationMode) {\r\n          // 模拟模式下，直接调用OCR接口处理示例图片\r\n          this.processOcrWithSimulationImage()\r\n          return\r\n        }\r\n        \r\n        // 设置身份证自动寻边模式\r\n        this.sendScannerCommand('bSetMode(4)')\r\n        \r\n        // 生成唯一文件名（使用时间戳）\r\n        const timestamp = new Date().getTime()\r\n        const filename = `idcard_${timestamp}`\r\n        this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\r\n        console.log('当前照片路径:', this.currentPhotoPath) \r\n        \r\n        // 拍照并保存到本地\r\n        this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\r\n        \r\n        // 识别条码\r\n        this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)\r\n        \r\n        // 调用OCR识别\r\n        setTimeout(() => {\r\n          this.processOcrWithImage(this.currentPhotoPath)\r\n        }, 1000) // 延迟1秒，确保图片保存完成\r\n        \r\n        this.$message.info('正在识别身份证，请稍候...')\r\n      } catch (error) {\r\n        console.error('扫描身份证失败', error)\r\n        this.$message.error('扫描身份证失败')\r\n      }\r\n    },\r\n\r\n    // 处理OCR识别结果\r\n    processOcrWithImage(imagePath) {\r\n      if (this.isProcessingOcr) {\r\n        return\r\n      }\r\n      \r\n      this.isProcessingOcr = true\r\n      this.$message.info('正在进行OCR识别...')\r\n      \r\n      // 准备表单数据\r\n      const formData = new FormData()\r\n      \r\n      // 判断是否是base64格式的图片数据\r\n      if (imagePath.startsWith('data:image')) {\r\n        // 创建文件对象从base64数据\r\n        const base64Data = imagePath.split(',')[1]\r\n        \r\n        // 检查是否是从 Base64Encode 命令获取的高质量图像数据\r\n        const isHighQualityBase64 = this.currentImageData === imagePath && !this.waitingForBase64;\r\n        \r\n        if (isHighQualityBase64) {\r\n          // 如果是高质量 base64 数据，直接使用，不需要额外处理\r\n          console.log('使用高质量 base64 数据，跳过图像处理')\r\n          \r\n          const byteCharacters = atob(base64Data)\r\n          const byteArrays = []\r\n          \r\n          for (let i = 0; i < byteCharacters.length; i++) {\r\n            byteArrays.push(byteCharacters.charCodeAt(i))\r\n          }\r\n          \r\n          const byteArray = new Uint8Array(byteArrays)\r\n          const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n          \r\n          // 创建文件对象\r\n          const fileName = `contract_${new Date().getTime()}.jpg`\r\n          const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n          \r\n          // 添加到表单\r\n          formData.append('image', file)\r\n          console.log('发送高质量 base64 数据进行 OCR 识别')\r\n          \r\n          // 同时保留 base64 数据作为备用\r\n          formData.append('image_base64', imagePath)\r\n          \r\n          // 调用 OCR API\r\n          this.callOcrApi(formData)\r\n        } else {\r\n          // 在发送前处理图像，去除紫色边框和文字标记\r\n          this.preprocessImage(imagePath).then(processedImageData => {\r\n            // 使用处理后的图像数据\r\n            const processedBase64 = processedImageData.split(',')[1]\r\n            const byteCharacters = atob(processedBase64)\r\n            const byteArrays = []\r\n            \r\n            for (let i = 0; i < byteCharacters.length; i++) {\r\n              byteArrays.push(byteCharacters.charCodeAt(i))\r\n            }\r\n            \r\n            const byteArray = new Uint8Array(byteArrays)\r\n            const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n            \r\n            // 创建文件对象\r\n            const fileName = `contract_${new Date().getTime()}.jpg`\r\n            const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n            \r\n            // 添加到表单\r\n            formData.append('image', file)\r\n            console.log('发送处理后的文件对象进行 OCR 识别')\r\n            \r\n            // 同时保留处理后的base64数据作为备用\r\n            formData.append('image_base64', processedImageData)\r\n            \r\n            // 调用OCR API\r\n            this.callOcrApi(formData)\r\n          }).catch(error => {\r\n            console.error('图像预处理失败，使用原始图像:', error)\r\n            \r\n            // 如果处理失败，使用原始图像\r\n            const byteCharacters = atob(base64Data)\r\n            const byteArrays = []\r\n            \r\n            for (let i = 0; i < byteCharacters.length; i++) {\r\n              byteArrays.push(byteCharacters.charCodeAt(i))\r\n            }\r\n            \r\n            const byteArray = new Uint8Array(byteArrays)\r\n            const blob = new Blob([byteArray], { type: 'image/jpeg' })\r\n            \r\n            // 创建文件对象\r\n            const fileName = `contract_${new Date().getTime()}.jpg`\r\n            const file = new File([blob], fileName, { type: 'image/jpeg' })\r\n            \r\n            // 添加到表单\r\n            formData.append('image', file)\r\n            console.log('发送原始文件对象进行 OCR 识别')\r\n            \r\n            // 同时保留base64数据作为备用\r\n            formData.append('image_base64', imagePath)\r\n            \r\n            // 调用OCR API\r\n            this.callOcrApi(formData)\r\n          })\r\n        }\r\n      } else {\r\n        // 如果是文件路径，尝试读取文件并上传\r\n        formData.append('image_path', imagePath)\r\n        console.log(`发送图片路径进行OCR识别: ${imagePath}`)\r\n        \r\n        // 调用OCR API\r\n        this.callOcrApi(formData)\r\n      }\r\n    },\r\n    \r\n    // 调用OCR API\r\n    callOcrApi(formData) {\r\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\r\n        .then(response => {\r\n          this.handleOcrResult(response.data)\r\n        })\r\n        .catch(error => {\r\n          console.error('OCR识别失败', error)\r\n          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\r\n        })\r\n        .finally(() => {\r\n          this.isProcessingOcr = false\r\n        })\r\n    },\r\n    \r\n    // 图像预处理函数 - 去除紫色边框和文字标记\r\n    preprocessImage(base64Image) {\r\n      return new Promise((resolve, reject) => {\r\n        try {\r\n          // 创建图像对象\r\n          const img = new Image()\r\n          img.onload = () => {\r\n            // 创建Canvas\r\n            const canvas = document.createElement('canvas')\r\n            canvas.width = img.width\r\n            canvas.height = img.height\r\n            const ctx = canvas.getContext('2d')\r\n            \r\n            // 绘制图像到Canvas\r\n            ctx.drawImage(img, 0, 0)\r\n            \r\n            // 获取图像数据\r\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n            const data = imageData.data\r\n            \r\n            // 处理紫色边框 - 将紫色像素替换为白色\r\n            for (let i = 0; i < data.length; i += 4) {\r\n              const r = data[i]\r\n              const g = data[i + 1]\r\n              const b = data[i + 2]\r\n              \r\n              // 检测紫色范围 (RGB近似值)\r\n              // 紫色通常R和B较高，G较低\r\n              if ((r > 100 && r < 200) && (g < 100) && (b > 100 && b < 200)) {\r\n                // 将紫色替换为白色\r\n                data[i] = 255     // R\r\n                data[i + 1] = 255 // G\r\n                data[i + 2] = 255 // B\r\n              }\r\n            }\r\n            \r\n            // 将处理后的图像数据放回Canvas\r\n            ctx.putImageData(imageData, 0, 0)\r\n            \r\n            // 尝试去除文字标记 (简化处理)\r\n            // 这里使用简单的边缘检测和阈值处理\r\n            // 获取灰度图像\r\n            const grayImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n            const grayData = grayImageData.data\r\n            \r\n            // 转换为灰度图\r\n            for (let i = 0; i < grayData.length; i += 4) {\r\n              const avg = (grayData[i] + grayData[i + 1] + grayData[i + 2]) / 3\r\n              \r\n              // 应用阈值，将可能的文字区域变白\r\n              if (avg < 180) { // 较暗的区域可能是文字\r\n                // 检查周围像素，如果是小区域的暗像素，可能是文字\r\n                // 这是一个简化的处理，实际应用中可能需要更复杂的算法\r\n                grayData[i] = 255     // R\r\n                grayData[i + 1] = 255 // G\r\n                grayData[i + 2] = 255 // B\r\n              }\r\n            }\r\n            \r\n            // 将处理后的图像数据转换为base64\r\n            const processedBase64 = canvas.toDataURL('image/jpeg', 0.95)\r\n            \r\n            console.log('图像预处理完成')\r\n            resolve(processedBase64)\r\n          }\r\n          \r\n          img.onerror = (error) => {\r\n            console.error('图像加载失败:', error)\r\n            reject(error)\r\n          }\r\n          \r\n          // 设置图像源\r\n          img.src = base64Image\r\n        } catch (error) {\r\n          console.error('图像预处理失败:', error)\r\n          reject(error)\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 模拟模式下使用示例图片进行OCR识别\r\n    processOcrWithSimulationImage() {\r\n      this.isProcessingOcr = true\r\n      this.$message.info('模拟模式：正在进行OCR识别...')\r\n      \r\n      // 准备表单数据\r\n      const formData = new FormData()\r\n      formData.append('simulation', 'true')\r\n      \r\n      // 调用OCR API\r\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\r\n        .then(response => {\r\n          this.handleOcrResult(response.data)\r\n        })\r\n        .catch(error => {\r\n          console.error('模拟OCR识别失败', error)\r\n          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))\r\n        })\r\n        .finally(() => {\r\n          this.isProcessingOcr = false\r\n        })\r\n    },\r\n    \r\n    // 处理OCR识别结果\r\n    handleOcrResult(result) {\r\n      if (!result || !result.success) {\r\n        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))\r\n        return\r\n      }\r\n      \r\n      this.$message.success('OCR识别成功')\r\n      console.log('OCR识别结果:', result)\r\n      \r\n      // 更新表单数据\r\n      const ocrData = result.data || {}\r\n      \r\n      // 更新身份证号\r\n      if (ocrData.id_number) {\r\n        this.contractForm.idCardNumber = ocrData.id_number\r\n        // 添加更明显的提示\r\n        this.$notify({\r\n          title: '证件号码识别成功',\r\n          message: ocrData.id_number,\r\n          type: 'success',\r\n          duration: 5000\r\n        })\r\n        \r\n        // 标记已找到身份证\r\n        this.idCardFound = true\r\n        \r\n        // 如果有身份证号，尝试从系统中查询更多信息\r\n        this.searchWorkerByIdCard(ocrData.id_number)\r\n      }\r\n      \r\n      // 更新姓名\r\n      if (ocrData.name) {\r\n        this.contractForm.workerName = ocrData.name\r\n      }\r\n    },\r\n    \r\n    // 验证是否为身份证号\r\n    isIdCardNumber(str) {\r\n      // 简单验证18位或15位身份证号\r\n      const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\r\n      return reg.test(str)\r\n    },\r\n    \r\n    // 根据身份证号查询工人信息\r\n    searchWorkerByIdCard(idCardNumber) {\r\n      if (!idCardNumber) {\r\n        this.$message.warning('证件号码不能为空')\r\n        return\r\n      }\r\n      \r\n      this.isLoadingWorkerInfo = true\r\n      this.$message.info('正在查询工人信息...')\r\n      \r\n      // 调用API根据身份证号查询工人信息\r\n      getWorkerByIdCard(idCardNumber)\r\n        .then(response => {\r\n          if (response.code === 0 && response.data) {\r\n            const workerData = response.data\r\n            \r\n            // 打印API返回的完整数据，查看是否包含参建单位ID相关字段\r\n            console.log('API返回的工人信息:', workerData)\r\n            \r\n            // 更新表单数据\r\n            this.contractForm.workerId = workerData.workId || ''\r\n            this.contractForm.workerName = workerData.workerName || ''\r\n            \r\n            // 获取并保存班组编号和班组名称\r\n            this.contractForm.teamCode = workerData.teamNo || workerData.teamSysNo || ''\r\n            this.contractForm.teamName = workerData.teamName || workerData.teamSysName || ''\r\n            \r\n            // 填充其他工人信息\r\n            this.contractForm.ProjectName = workerData.projectName || ''\r\n            this.contractForm.projectCode = workerData.projectCode || ''\r\n            \r\n            // 尝试从多个可能的字段中获取参建单位ID\r\n            this.contractForm.projectSubContractorId = \r\n              workerData.projectSubContractorId || \r\n              workerData.corpCode || \r\n              workerData.participantCode || \r\n              workerData.corpId || \r\n              workerData.subContractorId || \r\n              '';\r\n            \r\n            console.log('可能的参建单位ID字段:',\r\n              '1.projectSubContractorId=', workerData.projectSubContractorId,\r\n              '2.corpCode=', workerData.corpCode,\r\n              '3.participantCode=', workerData.participantCode,\r\n              '4.corpId=', workerData.corpId,\r\n              '5.subContractorId=', workerData.subContractorId\r\n            );\r\n            \r\n            this.contractForm.projectSubContractorName = workerData.projectSubContractorName || workerData.corpName || ''\r\n            \r\n            // 打印保存后的projectSubContractorId\r\n            console.log('保存的参建单位ID:', this.contractForm.projectSubContractorId)\r\n            \r\n            this.contractForm.contractPeriodType = workerData.contractPeriodType || '固定期限'\r\n            this.contractForm.contractStartDate = workerData.contractStartDate ? new Date(workerData.contractStartDate) : new Date()\r\n            this.contractForm.contractEndDate = workerData.contractEndDate ? new Date(workerData.contractEndDate) : null\r\n            \r\n            // 处理工资类型 - 将数字转换为文本表示\r\n            console.log('原始工资类型值:', workerData.salaryType)\r\n            this.contractForm.salaryType = this.getSalaryTypeText(workerData.salaryType) || '按天'\r\n            console.log('转换后工资类型:', this.contractForm.salaryType)\r\n            \r\n            this.contractForm.salaryUnitPrice = workerData.salaryUnitPrice || ''\r\n            this.contractForm.salaryPerUnit = workerData.salaryPerUnit || ''\r\n            this.contractForm.salaryUnitType = workerData.salaryUnitType || ''\r\n            \r\n            // 根据工资类型更新验证规则\r\n            this.handleSalaryTypeChange(this.contractForm.salaryType)\r\n            \r\n            if (workerData.contractSignDate) {\r\n              this.contractForm.contractSignDate = new Date(workerData.contractSignDate)\r\n            }\r\n            \r\n            this.$message.success('工人信息查询成功')\r\n            \r\n            // 添加更详细的通知\r\n            this.$notify({\r\n              title: '工人信息查询成功',\r\n              message: `已找到工人: ${workerData.workerName}${this.contractForm.teamName ? ', 班组: ' + this.contractForm.teamName : ''}`,\r\n              type: 'success',\r\n              duration: 5000\r\n            })\r\n          } else {\r\n            this.$message.warning(response.msg || '未找到工人信息')\r\n            \r\n            // 如果没有找到，可以保留一些基本信息\r\n            if (this.contractForm.idCardNumber && !this.contractForm.workerName) {\r\n              // 从身份证号提取出生日期和性别信息\r\n              this.extractInfoFromIdCard(idCardNumber)\r\n            }\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error('查询工人信息失败:', error)\r\n          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))\r\n          \r\n          // 如果API调用失败，可以尝试从身份证号提取一些基本信息\r\n          if (this.contractForm.idCardNumber) {\r\n            this.extractInfoFromIdCard(idCardNumber)\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.isLoadingWorkerInfo = false\r\n        })\r\n    },\r\n    \r\n    // 从身份证号提取信息\r\n    extractInfoFromIdCard(idCardNumber) {\r\n      if (!idCardNumber || idCardNumber.length < 18) return\r\n      \r\n      try {\r\n        // 提取性别 (第17位，奇数为男，偶数为女)\r\n        const genderCode = parseInt(idCardNumber.charAt(16))\r\n        const gender = genderCode % 2 === 1 ? '男' : '女'\r\n        \r\n        console.log('从身份证号提取的性别:', gender)\r\n      } catch (e) {\r\n        console.error('从身份证号提取信息失败:', e)\r\n      }\r\n    },\r\n    \r\n    // 解析身份证号码 (示例，实际需要更复杂的OCR库)\r\n    parseIdCardNumber(base64Image) {\r\n      // 这是一个非常简化的示例，实际需要使用专业的OCR库（如Tesseract.js, PaddleOCR等）\r\n      // 这里只是模拟一个简单的解析逻辑\r\n      try {\r\n        const img = new Image();\r\n        img.src = 'data:image/jpeg;base64,' + base64Image;\r\n        img.onload = () => {\r\n          // 在实际应用中，这里会调用OCR库进行识别\r\n          // 例如：PaddleOCR.recognizeText(img.src);\r\n          // 假设识别结果包含身份证号码\r\n          const mockIdCardNumber = '123456789012345678'; // 模拟身份证号码\r\n          return mockIdCardNumber;\r\n        };\r\n      } catch (e) {\r\n        console.error('图片解析失败', e);\r\n        return null;\r\n      }\r\n    },\r\n\r\n    // 获取员工信息\r\n    fetchWorkerInfo(idCardNumber) {\r\n      // 这是一个模拟的API调用，实际需要一个真实的后端接口\r\n      // 例如：axios.get(`${this.apiBaseUrl}/api/workers/idCard/${idCardNumber}`)\r\n      // 假设成功获取到工人信息\r\n      const mockWorker = {\r\n        workerId: '', // 模拟工人ID\r\n        workerName: '', // 模拟工人姓名\r\n        idCardNumber: idCardNumber,\r\n        contractType: '',\r\n        contractSignDate: new Date(),\r\n        remark: ''\r\n      };\r\n\r\n      this.contractForm = { ...mockWorker }; // 更新合同表单\r\n      this.$message.success('已获取到工人信息！');\r\n    },\r\n\r\n    // 删除照片\r\n    removePhoto(index) {\r\n      this.$confirm('确定要删除此照片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.photoList.splice(index, 1);\r\n        this.$message.success('照片已删除');\r\n      }).catch(() => {\r\n        // 用户取消删除\r\n      });\r\n    },\r\n\r\n    // 刷新工人信息\r\n    refreshWorkerInfo() {\r\n      if (!this.contractForm.idCardNumber) {\r\n        this.$message.warning('请先输入证件号码')\r\n        return\r\n      }\r\n      \r\n      // 重新获取工人信息\r\n      this.searchWorkerByIdCard(this.contractForm.idCardNumber)\r\n    },\r\n\r\n    // 获取计量单位标签\r\n    getUnitTypeLabel(value) {\r\n      const unitTypeMap = {\r\n        '80': '米',\r\n        '81': '平方米',\r\n        '82': '立方米'\r\n      };\r\n      return unitTypeMap[value] || '单位';\r\n    },\r\n    \r\n    // 将图片转换为PDF\r\n    convertImagesToPdf() {\r\n      if (this.photoList.length === 0) {\r\n        this.$message.warning('请先拍摄或上传合同图片');\r\n        return;\r\n      }\r\n\r\n      this.isGeneratingPDF = true;\r\n      this.$message.info('正在生成PDF，请稍候...');\r\n\r\n      // 创建一个新的PDF文档\r\n      const pdf = new jsPDF('p', 'mm', 'a4');\r\n      const promises = [];\r\n      const pageWidth = pdf.internal.pageSize.getWidth();\r\n      const pageHeight = pdf.internal.pageSize.getHeight();\r\n\r\n      // 为每张图片创建一个Promise\r\n      this.photoList.forEach((photo, index) => {\r\n        const promise = new Promise((resolve) => {\r\n          // 优先使用高质量的 base64 图像数据\r\n          if (photo.imageData && photo.imageData.startsWith('data:image')) {\r\n            // 创建一个临时的图片元素\r\n            const img = new Image();\r\n            img.src = photo.imageData;\r\n            \r\n            img.onload = () => {\r\n              // 计算图片在PDF中的尺寸，保持宽高比\r\n              let imgWidth = pageWidth - 20; // 留出10mm边距\r\n              let imgHeight = (img.height * imgWidth) / img.width;\r\n              \r\n              // 如果图片高度超过页面高度，按高度缩放\r\n              if (imgHeight > pageHeight - 20) {\r\n                imgHeight = pageHeight - 20;\r\n                imgWidth = (img.width * imgHeight) / img.height;\r\n              }\r\n              \r\n              // 如果不是第一页，添加新页\r\n              if (index > 0) {\r\n                pdf.addPage();\r\n              }\r\n              \r\n              // 将图片添加到PDF\r\n              pdf.addImage(\r\n                photo.imageData, \r\n                'JPEG', \r\n                (pageWidth - imgWidth) / 2, // 居中显示\r\n                10, // 顶部边距\r\n                imgWidth, \r\n                imgHeight\r\n              );\r\n              \r\n              resolve();\r\n            };\r\n            \r\n            img.onerror = () => {\r\n              this.$message.error(`处理第${index + 1}张图片时出错`);\r\n              resolve(); // 即使出错也继续处理其他图片\r\n            };\r\n          } else if (photo.path && photo.path.startsWith('C:\\\\')) {\r\n            // 如果没有图像数据但有本地路径，尝试获取本地图片的 base64 数据\r\n            console.log(`尝试获取本地图片的 base64 数据: ${photo.path}`);\r\n            \r\n            // 使用 Base64Encode 命令获取图片的 base64 数据\r\n            if (this.scannerConnected) {\r\n              this.sendScannerCommand(`Base64Encode(${photo.path})`);\r\n              \r\n              // 设置一个超时，如果在指定时间内没有收到响应，则跳过该图片\r\n              setTimeout(() => {\r\n                if (!photo.imageData || !photo.imageData.startsWith('data:image')) {\r\n                  this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);\r\n                  resolve();\r\n                }\r\n              }, 3000);\r\n            } else {\r\n              this.$message.warning(`无法获取第${index + 1}张图片的数据，将跳过`);\r\n              resolve();\r\n            }\r\n          } else {\r\n            // 如果既没有图像数据也没有本地路径，跳过该图片\r\n            this.$message.warning(`第${index + 1}张图片没有有效数据，将跳过`);\r\n            resolve();\r\n          }\r\n        });\r\n        \r\n        promises.push(promise);\r\n      });\r\n\r\n      // 当所有图片处理完成后，保存PDF\r\n      Promise.all(promises).then(() => {\r\n        // 生成文件名\r\n        const workerName = this.contractForm.workerName || '未命名';\r\n        const idCardNumber = this.contractForm.idCardNumber || '';\r\n        const timestamp = new Date().getTime();\r\n        const fileName = `${workerName}_${idCardNumber}_合同_${timestamp}.pdf`;\r\n        \r\n        // 获取PDF文件的Blob对象\r\n        const pdfBlob = pdf.output('blob');\r\n        \r\n        // 创建附件对象\r\n        const blobUrl = URL.createObjectURL(pdfBlob);\r\n        const attachment = {\r\n          name: fileName,\r\n          type: 'application/pdf',\r\n          size: pdfBlob.size,\r\n          data: blobUrl,\r\n          file: new File([pdfBlob], fileName, { type: 'application/pdf' })\r\n        };\r\n        \r\n        // 添加到合同附件数组\r\n        this.contractForm.attachments.push(attachment);\r\n        \r\n        // 将生成的PDF设置为合同文件\r\n        this.contractForm.contractFile = attachment;\r\n        \r\n        // 获取PDF的base64数据，用于上传API\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(pdfBlob);\r\n        reader.onloadend = () => {\r\n          const base64data = reader.result;\r\n          // 保存base64数据到附件对象，便于后续上传\r\n          attachment.base64 = base64data;\r\n        };\r\n        \r\n        this.$message.success('PDF生成成功并已添加到合同附件');\r\n        this.isGeneratingPDF = false;\r\n      }).catch(error => {\r\n        console.error('生成PDF失败:', error);\r\n        this.$message.error('生成PDF失败: ' + (error.message || '未知错误'));\r\n        this.isGeneratingPDF = false;\r\n      });\r\n    },\r\n\r\n    // 手动上传附件\r\n    handleAttachmentUpload(file) {\r\n      // Element UI 的 upload 组件传入的是一个包含文件信息的对象\r\n      // 需要从中获取实际的文件对象\r\n      const actualFile = file.raw || file;\r\n      \r\n      if (actualFile) {\r\n        const isLt10M = actualFile.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n          this.$message.error('文件大小不能超过10MB!');\r\n          return false;\r\n        }\r\n\r\n        // 显示上传中提示\r\n        this.$message.info('正在处理文件，请稍候...');\r\n\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(actualFile);\r\n        reader.onload = (e) => {\r\n          // 获取文件类型图标\r\n          const fileIcon = this.getFileTypeIcon(actualFile.type);\r\n          \r\n          let fileData = e.target.result;\r\n          \r\n          // 确保fileData是有效的data URL格式\r\n          if (!fileData || typeof fileData !== 'string' || !fileData.includes(';base64,')) {\r\n            console.warn('文件数据不是有效的data URL格式，将使用Blob URL代替');\r\n            fileData = URL.createObjectURL(actualFile);\r\n          }\r\n          \r\n          // 创建附件对象\r\n          const attachment = {\r\n            name: actualFile.name,\r\n            type: actualFile.type,\r\n            size: actualFile.size,\r\n            data: fileData,\r\n            file: actualFile,\r\n            icon: fileIcon\r\n          };\r\n          \r\n          // 添加到附件列表\r\n          this.contractForm.attachments.push(attachment);\r\n          \r\n          // 如果是PDF，自动设置为合同文件\r\n          if (actualFile.type === 'application/pdf' && !this.contractForm.contractFile) {\r\n            this.contractForm.contractFile = attachment;\r\n            this.$message.success('已自动设置为合同文件');\r\n          } else {\r\n            this.$message.success('附件上传成功');\r\n          }\r\n        };\r\n        reader.onerror = (error) => {\r\n          console.error('读取文件失败', error);\r\n          this.$message.error('读取文件失败');\r\n        };\r\n      }\r\n      return false; // 阻止默认的上传行为\r\n    },\r\n\r\n    // 根据文件类型获取图标\r\n    getFileTypeIcon(fileType) {\r\n      if (fileType.includes('image/')) {\r\n        return 'el-icon-picture';\r\n      } else if (fileType === 'application/pdf') {\r\n        return 'el-icon-document';\r\n      } else if (fileType.includes('word') || fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\r\n        return 'el-icon-document-checked';\r\n      } else if (fileType.includes('excel') || fileType === 'application/vnd.ms-excel' || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {\r\n        return 'el-icon-tickets';\r\n      } else {\r\n        return 'el-icon-document';\r\n      }\r\n    },\r\n\r\n    // 手动上传附件前的验证\r\n    beforeAttachmentUpload(file) {\r\n      // 验证文件大小（限制为10MB）\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$message.error('文件大小不能超过10MB!');\r\n        return false;\r\n      }\r\n      \r\n      // 验证文件类型\r\n      const allowedTypes = [\r\n        'application/pdf', \r\n        'application/msword', \r\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n        'application/vnd.ms-excel',\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n        'image/jpeg',\r\n        'image/png',\r\n        'image/gif'\r\n      ];\r\n      \r\n      const isAllowedType = allowedTypes.includes(file.type) || file.type.startsWith('image/');\r\n      if (!isAllowedType) {\r\n        this.$message.error('只支持PDF、Word、Excel和图片格式!');\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contract-upload-container {\r\n  padding: 20px;\r\n}\r\n\r\n.contract-upload-container h3 {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  color: #303133;\r\n}\r\n\r\n.scanner-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.scanner-layout {\r\n  display: flex;\r\n  gap: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.scanner-left, .scanner-right {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.scanner-right {\r\n  border-left: 1px solid #ebeef5;\r\n  padding-left: 20px;\r\n  max-height: 700px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.scanner-preview {\r\n  width: 100%;\r\n  height: 400px;\r\n  margin-bottom: 20px;\r\n  border: 1px solid #dcdfe6;\r\n  background-color: #ebeef5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: hidden;\r\n}\r\n\r\n.scanner-preview img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n.scanner-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-button {\r\n  display: inline-block;\r\n  margin-left: 10px;\r\n}\r\n\r\n.scanner-right h4 {\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.photo-actions-top {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.photo-items {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.photo-item {\r\n  width: 180px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.photo-thumbnail {\r\n  width: 100%;\r\n  height: 120px;\r\n  object-fit: cover;\r\n}\r\n\r\n.photo-actions {\r\n  padding: 8px;\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.contract-info-form {\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.id-card-input {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.id-card-input .el-input {\r\n  flex: 1;\r\n}\r\n\r\n.refresh-button {\r\n  flex-shrink: 0;\r\n  margin-left: 10px;\r\n}\r\n\r\n.contract-attachments {\r\n  margin-top: 20px;\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.contract-attachments h4 {\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  border-bottom: 1px solid #ebeef5;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.attachment-actions {\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger {\r\n  width: 100%;\r\n  height: 100px;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  cursor: pointer;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.upload-attachment .el-upload-dragger .el-icon-upload {\r\n  font-size: 24px;\r\n  color: #8c939d;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.upload-attachment .el-upload__tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.no-attachments {\r\n  text-align: center;\r\n  color: #909399;\r\n  padding: 20px;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 20px;\r\n  color: #606266;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px; /* Adjust as needed */\r\n}\r\n\r\n/* 图片预览对话框样式已合并到PDF预览对话框样式中 */\r\n\r\n/* PDF预览对话框样式 */\r\n.pdf-preview-dialog {\r\n  width: 95% !important;\r\n  height: 95vh !important;\r\n  margin: 0 auto !important;\r\n  max-width: none !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box {\r\n  width: 95% !important;\r\n  max-width: none !important;\r\n  margin: 0 auto;\r\n  height: 95vh !important;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: fixed;\r\n  top: 2vh;\r\n  left: 2.5%;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__header {\r\n  padding: 10px 20px !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__content {\r\n  padding: 0 !important;\r\n  flex: 1;\r\n  overflow: hidden;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: calc(95vh - 55px) !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__message {\r\n  height: 100% !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n.pdf-preview-dialog .el-message-box__message p {\r\n  height: 100% !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n.pdf-preview-dialog object {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n\r\n/* 为了确保遮罩层正确显示 */\r\n.v-modal {\r\n  opacity: 0.6 !important;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .scanner-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .scanner-right {\r\n    border-left: none;\r\n    border-top: 1px solid #ebeef5;\r\n    padding-left: 0;\r\n    padding-top: 20px;\r\n    margin-top: 20px;\r\n  }\r\n  \r\n  .scanner-preview {\r\n    width: 100%;\r\n    height: 300px;\r\n  }\r\n  \r\n  .scanner-controls {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .upload-button {\r\n    margin-left: 0;\r\n    margin-top: 10px;\r\n  }\r\n  \r\n  .id-card-input {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .refresh-button {\r\n    margin-left: 0;\r\n    margin-top: 10px;\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}