<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5b89555a-84f6-4085-8ef9-19f9f6ccebb3" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/server-java/src/main/java/com/daka/pro/service/RosterServiceImpl.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/server-java/src/main/resources/application.properties" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2vMU0TOCOlBgfgNWnVf5uobxdQG" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/project/shop&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\java\com\daka\pro\crawler" />
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\java\com\daka\pro" />
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\java\com\daka\pro\crawler\api" />
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\java\com\daka\pro\service" />
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\java\com\daka\pro\mapper" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\resources\sql" />
      <recent name="C:\Users\<USER>\Desktop\project\daka\daka\server-java\src\main\resources" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.daka.pro.config" />
      <recent name="com.daka.pro.controller.admin" />
      <recent name="com.daka.pro.service" />
      <recent name="com.daka.pro.mapper" />
      <recent name="com.daka.pro.domain" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Application.TeamSalaryConfigServiceImpl">
    <configuration name="ApiClientDemo" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.daka.pro.crawler.ApiClientDemo" />
      <module name="server-java" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.daka.pro.crawler.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HttpUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.daka.pro.utils.HttpUtils" />
      <module name="server-java" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.daka.pro.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JwtUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.daka.pro.utils.JwtUtil" />
      <module name="server-java" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.daka.pro.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TeamSalaryConfigServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.daka.pro.service.TeamSalaryConfigServiceImpl" />
      <module name="server-java" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.daka.pro.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="server-java" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.daka.pro.Main" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.daka.pro.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.TeamSalaryConfigServiceImpl" />
        <item itemvalue="Spring Boot.Main" />
        <item itemvalue="Application.HttpUtils" />
        <item itemvalue="Application.JwtUtil" />
        <item itemvalue="Application.ApiClientDemo" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5b89555a-84f6-4085-8ef9-19f9f6ccebb3" name="Changes" comment="" />
      <created>1743954213286</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743954213286</updated>
      <workItem from="1743954214340" duration="40634000" />
      <workItem from="1744531145738" duration="17228000" />
      <workItem from="1744958278959" duration="6462000" />
      <workItem from="1745350117611" duration="4475000" />
      <workItem from="1745643891951" duration="27143000" />
      <workItem from="1746361503552" duration="81193000" />
      <workItem from="1747026449885" duration="26431000" />
      <workItem from="1747383745841" duration="20795000" />
      <workItem from="1748771857429" duration="48862000" />
      <workItem from="1749881418471" duration="14880000" />
      <workItem from="1750307458142" duration="14192000" />
      <workItem from="1750646013946" duration="113000" />
      <workItem from="1750646159615" duration="2462000" />
      <workItem from="1750841147283" duration="2119000" />
      <workItem from="1751171319419" duration="11747000" />
      <workItem from="1751428473018" duration="27974000" />
      <workItem from="1752396318415" duration="35365000" />
      <workItem from="1753065238023" duration="595000" />
      <workItem from="1753156823338" duration="1926000" />
      <workItem from="1753499877928" duration="595000" />
      <workItem from="1753668890898" duration="1295000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vite" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vue" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/server-java/src/main/java/com/daka/pro/utils/Test.java</url>
          <line>65</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/server-java/src/main/java/com/daka/pro/service/impl/TaskServiceImpl.java</url>
          <line>590</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>