{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep1.vue", "mtime": 1753349360417}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractStep1.vue"], "names": [], "mappings": ";AAwFA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ContractStep1.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\n  <div class=\"contract-step1-container\">\n    <h3>步骤1：工人身份信息识别</h3>\n    \n    <div class=\"scanner-container\">\n      <div class=\"scanner-preview\">\n        <img id=\"photo\" src=\"\" width=\"600\" height=\"400\" ref=\"scannerPreview\">\n      </div>\n      \n      <div class=\"scanner-controls\">\n        <el-button type=\"primary\" @click=\"startScanner\">启动高拍仪</el-button>\n        <el-button type=\"success\" @click=\"scanIdCard\">识别身份证</el-button>\n        <el-button type=\"warning\" @click=\"stopScanner\">停止高拍仪</el-button>\n        <el-upload\n          class=\"upload-button\"\n          action=\"#\"\n          :show-file-list=\"false\"\n          :before-upload=\"handleUploadImage\">\n          <el-button type=\"primary\" icon=\"el-icon-upload\">上传图片</el-button>\n        </el-upload>\n      </div>\n    </div>\n    \n    <div class=\"worker-info-form\">\n      <el-form :model=\"workerForm\" label-width=\"120px\" ref=\"workerForm\" :rules=\"rules\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\n              <el-input v-model=\"workerForm.workerName\" placeholder=\"请输入工人姓名\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\n              <div class=\"input-with-button\">\n              <el-input v-model=\"workerForm.idCardNumber\" placeholder=\"请输入证件号码\"></el-input>\n                <el-button type=\"primary\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refreshWorkerInfo\" :disabled=\"!workerForm.idCardNumber\" title=\"重新获取信息\">刷新</el-button>\n              </div>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"性别\" prop=\"gender\">\n              <el-select v-model=\"workerForm.gender\" placeholder=\"请选择性别\" style=\"width: 100%\">\n                <el-option label=\"男\" value=\"男\"></el-option>\n                <el-option label=\"女\" value=\"女\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"家庭住址\" prop=\"homeAddress\">\n              <el-input v-model=\"workerForm.homeAddress\" placeholder=\"请输入家庭住址\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"班组\" prop=\"teamName\">\n              <el-input v-model=\"workerForm.teamName\" placeholder=\"请输入班组名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"工种\" prop=\"jobPosition\">\n              <el-input v-model=\"workerForm.jobPosition\" placeholder=\"请输入工种\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"参建单位\" prop=\"participantName\">\n              <el-input v-model=\"workerForm.participantName\" placeholder=\"请输入参建单位\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      \n      <div class=\"form-actions\">\n        <el-button type=\"primary\" @click=\"searchWorker\">查找工人</el-button>\n        <el-button type=\"success\" @click=\"nextStep\">下一步</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getWorkerDetail, getWorkerByIdCard } from '@/api/roster'\nimport axios from 'axios'\n\nexport default {\n  name: 'ContractStep1',\n  props: {\n    workerInfo: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      webSocket: null,\n      scannerConnected: false,\n      workerForm: {\n        workerId: '',\n        workerName: '',\n        idCardNumber: '',\n        gender: '男',\n        homeAddress: '',\n        teamName: '',\n        teamCode: '',\n        participantName: '',\n        participantCode: '',\n        projectSubContractorId: '',\n        projectCode: '',\n        projectName: '',\n        jobPosition: ''\n      },\n      rules: {\n        workerName: [\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\n        ],\n        idCardNumber: [\n          { required: true, message: '请输入证件号码', trigger: 'blur' }\n        ],\n        homeAddress: [\n          { required: true, message: '请输入家庭住址', trigger: 'blur' }\n        ],\n        teamName: [\n          { required: true, message: '请输入班组名称', trigger: 'blur' }\n        ],\n        participantName: [\n          { required: true, message: '请输入参建单位', trigger: 'blur' }\n        ]\n      },\n      simulationMode: false, // 模拟模式标志\n      connectionTimeout: null, // 连接超时\n      manuallyDisconnected: false, // 手动断开标志\n      scannerConfig: {\n        wsUrl: 'ws://localhost:1818', // WebSocket连接地址\n        timeout: 3000, // 连接超时时间(毫秒)\n        autoSimulate: true, // 连接失败时是否自动切换到模拟模式\n        ocrApiUrl: '/ocr' // OCR API地址\n      },\n      configDialogVisible: false, // 高拍仪配置对话框可见性\n      tempScannerConfig: { // 临时存储的高拍仪配置\n        wsUrl: 'ws://localhost:1818',\n        timeout: 3000,\n        autoSimulate: true,\n        ocrApiUrl: '/ocr'\n      },\n      currentPhotoPath: '', // 当前拍摄的照片路径\n      isProcessingOcr: false, // 是否正在处理OCR\n      currentImageData: '', // 当前接收到的图像数据\n      waitingForBase64: false // 是否正在等待 Base64Encode 的响应\n    }\n  },\n  created() {\n    // 从props初始化表单\n    this.initFormFromProps()\n    \n    // 从URL获取项目编码\n    const { projectCode } = this.$route.query\n    if (projectCode) {\n      this.workerForm.projectCode = projectCode\n    }\n    \n    // 如果props中没有必要的数据，尝试从本地存储恢复\n    if (!this.workerForm.workerName && !this.workerForm.idCardNumber) {\n      this.tryRestoreFromLocalStorage(false) // 传false参数表示不要emit到父组件\n    }\n    \n    // 添加调试日志\n    console.log('ContractStep1 created - 初始化后的表单数据:', JSON.stringify(this.workerForm))\n  },\n  mounted() {\n    // 初始化高拍仪WebSocket连接\n    this.initScannerWebSocket()\n    \n    console.log('ContractStep1 mounted, 当前表单数据:', JSON.stringify(this.workerForm))\n  },\n  watch: {\n    // 监听workerInfo变化\n    workerInfo: {\n      handler(newVal, oldVal) {\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n          console.log('ContractStep1 - workerInfo变化:', JSON.stringify(newVal))\n          \n          // 更新表单数据\n          this.initFormFromProps()\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  beforeDestroy() {\n    // 组件销毁前关闭WebSocket连接\n    this.closeWebSocket()\n\n    // 只有当完全离开合同流程时才清空数据\n    // 不要在步骤之间导航时清空\n    console.log('ContractStep1 beforeDestroy - 当前路径:', this.$route?.path)\n    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {\n      console.log('ContractStep1 - 离开合同流程，清空数据')\n      this.clearContractData()\n    } else {\n      console.log('ContractStep1 - 在合同流程内导航，保留数据')\n      // 确保数据已保存到localStorage\n      this.saveToLocalStorage()\n    }\n  },\n  methods: {\n    // 清空合同数据\n    clearContractData() {\n      console.log('ContractStep1 - 清空合同数据')\n      localStorage.removeItem('contractWorkerInfo')\n    },\n    \n    // 尝试从本地存储恢复数据\n    tryRestoreFromLocalStorage(shouldEmit = true) {\n      try {\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\n        if (savedWorkerInfo) {\n          const parsedData = JSON.parse(savedWorkerInfo)\n          console.log('ContractStep1 - 从本地存储恢复数据:', JSON.stringify(parsedData))\n          \n          // 更新表单数据\n          Object.keys(this.workerForm).forEach(key => {\n            if (parsedData[key] !== undefined && parsedData[key] !== null) {\n              this.workerForm[key] = parsedData[key]\n            }\n          })\n          \n          // 只在需要时更新父组件数据\n          if (shouldEmit) {\n            this.$emit('update-worker-info', { ...parsedData })\n          }\n          \n          return true\n        }\n      } catch (error) {\n        console.error('ContractStep1 - 从本地存储恢复数据失败:', error)\n      }\n      return false\n    },\n    \n    // 从props初始化表单\n    initFormFromProps() {\n      if (this.workerInfo) {\n        console.log('ContractStep1 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))\n        \n        // 复制属性到表单，只复制有值的字段\n        Object.keys(this.workerForm).forEach(key => {\n          if (this.workerInfo[key] !== undefined && this.workerInfo[key] !== null && this.workerInfo[key] !== '') {\n            this.workerForm[key] = this.workerInfo[key]\n          }\n        })\n        \n        console.log('ContractStep1 - 从props初始化数据后:', JSON.stringify(this.workerForm))\n      }\n    },\n    \n    // 初始化高拍仪WebSocket连接\n    initScannerWebSocket() {\n      try {\n        // 添加模拟模式标志\n        this.simulationMode = false;\n        \n        // 尝试连接WebSocket\n        this.webSocket = new WebSocket(this.scannerConfig.wsUrl)\n        \n        // 设置连接超时\n        this.connectionTimeout = setTimeout(() => {\n          if (!this.scannerConnected) {\n            console.warn('高拍仪连接超时，切换到模拟模式');\n            if (this.scannerConfig.autoSimulate) {\n              this.switchToSimulationMode();\n            } else {\n              this.$message.error(`高拍仪连接超时，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\n            }\n          }\n        }, this.scannerConfig.timeout);\n        \n        this.webSocket.onopen = (event) => {\n          console.log('高拍仪WebSocket连接成功')\n          this.scannerConnected = true\n          this.$message.success('高拍仪连接成功')\n          clearTimeout(this.connectionTimeout);\n        }\n        \n        this.webSocket.onclose = (event) => {\n          console.log('高拍仪WebSocket连接关闭')\n          this.scannerConnected = false\n          // 如果不是主动关闭，尝试切换到模拟模式\n          if (!this.simulationMode && !this.manuallyDisconnected && this.scannerConfig.autoSimulate) {\n            this.switchToSimulationMode();\n          }\n        }\n        \n        this.webSocket.onerror = (event) => {\n          console.error('高拍仪WebSocket连接错误', event)\n          this.scannerConnected = false\n          if (this.scannerConfig.autoSimulate) {\n            this.switchToSimulationMode();\n          } else {\n            this.$message.error(`高拍仪连接失败，请检查设备是否已连接并且服务已启动(${this.scannerConfig.wsUrl})`);\n          }\n        }\n        \n        this.webSocket.onmessage = (event) => {\n          this.handleScannerMessage(event)\n        }\n      } catch (error) {\n        console.error('初始化高拍仪WebSocket失败', error)\n        if (this.scannerConfig.autoSimulate) {\n          this.switchToSimulationMode();\n        } else {\n          this.$message.error(`初始化高拍仪失败: ${error.message}`);\n        }\n      }\n    },\n    \n    // 处理高拍仪消息\n    handleScannerMessage(event) {\n      const begin_data = \"data:image/jpeg;base64,\"\n      \n      if (event.data.indexOf('BarCodeTransferBegin') >= 0) {\n        // 处理条码识别结果\n        const barcode = event.data.replace('BarCodeTransferBegin', '').replace('BarCodeTransferEnd', '')\n        this.$message.success('识别到条码: ' + barcode)\n        \n        // 如果是身份证号格式，填入表单\n        if (this.isIdCardNumber(barcode)) {\n          this.workerForm.idCardNumber = barcode\n          this.searchWorkerByIdCard(barcode)\n        }\n      } else if (event.data.indexOf('BeginbSaveJPG') >= 0) {\n        // 处理保存图片结果\n        const result = event.data.replace('BeginbSaveJPG', '').replace('EndbSaveJPG', '')\n        this.$message.success('图片保存成功: ' + result)\n      } else if (event.data.indexOf('BeginBase64Encode') >= 0) {\n        // 处理 Base64Encode 命令返回的 base64 数据\n        const base64Data = event.data.replace('BeginBase64Encode', '').replace('EndBase64Encode', '')\n        console.log('获取到高质量 base64 图像数据，长度:', base64Data.length)\n        \n        // 重置等待标志\n        this.waitingForBase64 = false\n        \n        if (base64Data && base64Data.length > 1000) { // 确保数据有效\n          // 保存 base64 数据\n          this.currentImageData = begin_data + base64Data\n          \n          // 使用高质量 base64 数据进行 OCR 识别\n          console.log('使用高质量 base64 数据进行 OCR 识别')\n          this.processOcrWithImage(this.currentImageData)\n        } else {\n          console.error('获取到的 base64 数据无效或太短')\n          \n          // 如果 base64 数据无效，尝试使用预览图或文件路径\n          if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \n              this.$refs.scannerPreview.src.startsWith('data:image')) {\n            console.log('使用预览图数据进行 OCR 识别')\n            this.processOcrWithImage(this.$refs.scannerPreview.src)\n          } else {\n            console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\n            this.processOcrWithImage(this.currentPhotoPath)\n          }\n        }\n      } else if (event.data.indexOf('BeginbDirIsExist') >= 0) {\n        // 处理目录检查结果\n        const result = event.data.replace('BeginbDirIsExist', '').replace('EndbDirIsExist', '')\n        console.log('目录检查结果:', result)\n        // 如果目录不存在，结果为\"0\"，存在则为\"1\"\n        if (result === \"0\") {\n          console.log('C:\\\\pic\\\\ 目录不存在，将创建')\n        }\n      } else if (event.data.indexOf('BeginbCreateDir') >= 0) {\n        // 处理创建目录结果\n        const result = event.data.replace('BeginbCreateDir', '').replace('EndbCreateDir', '')\n        console.log('创建目录结果:', result)\n        // 如果创建成功，结果为\"1\"，失败则为\"0\"\n        if (result === \"1\") {\n          console.log('C:\\\\pic\\\\ 目录创建成功')\n        } else {\n          console.warn('C:\\\\pic\\\\ 目录创建失败或已存在')\n        }\n      } else if (event.data.indexOf('BeginGetBarCodeEx') >= 0 || event.data.indexOf('EndGetBarCode') >= 0) {\n        // 处理条码识别命令响应，不作为图像数据处理\n        console.log('收到条码识别命令响应:', event.data)\n      } else if (event.data.startsWith('/9j/') || (event.data.length > 500 && !event.data.includes('GetBarCode'))) {\n        // 处理图像数据 - 判断是否为base64图像数据\n        // 增加额外检查，确保不是条码数据\n        if (this.$refs.scannerPreview) {\n          try {\n            // 尝试验证是否为有效的base64图像数据\n            const testData = event.data.substring(0, 100); // 只取前100个字符测试\n            window.atob(testData); // 尝试解码，如果不是有效的base64会抛出异常\n            \n            // 确保是完整的base64数据\n            const imgData = begin_data + event.data\n            this.$refs.scannerPreview.src = imgData\n            \n            // 保存当前图像数据以备后用\n            this.currentImageData = imgData\n            console.log('成功保存图像数据，长度:', event.data.length)\n          } catch (e) {\n            console.error('收到的数据不是有效的base64图像:', e)\n          }\n        }\n      } else {\n        // 其他消息，可能是普通文本或命令响应\n        console.log('收到高拍仪消息:', event.data)\n      }\n    },\n    \n    // 启动高拍仪\n    startScanner() {\n      if (!this.scannerConnected) {\n        this.initScannerWebSocket()\n        return\n      }\n      \n      try {\n        // 设置分辨率\n        this.sendScannerCommand('vSetResolution(8)')\n        \n        // 启用去黑边功能\n        this.sendScannerCommand('vSetDelHBFlag(true)')\n        \n        // 增加亮度控制\n        this.sendScannerCommand('vSetBrightness(80)') // 增加亮度\n        \n        // 增加对比度控制\n        this.sendScannerCommand('vSetContrast(70)') // 增加对比度\n        \n        // 设置曝光\n        this.sendScannerCommand('vSetExposure(60)') // 设置曝光\n        \n        // 启动主摄像头\n        this.sendScannerCommand('bStartPlay()')\n        this.$message.success('高拍仪已启动')\n      } catch (error) {\n        console.error('启动高拍仪失败', error)\n        this.$message.error('启动高拍仪失败')\n      }\n    },\n    \n    // 停止高拍仪\n    stopScanner() {\n      if (!this.scannerConnected) {\n        return\n      }\n      \n      try {\n        this.sendScannerCommand('bStopPlay()')\n        this.$message.success('高拍仪已停止')\n      } catch (error) {\n        console.error('停止高拍仪失败', error)\n        this.$message.error('停止高拍仪失败')\n      }\n    },\n    \n    // 扫描身份证\n    scanIdCard() {\n      if (!this.scannerConnected && !this.simulationMode) {\n        this.$message.warning('请先启动高拍仪')\n        return\n      }\n      \n      try {\n        if (this.simulationMode) {\n          // 模拟模式下，直接调用OCR接口处理示例图片\n          this.processOcrWithSimulationImage()\n          return\n        }\n        \n        // 确保启用去黑边功能\n        this.sendScannerCommand('vSetDelHBFlag(true)')\n        \n        // 设置身份证自动寻边模式\n        this.sendScannerCommand('bSetMode(4)')\n        \n        // 先检查目录是否存在，不存在则创建\n        this.sendScannerCommand('bDirIsExist(C:\\\\pic\\\\)')\n        \n        // 延迟一下，确保目录检查完成\n        setTimeout(() => {\n          // 创建目录（即使目录已存在，这个命令也不会报错）\n          this.sendScannerCommand('bCreateDir(C:\\\\pic\\\\)')\n          \n          // 生成唯一文件名（使用时间戳）\n          const timestamp = new Date().getTime()\n          const filename = `idcard_${timestamp}`\n          this.currentPhotoPath = `C:\\\\pic\\\\${filename}.jpg`\n          console.log('当前照片路径:', this.currentPhotoPath)\n          \n          // 拍照并保存到本地\n          this.sendScannerCommand(`bSaveJPG(C:\\\\pic\\\\,${filename})`)\n          \n          // 设置一个标志，表示我们正在等待 Base64Encode 的响应\n          this.waitingForBase64 = true\n          \n          // 延迟一下，确保图片保存完成\n          setTimeout(() => {\n            // 使用 Base64Encode 命令获取高质量的 base64 图像数据\n            this.sendScannerCommand(`Base64Encode(${this.currentPhotoPath})`)\n            \n            // 识别条码\n            this.sendScannerCommand(`sGetBarCodeEx(113662,${this.currentPhotoPath})`)\n            \n            // 清除之前的图像数据，确保不会使用旧数据\n            this.currentImageData = null\n            \n            // 设置超时，确保即使没有收到 Base64Encode 的响应，也会调用 OCR 接口\n            setTimeout(() => {\n              if (this.waitingForBase64) {\n                console.log('Base64Encode 响应超时，使用备用方法调用 OCR')\n                this.waitingForBase64 = false\n                \n                // 如果有预览图数据，使用预览图数据\n                if (this.$refs.scannerPreview && this.$refs.scannerPreview.src && \n                    this.$refs.scannerPreview.src.startsWith('data:image')) {\n                  console.log('使用预览图数据进行 OCR 识别')\n                  this.processOcrWithImage(this.$refs.scannerPreview.src)\n                } else {\n                  // 否则使用文件路径\n                  console.log('使用文件路径进行 OCR 识别:', this.currentPhotoPath)\n                  this.processOcrWithImage(this.currentPhotoPath)\n                }\n              }\n            }, 3000) // 等待3秒，如果还没收到 Base64Encode 的响应，就使用备用方法\n            \n          }, 1000) // 延迟1秒，确保图片保存完成\n        }, 500) // 延迟500ms，确保目录检查完成\n        \n        this.$message.info('正在识别身份证，请稍候...')\n      } catch (error) {\n        console.error('扫描身份证失败', error)\n        this.$message.error('扫描身份证失败')\n      }\n    },\n    \n         // 处理OCR识别结果\n    processOcrWithImage(imagePath) {\n      if (this.isProcessingOcr) {\n        return\n      }\n      \n      this.isProcessingOcr = true\n      this.$message.info('正在进行OCR识别...')\n      \n      // 判断是否是base64格式的图片数据\n      if (imagePath.startsWith('data:image')) {\n        // 对图片进行缩放处理，然后再进行OCR识别\n        this.scaleImageForOcr(imagePath).then(scaledImageData => {\n          console.log('图片已缩放处理，准备进行OCR识别')\n          \n          // 创建文件对象从base64数据\n          const base64Data = scaledImageData.split(',')[1]\n          \n          // 添加JSON格式的请求头\n          const config = {\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          }\n          \n          // 创建JSON数据\n          const jsonData = {\n            image: base64Data\n          }\n          \n          // 调用OCR API - 使用JSON格式发送\n          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)\n            .then(response => {\n              console.log('OCR API 响应:', response)\n              this.handleOcrResult(response.data)\n            })\n            .catch(error => {\n              console.error('OCR识别失败', error)\n              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n              \n              // 如果JSON格式失败，尝试使用表单数据\n              console.log('尝试使用表单数据格式重新发送请求')\n              \n              // 创建表单数据\n              const formData = new FormData()\n              const byteCharacters = atob(base64Data)\n              const byteArrays = []\n              \n              for (let i = 0; i < byteCharacters.length; i++) {\n                byteArrays.push(byteCharacters.charCodeAt(i))\n              }\n              \n              const byteArray = new Uint8Array(byteArrays)\n              const blob = new Blob([byteArray], { type: 'image/jpeg' })\n              \n              // 创建文件对象\n              const fileName = `idcard_${new Date().getTime()}.jpg`\n              const file = new File([blob], fileName, { type: 'image/jpeg' })\n              \n              // 添加到表单\n              formData.append('image', file)\n              this.callOcrApi(formData)\n            })\n            .finally(() => {\n              this.isProcessingOcr = false\n            })\n        }).catch(error => {\n          console.error('图像缩放处理失败:', error)\n          \n          // 如果缩放处理失败，直接使用原始图像\n          this.sendOriginalImage(imagePath)\n        })\n      } else {\n        // 如果是文件路径，尝试读取文件并上传\n        const formData = new FormData()\n        formData.append('image_path', imagePath)\n        console.log(`发送图片路径进行OCR识别: ${imagePath}`)\n        \n        // 调用OCR API\n        this.callOcrApi(formData)\n      }\n    },\n    \n    // 对图片进行缩放处理，只进行尺寸调整\n    scaleImageForOcr(imageDataUrl) {\n      return new Promise((resolve, reject) => {\n        try {\n          const img = new Image()\n          img.src = imageDataUrl\n          \n          img.onload = () => {\n            // 判断是否需要缩放\n            if (img.width <= 1000 && img.height <= 630) {\n              console.log('图片尺寸已经合适，无需缩放')\n              resolve(imageDataUrl)\n              return\n            }\n            \n            // 为OCR识别优化的尺寸，减小尺寸提高处理速度\n            // 身份证比例大约是1.58:1\n            const maxWidth = 1000  // 从1800减小到1000\n            const maxHeight = 630  // 从1140减小到630\n            \n            // 计算等比例缩放后的尺寸\n            let width = img.width\n            let height = img.height\n            \n            if (width > maxWidth) {\n              height = (height * maxWidth) / width\n              width = maxWidth\n            }\n            \n            if (height > maxHeight) {\n              width = (width * maxHeight) / height\n              height = maxHeight\n            }\n            \n            // 创建Canvas\n            const canvas = document.createElement('canvas')\n            canvas.width = width\n            canvas.height = height\n            const ctx = canvas.getContext('2d')\n            \n            // 绘制图像\n            ctx.drawImage(img, 0, 0, width, height)\n            \n            // 转换为适中质量JPEG，进一步减小文件大小\n            const scaledImageData = canvas.toDataURL('image/jpeg', 0.85)\n            \n            // 输出调试信息\n            console.log(`图片已缩放: 原始尺寸=${img.width}x${img.height}, 缩放尺寸=${width}x${height}`)\n            \n            resolve(scaledImageData)\n          }\n          \n          img.onerror = (error) => {\n            console.error('图像加载失败:', error)\n            reject(error)\n          }\n        } catch (e) {\n          console.error('图像缩放处理失败:', e)\n          reject(e)\n        }\n      })\n    },\n    \n    // 发送原始图像\n    sendOriginalImage(imagePath) {\n      try {\n        console.log('使用原始图像数据进行OCR识别')\n        \n        if (imagePath.startsWith('data:image')) {\n          // 创建文件对象从base64数据\n          const base64Data = imagePath.split(',')[1]\n          \n          // 添加JSON格式的请求头\n          const config = {\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          }\n          \n          // 创建JSON数据\n          const jsonData = {\n            image: base64Data\n          }\n          \n          // 使用JSON格式发送\n          axios.post(this.scannerConfig.ocrApiUrl, jsonData, config)\n            .then(response => {\n              console.log('OCR API 响应:', response)\n              this.handleOcrResult(response.data)\n            })\n            .catch(error => {\n              console.error('OCR识别失败', error)\n              this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n            })\n            .finally(() => {\n              this.isProcessingOcr = false\n            })\n        } else {\n          // 如果是文件路径\n          const formData = new FormData()\n          formData.append('image_path', imagePath)\n          this.callOcrApi(formData)\n        }\n      } catch (e) {\n        console.error('发送原始图像失败:', e)\n        this.isProcessingOcr = false\n        this.$message.error('发送图像失败: ' + e.message)\n      }\n    },\n    \n    // 模拟模式下使用示例图片进行OCR识别\n    processOcrWithSimulationImage() {\n      this.isProcessingOcr = true\n      this.$message.info('模拟模式：正在进行OCR识别...')\n      \n      // 准备表单数据\n      const formData = new FormData()\n      formData.append('simulation', 'true')\n      \n      // 调用OCR API\n      axios.post(this.scannerConfig.ocrApiUrl, formData)\n        .then(response => {\n          this.handleOcrResult(response.data)\n        })\n        .catch(error => {\n          console.error('模拟OCR识别失败', error)\n          this.$message.error('模拟OCR识别失败: ' + (error.response?.data?.message || error.message))\n        })\n        .finally(() => {\n          this.isProcessingOcr = false\n        })\n    },\n    \n    // 处理OCR识别结果\n    handleOcrResult(result) {\n      if (!result || !result.success) {\n        this.$message.error('OCR识别失败: ' + (result?.message || '未知错误'))\n        return\n      }\n      \n      this.$message.success('OCR识别成功')\n      console.log('OCR识别结果:', result)\n      \n      // 更新表单数据\n      const ocrData = result.data || {}\n      \n      // 保存OCR识别的地址，以便在API查询后仍能使用\n      const ocrAddress = ocrData.address || ''\n      \n      // 更新身份证号\n      if (ocrData.id_number) {\n        this.workerForm.idCardNumber = ocrData.id_number\n        // 添加更明显的提示\n        this.$notify({\n          title: '证件号码识别成功',\n          type: 'success',\n          duration: 5000\n        })\n      }\n      \n      // 更新姓名\n      if (ocrData.name) {\n        this.workerForm.workerName = ocrData.name\n      }\n      \n      // 更新性别\n      if (ocrData.gender) {\n        this.workerForm.gender = ocrData.gender\n      }\n      \n      // 更新地址\n      if (ocrData.address) {\n        this.workerForm.homeAddress = ocrData.address\n        // 添加地址识别成功的提示\n        this.$notify({\n          title: '地址识别成功',\n          message: `识别到地址: ${ocrData.address}`,\n          type: 'success',\n          duration: 5000\n        })\n      }\n      \n      // 如果有身份证号，尝试从系统中查询更多信息\n      if (ocrData.id_number) {\n        // 传递OCR识别的地址作为参数\n        this.searchWorkerByIdCard(ocrData.id_number, ocrAddress)\n      }\n    },\n    \n    // 发送高拍仪命令\n    sendScannerCommand(command) {\n      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {\n        this.webSocket.send(command)\n      } else {\n        throw new Error('WebSocket未连接')\n      }\n    },\n    \n    // 关闭WebSocket连接\n    closeWebSocket() {\n      if (this.webSocket) {\n        // 先停止高拍仪\n        if (this.scannerConnected) {\n          try {\n            this.webSocket.send('bStopPlay()')\n          } catch (e) {\n            console.error('停止高拍仪失败', e)\n          }\n        }\n        \n        // 关闭连接\n        this.webSocket.close()\n        this.webSocket = null\n        this.scannerConnected = false\n      }\n    },\n    \n    // 验证是否为身份证号\n    isIdCardNumber(str) {\n      // 简单验证18位或15位身份证号\n      const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\n      return reg.test(str)\n    },\n    \n    // 根据身份证号查询工人信息\n    searchWorkerByIdCard(idCardNumber, addressFromOcr = '') {\n      if (!idCardNumber) {\n        this.$message.warning('证件号码不能为空')\n        return\n      }\n      \n      this.$message.info('正在查询工人信息...')\n      \n      // 调用API根据身份证号查询工人信息\n      getWorkerByIdCard(idCardNumber)\n        .then(response => {\n          if (response.code === 0 && response.data) {\n            const workerData = response.data\n            \n            // 更新表单数据\n            this.workerForm.workerId = workerData.id || ''\n            this.workerForm.workerName = workerData.workerName || ''\n            this.workerForm.gender = workerData.gender || '男'\n            this.workerForm.homeAddress = workerData.homeAddress || ''\n            this.workerForm.teamName = workerData.teamSysName || ''\n            this.workerForm.teamCode = workerData.teamSysNo || ''\n            this.workerForm.jobPosition = workerData.workerType || ''\n            this.workerForm.participantName = workerData.projectSubContractorName || workerData.corpName || ''\n            this.workerForm.participantCode = workerData.corpCode || ''\n            this.workerForm.projectSubContractorId = workerData.projectSubContractorId || workerData.corpId || ''\n            this.workerForm.projectCode = workerData.projectCode || this.workerForm.projectCode\n            this.workerForm.projectName = workerData.projectName || ''\n            \n            // 如果API没有返回地址，但OCR识别到了地址，则使用OCR识别的地址\n            if ((!workerData.homeAddress || workerData.homeAddress === '') && addressFromOcr) {\n              console.log('使用OCR识别的地址:', addressFromOcr)\n              this.workerForm.homeAddress = addressFromOcr\n            }\n            \n            // 保存到本地存储\n            this.saveToLocalStorage()\n        \n            this.$message.success('工人信息查询成功')\n            \n            // 添加更详细的通知\n            this.$notify({\n              title: '工人信息查询成功',\n              message: `已找到工人: ${workerData.workerName}，所属班组: ${workerData.teamSysName || '未知'}`,\n              type: 'success',\n              duration: 5000\n            })\n          } else {\n            this.$message.warning(response.msg || '未找到工人信息')\n            \n            // 如果没有找到，可以保留一些基本信息\n            if (this.workerForm.idCardNumber && !this.workerForm.workerName) {\n              // 从身份证号提取出生日期和性别信息\n              this.extractInfoFromIdCard(idCardNumber)\n            }\n            \n            // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址\n            if (addressFromOcr) {\n              console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)\n              this.workerForm.homeAddress = addressFromOcr\n            }\n          }\n        })\n        .catch(error => {\n          console.error('查询工人信息失败:', error)\n          this.$message.error('查询工人信息失败: ' + (error.message || '未知错误'))\n          \n          // 如果API调用失败，可以尝试从身份证号提取一些基本信息\n          if (this.workerForm.idCardNumber) {\n            this.extractInfoFromIdCard(idCardNumber)\n          }\n          \n          // 如果API查询失败但OCR识别到了地址，则使用OCR识别的地址\n          if (addressFromOcr) {\n            console.log('API查询失败，使用OCR识别的地址:', addressFromOcr)\n            this.workerForm.homeAddress = addressFromOcr\n          }\n        })\n    },\n    \n    // 从身份证号提取信息\n    extractInfoFromIdCard(idCardNumber) {\n      if (!idCardNumber || idCardNumber.length < 18) return\n      \n      try {\n        // 提取性别 (第17位，奇数为男，偶数为女)\n        const genderCode = parseInt(idCardNumber.charAt(16))\n        this.workerForm.gender = genderCode % 2 === 1 ? '男' : '女'\n        \n        // 可以添加更多提取逻辑，如出生日期等\n        console.log('从身份证号提取的性别:', this.workerForm.gender)\n      } catch (e) {\n        console.error('从身份证号提取信息失败:', e)\n      }\n    },\n    \n    // 查找工人\n    searchWorker() {\n      if (!this.workerForm.idCardNumber) {\n        this.$message.warning('请先输入证件号码')\n        return\n      }\n      \n      // 根据证件号码查询工人信息\n      this.searchWorkerByIdCard(this.workerForm.idCardNumber)\n    },\n    \n    // 刷新工人信息\n    refreshWorkerInfo() {\n      if (this.workerForm.idCardNumber) {\n        this.$message.info('正在重新获取工人信息...')\n        this.searchWorkerByIdCard(this.workerForm.idCardNumber)\n      } else {\n        this.$message.warning('请先输入证件号码')\n      }\n    },\n    \n    // 保存数据到本地存储\n    saveToLocalStorage() {\n      try {\n        // 合并表单数据和已有数据\n        let existingData = {}\n        try {\n          const savedData = localStorage.getItem('contractWorkerInfo')\n          if (savedData) {\n            existingData = JSON.parse(savedData)\n          }\n        } catch (e) {}\n        \n        const dataToSave = { \n          ...existingData,\n          ...this.workerForm \n        }\n        \n        console.log('ContractStep1 保存数据到本地存储:', JSON.stringify(dataToSave))\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave))\n        return dataToSave\n      } catch (error) {\n        console.error('保存到本地存储失败:', error)\n        return this.workerForm\n      }\n    },\n    \n    // 下一步\n    nextStep() {\n      this.$refs.workerForm.validate(valid => {\n        if (valid) {\n          // 关闭高拍仪\n          if (this.scannerConnected) {\n            this.stopScanner();\n            this.closeWebSocket();\n            console.log('下一步操作：已自动关闭高拍仪');\n          }\n          \n          // 添加详细的调试日志\n          console.log('=== ContractStep1 nextStep 开始 ===')\n          console.log('ContractStep1 当前表单数据:', JSON.stringify(this.workerForm))\n\n          // 保存到本地存储并获取完整数据\n          const completeData = this.saveToLocalStorage()\n          console.log('ContractStep1 保存到localStorage的完整数据:', JSON.stringify(completeData))\n\n          // 准备一个包含所有必要字段的数据对象\n          const dataToUpdate = {\n            ...completeData,\n            // 确保这些重要字段一定会被传递\n            workerName: this.workerForm.workerName,\n            idCardNumber: this.workerForm.idCardNumber,\n            gender: this.workerForm.gender,\n            homeAddress: this.workerForm.homeAddress,\n            teamName: this.workerForm.teamName,\n            jobPosition: this.workerForm.jobPosition,\n            participantName: this.workerForm.participantName\n          }\n\n          console.log('ContractStep1 准备发送给父组件的数据:', JSON.stringify(dataToUpdate))\n          console.log('ContractStep1 关键字段检查:')\n          console.log('- workerName:', dataToUpdate.workerName)\n          console.log('- idCardNumber:', dataToUpdate.idCardNumber)\n\n          // 先更新父组件中的工人信息\n          this.$emit('update-worker-info', dataToUpdate)\n          console.log('ContractStep1 已发送update-worker-info事件')\n          \n          // 等待数据更新后再触发导航\n          this.$nextTick(() => {\n            // 发送下一步事件，让父组件处理导航\n            this.$emit('next-step')\n          })\n        } else {\n          this.$message.warning('请完善工人信息')\n        }\n      })\n    },\n    \n    // 切换到模拟模式\n    switchToSimulationMode() {\n      this.simulationMode = true;\n      this.scannerConnected = false;\n      \n      if (this.webSocket) {\n        this.manuallyDisconnected = true;\n        this.webSocket.close();\n        this.webSocket = null;\n      }\n      \n      this.$message.warning('高拍仪连接失败，已切换到模拟模式。您可以手动输入信息或使用模拟识别功能。');\n      \n      // 清除连接超时\n      if (this.connectionTimeout) {\n        clearTimeout(this.connectionTimeout);\n      }\n    },\n\n    // 上传图片处理函数\n    handleUploadImage(file) {\n      if (file) {\n        // 验证文件类型\n        const isImage = file.type.indexOf('image/') !== -1;\n        if (!isImage) {\n          this.$message.error('请上传图片文件!');\n          return false;\n        }\n        \n        // 验证文件大小 (限制为10MB)\n        const isLt10M = file.size / 1024 / 1024 < 10;\n        if (!isLt10M) {\n          this.$message.error('图片大小不能超过10MB!');\n          return false;\n        }\n        \n        this.$message.info('正在处理图片，请稍候...');\n        \n        // 更新预览图并压缩图片\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = (e) => {\n          // 压缩图片\n          this.compressImage(e.target.result, (compressedDataUrl) => {\n            // 更新预览图\n            if (this.$refs.scannerPreview) {\n              this.$refs.scannerPreview.src = compressedDataUrl;\n            }\n            \n            // 保存图片路径并处理OCR\n            this.currentPhotoPath = compressedDataUrl;\n            this.$message.info('正在识别上传的图片，请稍候...');\n            \n            // 调用OCR识别\n            setTimeout(() => {\n              this.processOcrWithImage(this.currentPhotoPath);\n            }, 300); // 短暂延迟，确保UI更新\n          });\n        };\n        \n        reader.onerror = (error) => {\n          console.error('读取图片文件失败', error);\n          this.$message.error('读取图片文件失败');\n        };\n      }\n      return false; // 阻止默认的上传行为\n    },\n    \n    // 压缩图片函数\n    compressImage(dataUrl, callback, maxWidth = 1200, maxHeight = 1200, quality = 0.7) {\n      const img = new Image();\n      img.src = dataUrl;\n      \n      img.onload = () => {\n        // 创建Canvas\n        const canvas = document.createElement('canvas');\n        let width = img.width;\n        let height = img.height;\n        \n        // 计算缩放比例\n        if (width > height) {\n          if (width > maxWidth) {\n            height *= maxWidth / width;\n            width = maxWidth;\n          }\n        } else {\n          if (height > maxHeight) {\n            width *= maxHeight / height;\n            height = maxHeight;\n          }\n        }\n        \n        // 设置Canvas大小\n        canvas.width = width;\n        canvas.height = height;\n        \n        // 绘制图像\n        const ctx = canvas.getContext('2d');\n        ctx.drawImage(img, 0, 0, width, height);\n        \n        // 转换为压缩后的DataURL\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);\n        \n        // 计算压缩率\n        const originalSize = dataUrl.length;\n        const compressedSize = compressedDataUrl.length;\n        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);\n        \n        console.log(`图片已压缩: 原始大小=${(originalSize/1024/1024).toFixed(2)}MB, 压缩后大小=${(compressedSize/1024/1024).toFixed(2)}MB, 压缩率=${compressionRatio}%`);\n        \n        callback(compressedDataUrl);\n      };\n      \n      img.onerror = () => {\n        console.error('图片压缩失败');\n        callback(dataUrl); // 失败时使用原始图片\n      };\n    },\n\n    // 增强图像质量\n    enhanceImage(imageDataUrl) {\n      return new Promise((resolve, reject) => {\n        try {\n          const img = new Image();\n          img.src = imageDataUrl;\n          \n          img.onload = () => {\n            // 创建Canvas\n            const canvas = document.createElement('canvas');\n            canvas.width = img.width;\n            canvas.height = img.height;\n            const ctx = canvas.getContext('2d');\n            \n            // 绘制原始图像\n            ctx.drawImage(img, 0, 0, img.width, img.height);\n            \n            // 1. 自动裁剪 - 找到身份证区域\n            this.cropIdCard(canvas, ctx);\n            \n            // 2. 亮度和对比度增强\n            this.adjustBrightnessContrast(canvas, ctx, 50, 80); // 增加亮度和对比度\n            \n            // 3. 锐化处理\n            this.sharpenImage(canvas, ctx);\n            \n            // 返回处理后的图像\n            resolve(canvas.toDataURL('image/jpeg', 0.95)); // 使用高质量\n          };\n          \n          img.onerror = (error) => {\n            console.error('图像加载失败:', error);\n            reject(error);\n          };\n        } catch (e) {\n          console.error('图像增强处理失败:', e);\n          reject(e);\n        }\n      });\n    },\n    \n    // 裁剪身份证区域\n    cropIdCard(canvas, ctx) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        \n        // 查找非黑色区域边界\n        let minX = canvas.width, minY = canvas.height, maxX = 0, maxY = 0;\n        const threshold = 40; // 亮度阈值\n        \n        for (let y = 0; y < canvas.height; y++) {\n          for (let x = 0; x < canvas.width; x++) {\n            const idx = (y * canvas.width + x) * 4;\n            const brightness = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;\n            \n            if (brightness > threshold) {\n              minX = Math.min(minX, x);\n              minY = Math.min(minY, y);\n              maxX = Math.max(maxX, x);\n              maxY = Math.max(maxY, y);\n            }\n          }\n        }\n        \n        // 添加边距\n        const padding = 10;\n        minX = Math.max(0, minX - padding);\n        minY = Math.max(0, minY - padding);\n        maxX = Math.min(canvas.width, maxX + padding);\n        maxY = Math.min(canvas.height, maxY + padding);\n        \n        // 检查是否找到了有效区域\n        const width = maxX - minX;\n        const height = maxY - minY;\n        \n        if (width > 50 && height > 50 && width < canvas.width && height < canvas.height) {\n          // 裁剪图像\n          const croppedData = ctx.getImageData(minX, minY, width, height);\n          canvas.width = width;\n          canvas.height = height;\n          ctx.putImageData(croppedData, 0, 0);\n          console.log('成功裁剪身份证区域:', width, 'x', height);\n        } else {\n          console.log('未找到明确的身份证区域，跳过裁剪');\n        }\n      } catch (e) {\n        console.error('裁剪身份证区域失败:', e);\n      }\n    },\n    \n    // 调整亮度和对比度\n    adjustBrightnessContrast(canvas, ctx, brightness = 0, contrast = 0) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        \n        // 计算对比度因子\n        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));\n        \n        for (let i = 0; i < data.length; i += 4) {\n          // 亮度调整\n          data[i] += brightness;     // R\n          data[i + 1] += brightness; // G\n          data[i + 2] += brightness; // B\n          \n          // 对比度调整\n          data[i] = factor * (data[i] - 128) + 128;\n          data[i + 1] = factor * (data[i + 1] - 128) + 128;\n          data[i + 2] = factor * (data[i + 2] - 128) + 128;\n          \n          // 确保值在0-255范围内\n          data[i] = Math.min(255, Math.max(0, data[i]));\n          data[i + 1] = Math.min(255, Math.max(0, data[i + 1]));\n          data[i + 2] = Math.min(255, Math.max(0, data[i + 2]));\n        }\n        \n        ctx.putImageData(imageData, 0, 0);\n        console.log('成功调整亮度和对比度');\n      } catch (e) {\n        console.error('调整亮度和对比度失败:', e);\n      }\n    },\n    \n    // 锐化图像\n    sharpenImage(canvas, ctx) {\n      try {\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        const data = imageData.data;\n        const width = canvas.width;\n        const height = canvas.height;\n        const sharpenData = new Uint8ClampedArray(data);\n        \n        // 锐化卷积核\n        const kernel = [\n          0, -1, 0,\n          -1, 5, -1,\n          0, -1, 0\n        ];\n        \n        // 应用卷积\n        for (let y = 1; y < height - 1; y++) {\n          for (let x = 1; x < width - 1; x++) {\n            for (let c = 0; c < 3; c++) {\n              let sum = 0;\n              for (let ky = -1; ky <= 1; ky++) {\n                for (let kx = -1; kx <= 1; kx++) {\n                  const idx = ((y + ky) * width + (x + kx)) * 4 + c;\n                  sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n                }\n              }\n              sharpenData[(y * width + x) * 4 + c] = Math.min(255, Math.max(0, sum));\n            }\n          }\n        }\n        \n        // 更新图像数据\n        const enhancedImageData = new ImageData(sharpenData, width, height);\n        ctx.putImageData(enhancedImageData, 0, 0);\n        console.log('成功锐化图像');\n      } catch (e) {\n        console.error('锐化图像失败:', e);\n      }\n    },\n\n    // 调用OCR API\n    callOcrApi(formData) {\n      console.log('调用OCR API:', this.scannerConfig.ocrApiUrl)\n      \n      // 添加跨域请求头\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Access-Control-Allow-Origin': '*'\n        }\n      }\n      \n      axios.post(this.scannerConfig.ocrApiUrl, formData, config)\n        .then(response => {\n          console.log('OCR API 响应:', response)\n          this.handleOcrResult(response.data)\n        })\n        .catch(error => {\n          console.error('OCR识别失败', error)\n          this.$message.error('OCR识别失败: ' + (error.response?.data?.message || error.message))\n        })\n        .finally(() => {\n          this.isProcessingOcr = false\n        })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.contract-step1-container {\n  padding: 20px;\n}\n\n.contract-step1-container h3 {\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n  color: #303133;\n}\n\n.scanner-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  background-color: #f9fafc;\n}\n\n.scanner-preview {\n  width: 600px;\n  height: 400px;\n  margin-bottom: 20px;\n  border: 1px solid #dcdfe6;\n  background-color: #ebeef5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n}\n\n.scanner-preview img {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n.scanner-controls {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.upload-button {\n  display: inline-block;\n  margin-left: 10px;\n}\n\n.worker-info-form {\n  padding: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  background-color: #fff;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.input-with-button {\n  display: flex;\n  align-items: center;\n}\n\n.input-with-button .el-input {\n  flex: 1;\n  margin-right: 8px;\n}\n\n@media (max-width: 768px) {\n  .scanner-preview {\n    width: 100%;\n    height: 300px;\n  }\n  \n  .scanner-controls {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .upload-button {\n    margin-left: 0;\n    margin-top: 10px;\n  }\n  \n  .input-with-button {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .input-with-button .el-input {\n    margin-right: 0;\n    margin-bottom: 8px;\n  }\n}\n</style> "]}]}