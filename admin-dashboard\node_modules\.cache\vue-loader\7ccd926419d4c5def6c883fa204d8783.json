{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep2.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\src\\views\\contract\\ContractStep2.vue", "mtime": 1753347546119}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749542386243}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749542386307}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\daka\\daka\\admin-dashboard\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749542425518}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ContractStep2.vue"], "names": [], "mappings": ";AAg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file": "ContractStep2.vue", "sourceRoot": "src/views/contract", "sourcesContent": ["<template>\r\n  <div class=\"contract-step2-container\">\r\n    <h3>步骤2：银行卡信息</h3>\r\n    \r\n    <div class=\"bank-info-form\">\r\n      <el-form :model=\"bankForm\" label-width=\"120px\" ref=\"bankForm\" :rules=\"rules\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工人姓名\" prop=\"workerName\">\r\n              <el-input v-model=\"bankForm.workerName\" placeholder=\"请输入工人姓名\" disabled></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"证件号码\" prop=\"idCardNumber\">\r\n              <el-input v-model=\"bankForm.idCardNumber\" placeholder=\"请输入证件号码\" disabled></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行卡号\" prop=\"bankCardNumber\">\r\n              <el-input v-model=\"bankForm.bankCardNumber\" placeholder=\"请输入银行卡号\" maxlength=\"23\" @input=\"formatBankCardNumber\" @blur=\"queryBankInfo\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行名称\" prop=\"bankName\">\r\n              <el-input v-model=\"bankForm.bankName\" placeholder=\"银行名称将自动识别\" readonly></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      \r\n      <!-- 删除调试信息显示区域 -->\r\n      \r\n      <div class=\"bank-card-preview\">\r\n        <div class=\"card-container\" :class=\"getBankCardClass()\">\r\n          <div class=\"card-header\">\r\n            <div class=\"bank-logo\">{{ getBankLogo() }}</div>\r\n            <div class=\"bank-name\">{{ bankForm.bankName || '银行卡' }}</div>\r\n          </div>\r\n          <div class=\"card-number\">{{ formatDisplayCardNumber() }}</div>\r\n          <div class=\"card-footer\">\r\n            <div class=\"card-holder\">持卡人：{{ bankForm.workerName || '未填写' }}</div>\r\n            <div class=\"card-valid\">有效期：长期</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-actions\">\r\n        <el-button @click=\"prevStep\">上一步</el-button>\r\n        <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 银行选择对话框 -->\r\n    <el-dialog title=\"选择开户银行\" :visible.sync=\"bankSelectorVisible\" width=\"50%\">\r\n      <div class=\"bank-search\">\r\n        <el-input\r\n          placeholder=\"请输入银行名称或代码\"\r\n          v-model=\"bankSearchKeyword\"\r\n          clearable\r\n          @keyup.enter.native=\"searchBanks\"\r\n        >\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchBanks\"></el-button>\r\n        </el-input>\r\n      </div>\r\n      \r\n      <el-table\r\n        v-loading=\"bankListLoading\"\r\n        :data=\"pagedBankList\"\r\n        border\r\n        style=\"width: 100%; margin-top: 15px;\"\r\n        height=\"400px\"\r\n        @row-click=\"handleRowClick\"\r\n        @row-dblclick=\"handleRowDblClick\"\r\n        highlight-current-row\r\n        ref=\"bankTable\"\r\n      >\r\n        <el-table-column width=\"55\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio :label=\"scope.row.id\" v-model=\"selectedBankId\" @change.native=\"handleRadioChange(scope.row)\">&nbsp;</el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"bankName\" label=\"银行名称\" min-width=\"200\"></el-table-column>\r\n        <el-table-column prop=\"bankCode\" label=\"银行代码\" width=\"150\" align=\"center\"></el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"bankListQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"bankListQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"totalBankCount\"\r\n          background\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n      \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"bankSelectorVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmBankSelection\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getBankInfoList } from '@/api/bankInfo'\r\nimport { getBankInfoByBin } from '@/api/bankCardBin'\r\n\r\nexport default {\r\n  name: 'ContractStep2',\r\n  props: {\r\n    workerInfo: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    // 银行卡号验证\r\n    const validateBankCard = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请输入银行卡号'))\r\n      }\r\n      \r\n      // 移除空格后验证\r\n      const cardNumber = value.replace(/\\s/g, '')\r\n      if (!/^\\d{16,19}$/.test(cardNumber)) {\r\n        return callback(new Error('银行卡号格式不正确，应为16-19位数字'))\r\n      }\r\n      \r\n      callback()\r\n    }\r\n    \r\n    // 证件号码验证\r\n    const validateIdCard = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请输入证件号码'))\r\n      }\r\n      \r\n      // 简单验证18位或15位身份证号\r\n      // const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\r\n      // if (!reg.test(value)) {\r\n      //   return callback(new Error('证件号码格式不正确'))\r\n      // }\r\n      \r\n      callback()\r\n    }\r\n    \r\n    return {\r\n      bankForm: {\r\n        workerName: '',\r\n        idCardNumber: '',\r\n        bankCardNumber: '',\r\n        bankName: '',\r\n        bankCode: ''\r\n      },\r\n      rules: {\r\n        workerName: [\r\n          { required: true, message: '请输入工人姓名', trigger: 'blur' }\r\n        ],\r\n        idCardNumber: [\r\n          { required: true, message: '请输入证件号码', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ],\r\n        bankCardNumber: [\r\n          { required: true, message: '请输入银行卡号', trigger: 'blur' },\r\n          { validator: validateBankCard, trigger: 'blur' }\r\n        ],\r\n        bankName: [\r\n          { required: true, message: '银行名称不能为空', trigger: 'change' }\r\n        ]\r\n      },\r\n      // 银行选择相关数据\r\n      bankSelectorVisible: false,\r\n      bankListLoading: false,\r\n      bankList: [],\r\n      bankSearchKeyword: '',\r\n      selectedBank: null,\r\n      selectedBankId: null, // 添加selectedBankId用于单选按钮\r\n      bankListQuery: {\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      totalBankCount: 0 // 添加总银行数量字段\r\n    }\r\n  },\r\n  computed: {\r\n    filteredBankList() {\r\n      return this.bankList;\r\n    },\r\n    pagedBankList() {\r\n      return this.bankList;\r\n    }\r\n  },\r\n  created() {\r\n    // 添加详细的调试日志\r\n    console.log('=== ContractStep2 created 开始 ===')\r\n    console.log('ContractStep2 created - 接收到的props:', JSON.stringify(this.workerInfo))\r\n    console.log('ContractStep2 created - 初始表单数据:', JSON.stringify(this.bankForm))\r\n\r\n    // 首先尝试从props初始化表单\r\n    this.initFormFromProps()\r\n\r\n    // 如果props中没有姓名和证件号码，尝试从localStorage恢复\r\n    if (!this.bankForm.workerName || !this.bankForm.idCardNumber) {\r\n      console.log('ContractStep2 created - props中缺少数据，尝试从localStorage恢复')\r\n      // false表示不要emit更新父组件\r\n      this.tryRestoreFromLocalStorage(false)\r\n    }\r\n\r\n    // 添加调试日志\r\n    console.log('ContractStep2 created - 最终表单数据:', JSON.stringify(this.bankForm))\r\n    console.log('=== ContractStep2 created 结束 ===')\r\n  },\r\n  mounted() {\r\n    // 在组件挂载后，强制刷新表单数据\r\n    this.$nextTick(() => {\r\n      // 确保数据已经正确初始化\r\n      if ((!this.bankForm.workerName || !this.bankForm.idCardNumber) && this.workerInfo) {\r\n        console.log('ContractStep2 mounted - 重新从props初始化数据')\r\n        this.initFormFromProps()\r\n      }\r\n      \r\n      // 如果还是没有数据，尝试从localStorage恢复\r\n      if (!this.bankForm.workerName || !this.bankForm.idCardNumber) {\r\n        console.log('ContractStep2 mounted - 尝试从localStorage恢复数据')\r\n        this.tryRestoreFromLocalStorage(true)\r\n      }\r\n      \r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n      \r\n      // 添加调试日志\r\n      console.log('ContractStep2 mounted - 刷新后的表单数据:', JSON.stringify(this.bankForm))\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 只有当完全离开合同流程时才清空数据\r\n    // 不要在步骤之间导航时清空\r\n    if (this.$route && !this.$route.path.includes('/contract/contract-steps/')) {\r\n      console.log('ContractStep2 - 离开合同流程，清空数据')\r\n      localStorage.removeItem('contractWorkerInfo')\r\n    } else {\r\n      console.log('ContractStep2 - 在合同流程内导航，保留数据')\r\n      // 确保数据已保存到localStorage\r\n      this.saveToLocalStorage()\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听 workerInfo 变化\r\n    workerInfo: {\r\n      handler(newVal, oldVal) {\r\n        // 只在有实质变化时处理\r\n        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\r\n          console.log('ContractStep2 - workerInfo变化:', JSON.stringify(newVal))\r\n          \r\n          // 如果新的 workerInfo 包含有效的工人信息，初始化表单\r\n          if (newVal?.workerName || newVal?.idCardNumber) {\r\n            this.$nextTick(() => {\r\n              this.initFormFromProps();\r\n              console.log('ContractStep2 - 从props更新后的表单数据:', JSON.stringify(this.bankForm))\r\n            });\r\n          }\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true // 立即触发，确保组件创建时就处理props\r\n    }\r\n  },\r\n  methods: {\r\n    // 显示银行选择器\r\n    showBankSelector() {\r\n      this.bankSelectorVisible = true;\r\n      this.bankSearchKeyword = '';\r\n      this.loadBankList();\r\n    },\r\n    \r\n    // 加载银行列表\r\n    loadBankList() {\r\n      this.bankListLoading = true;\r\n      getBankInfoList({\r\n        page: this.bankListQuery.page,\r\n        limit: this.bankListQuery.limit,\r\n        bankName: this.bankSearchKeyword,  // 添加搜索关键词\r\n        bankCode: this.bankSearchKeyword   // 添加搜索关键词\r\n      }).then(response => {\r\n        if (response.code === 0) {\r\n          this.bankList = response.data || [];\r\n          this.totalBankCount = response.count || 0; // 设置总记录数\r\n        } else {\r\n          this.$message.error(response.msg || '获取银行列表失败');\r\n          this.bankList = [];\r\n          this.totalBankCount = 0;\r\n        }\r\n        this.bankListLoading = false;\r\n      }).catch(() => {\r\n        this.bankListLoading = false;\r\n        this.$message.error('获取银行列表失败');\r\n      });\r\n    },\r\n    \r\n    // 搜索银行\r\n    searchBanks() {\r\n      // 重置到第一页\r\n      this.bankListQuery.page = 1;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 处理每页条数变化\r\n    handleSizeChange(val) {\r\n      this.bankListQuery.limit = val;\r\n      this.bankListQuery.page = 1;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.bankListQuery.page = val;\r\n      this.loadBankList(); // 重新加载数据\r\n    },\r\n    \r\n    // 控制行是否可选\r\n    rowSelectable(row, index) {\r\n      return true; // 所有行都可选\r\n    },\r\n    \r\n    // 处理单选按钮变化\r\n    handleRadioChange(row) {\r\n      this.selectBank(row);\r\n    },\r\n    \r\n    // 选择银行\r\n    selectBank(row) {\r\n      this.selectedBank = row;\r\n      this.selectedBankId = row.id;\r\n      // 设置当前行高亮\r\n      this.$refs.bankTable.setCurrentRow(row);\r\n    },\r\n    \r\n    // 处理行点击事件\r\n    handleRowClick(row) {\r\n      this.selectBank(row);\r\n    },\r\n    \r\n    // 处理行双击事件\r\n    handleRowDblClick(row) {\r\n      this.selectBank(row);\r\n      this.confirmBankSelection();\r\n    },\r\n    \r\n    // 确认银行选择\r\n    confirmBankSelection() {\r\n      if (this.selectedBank) {\r\n        this.bankForm.bankName = this.selectedBank.bankName;\r\n        this.bankForm.bankCode = this.selectedBank.bankCode;\r\n        this.bankSelectorVisible = false;\r\n      } else {\r\n        this.$message.warning('请选择一家银行');\r\n      }\r\n    },\r\n    \r\n    // 根据银行卡号查询银行信息\r\n    queryBankInfo() {\r\n      const cardNumber = this.bankForm.bankCardNumber.replace(/\\s/g, '');\r\n      if (cardNumber.length >= 3) {  // 改为3位就开始查询\r\n        getBankInfoByBin(cardNumber).then(response => {  // 传入完整卡号，让后端处理\r\n          if (response.code === 0 && response.data) {\r\n            this.bankForm.bankName = response.data.bank;\r\n            this.bankForm.bankCode = response.data.bankCode;\r\n          } else {\r\n            this.bankForm.bankName = '';\r\n            this.bankForm.bankCode = '';\r\n            // 只有在输入足够位数时才提示未找到\r\n            if (cardNumber.length >= 6) {\r\n              this.$message.warning('未找到对应的银行信息');\r\n            }\r\n          }\r\n        }).catch(() => {\r\n          this.bankForm.bankName = '';\r\n          this.bankForm.bankCode = '';\r\n          if (cardNumber.length >= 6) {\r\n            this.$message.error('查询银行信息失败');\r\n          }\r\n        });\r\n      } else {\r\n        this.bankForm.bankName = '';\r\n        this.bankForm.bankCode = '';\r\n      }\r\n    },\r\n    \r\n    // 获取银行卡样式类\r\n    getBankCardClass() {\r\n      if (!this.bankForm.bankName) return 'default-card';\r\n      \r\n      // 根据银行名称返回对应的样式类\r\n      const bankNameMap = {\r\n        '中国工商银行': 'icbc-card',\r\n        '中国农业银行': 'abc-card',\r\n        '中国建设银行': 'ccb-card',\r\n        '中国银行': 'boc-card',\r\n        '交通银行': 'bocom-card',\r\n        '中国邮政储蓄银行': 'psbc-card',\r\n        '招商银行': 'cmb-card',\r\n        '中信银行': 'citic-card',\r\n        '上海浦东发展银行': 'spdb-card',\r\n        '兴业银行': 'cib-card',\r\n        '中国光大银行': 'ceb-card',\r\n        '中国民生银行': 'cmbc-card',\r\n        '华夏银行': 'hxb-card',\r\n        '广发银行': 'cgb-card',\r\n        '平安银行': 'pab-card'\r\n      };\r\n      \r\n      return bankNameMap[this.bankForm.bankName] || 'default-card';\r\n    },\r\n    \r\n    // 获取银行Logo\r\n    getBankLogo() {\r\n      if (!this.bankForm.bankName) return '银';\r\n      \r\n      // 返回银行名称的第一个字\r\n      return this.bankForm.bankName.charAt(0);\r\n    },\r\n    \r\n    // 清空合同数据\r\n    clearContractData() {\r\n      console.log('ContractStep2 - 清空合同数据')\r\n      localStorage.removeItem('contractWorkerInfo')\r\n    },\r\n    \r\n    // 尝试从localStorage恢复数据\r\n    tryRestoreFromLocalStorage(shouldEmit = true) {\r\n      try {\r\n        const savedWorkerInfo = localStorage.getItem('contractWorkerInfo')\r\n        if (savedWorkerInfo) {\r\n          const parsedData = JSON.parse(savedWorkerInfo)\r\n          console.log('ContractStep2 - 从本地存储恢复数据:', JSON.stringify(parsedData))\r\n          \r\n          // 更新表单数据\r\n          this.bankForm.workerName = parsedData.workerName || this.bankForm.workerName || '';\r\n          this.bankForm.idCardNumber = parsedData.idCardNumber || this.bankForm.idCardNumber || '';\r\n          this.bankForm.bankCardNumber = parsedData.bankCardNumber || this.bankForm.bankCardNumber || '';\r\n          this.bankForm.bankName = parsedData.bankName || this.bankForm.bankName || '';\r\n          this.bankForm.bankCode = parsedData.bankCode || this.bankForm.bankCode || '';\r\n          \r\n          // 只在需要时更新父组件数据\r\n          if (shouldEmit && (parsedData.workerName || parsedData.idCardNumber)) {\r\n            this.$emit('update-worker-info', { ...parsedData })\r\n          }\r\n          \r\n          return true;\r\n        }\r\n      } catch (error) {\r\n        console.error('ContractStep2 - 从本地存储恢复数据失败:', error)\r\n      }\r\n      return false;\r\n    },\r\n    \r\n    // 保存数据到localStorage\r\n    saveToLocalStorage() {\r\n      try {\r\n        // 确保保存的数据包含所有必要字段\r\n        const existingData = {};\r\n        try {\r\n          const saved = localStorage.getItem('contractWorkerInfo');\r\n          if (saved) {\r\n            Object.assign(existingData, JSON.parse(saved));\r\n          }\r\n        } catch (e) {\r\n          console.error('解析已存储数据失败:', e)\r\n        }\r\n        \r\n        // 合并现有数据和当前表单数据\r\n        const dataToSave = { \r\n          ...existingData,\r\n          // 确保工人和银行卡信息都被保存\r\n          workerName: this.bankForm.workerName || existingData.workerName || '',\r\n          idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n          bankCardNumber: this.bankForm.bankCardNumber || '',\r\n          bankName: this.bankForm.bankName || '',\r\n          bankCode: this.bankForm.bankCode || ''\r\n        };\r\n        \r\n        localStorage.setItem('contractWorkerInfo', JSON.stringify(dataToSave));\r\n        console.log('ContractStep2 - 已保存数据到localStorage:', JSON.stringify(dataToSave))\r\n        return dataToSave;\r\n      } catch (error) {\r\n        console.error('ContractStep2 - 保存到本地存储失败:', error);\r\n        return this.bankForm;\r\n      }\r\n    },\r\n    \r\n    // 从props初始化表单\r\n    initFormFromProps() {\r\n      // 直接从props中获取数据\r\n      if (this.workerInfo) {\r\n        console.log('ContractStep2 - 从props初始化表单，获取到的数据:', JSON.stringify(this.workerInfo))\r\n        \r\n        // 防止覆盖已有的有效数据\r\n        if (this.workerInfo.workerName) this.bankForm.workerName = this.workerInfo.workerName;\r\n        if (this.workerInfo.idCardNumber) this.bankForm.idCardNumber = this.workerInfo.idCardNumber;\r\n        if (this.workerInfo.bankCardNumber) this.bankForm.bankCardNumber = this.workerInfo.bankCardNumber;\r\n        if (this.workerInfo.bankName) this.bankForm.bankName = this.workerInfo.bankName;\r\n        if (this.workerInfo.bankCode) this.bankForm.bankCode = this.workerInfo.bankCode;\r\n        \r\n        // 如果从props中获取到了有效的工人信息，保存到localStorage\r\n        if (this.workerInfo.workerName || this.workerInfo.idCardNumber) {\r\n          this.saveToLocalStorage();\r\n        }\r\n        \r\n        // 强制更新视图\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 格式化银行卡号（每4位添加空格）\r\n    formatBankCardNumber(value) {\r\n      if (!value) return\r\n      \r\n      // 移除所有空格\r\n      let cardNumber = value.replace(/\\s/g, '')\r\n      \r\n      // 限制只能输入数字\r\n      cardNumber = cardNumber.replace(/[^\\d]/g, '')\r\n      \r\n      // 限制最大长度为19位\r\n      cardNumber = cardNumber.substring(0, 19)\r\n      \r\n      // 每4位添加空格\r\n      let formattedNumber = ''\r\n      for (let i = 0; i < cardNumber.length; i++) {\r\n        if (i > 0 && i % 4 === 0) {\r\n          formattedNumber += ' '\r\n        }\r\n        formattedNumber += cardNumber.charAt(i)\r\n      }\r\n      \r\n      // 更新表单值\r\n      this.bankForm.bankCardNumber = formattedNumber\r\n      \r\n      // 如果输入了足够的位数，自动查询银行信息\r\n      if (cardNumber.length >= 3) {  // 改为3位就开始查询\r\n        this.queryBankInfo();\r\n      } else {\r\n        this.bankForm.bankName = '';\r\n        this.bankForm.bankCode = '';\r\n      }\r\n    },\r\n    \r\n    // 获取银行卡预览显示的卡号\r\n    formatDisplayCardNumber() {\r\n      if (!this.bankForm.bankCardNumber) {\r\n        return '**** **** **** ****'\r\n      }\r\n      \r\n      return this.bankForm.bankCardNumber\r\n    },\r\n    \r\n    // 上一步\r\n    prevStep() {\r\n      // 准备要保存的数据\r\n      const existingData = {};\r\n      try {\r\n        const saved = localStorage.getItem('contractWorkerInfo');\r\n        if (saved) {\r\n          Object.assign(existingData, JSON.parse(saved));\r\n        }\r\n      } catch (e) {}\r\n      \r\n      // 合并现有数据和当前表单数据\r\n      const bankInfo = {\r\n        ...existingData, // 保留其他字段的数据\r\n        // 确保工人的基本信息和银行卡信息被保存\r\n        workerName: this.bankForm.workerName || existingData.workerName || '',\r\n        idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n        bankCardNumber: this.bankForm.bankCardNumber || '',\r\n        bankName: this.bankForm.bankName || '',\r\n        bankCode: this.bankForm.bankCode || ''\r\n      };\r\n      \r\n      // 保存到本地存储\r\n      localStorage.setItem('contractWorkerInfo', JSON.stringify(bankInfo));\r\n      \r\n      // 先更新数据，再通知父组件导航\r\n      this.$emit('update-worker-info', bankInfo);\r\n      \r\n      // 等待数据更新后再导航\r\n      this.$nextTick(() => {\r\n        // 发送上一步事件，由父组件处理导航\r\n        this.$emit('prev-step');\r\n      });\r\n    },\r\n    \r\n    // 下一步\r\n    nextStep() {\r\n      this.$refs.bankForm.validate(valid => {\r\n        if (valid) {\r\n          // 准备要保存的数据\r\n          const existingData = {};\r\n          try {\r\n            const saved = localStorage.getItem('contractWorkerInfo');\r\n            if (saved) {\r\n              Object.assign(existingData, JSON.parse(saved));\r\n            }\r\n          } catch (e) {}\r\n          \r\n          // 合并现有数据和当前表单数据\r\n          const bankInfo = {\r\n            ...existingData, // 保留其他字段的数据\r\n            // 确保工人的基本信息也被保存\r\n            workerName: this.bankForm.workerName || existingData.workerName || '',\r\n            idCardNumber: this.bankForm.idCardNumber || existingData.idCardNumber || '',\r\n            gender: existingData.gender || '',\r\n            homeAddress: existingData.homeAddress || '',\r\n            teamName: existingData.teamName || '',\r\n            jobPosition: existingData.jobPosition || '',\r\n            participantName: existingData.participantName || '',\r\n            // 银行卡信息\r\n            bankCardNumber: this.bankForm.bankCardNumber || '',\r\n            bankName: this.bankForm.bankName || '',\r\n            bankCode: this.bankForm.bankCode || ''\r\n          };\r\n          \r\n          // 保存到本地存储\r\n          localStorage.setItem('contractWorkerInfo', JSON.stringify(bankInfo));\r\n          \r\n          // 更新父组件中的工人信息\r\n          this.$emit('update-worker-info', bankInfo);\r\n          \r\n          // 等待数据更新后再导航\r\n          this.$nextTick(() => {\r\n            // 发送下一步事件，由父组件处理导航\r\n            this.$emit('next-step');\r\n          });\r\n        } else {\r\n          this.$message.warning('请完善银行卡信息');\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contract-step2-container {\r\n  padding: 20px;\r\n}\r\n\r\n.contract-step2-container h3 {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  color: #303133;\r\n}\r\n\r\n.bank-info-form {\r\n  padding: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.bank-card-preview {\r\n  margin: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.card-container {\r\n  width: 360px;\r\n  height: 220px;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n  background: linear-gradient(135deg, #0a4b78, #0a78a1);\r\n  color: white;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-container::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: -50px;\r\n  right: -50px;\r\n  width: 150px;\r\n  height: 150px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  z-index: 1;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 2;\r\n}\r\n\r\n.bank-logo {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-right: 15px;\r\n}\r\n\r\n.bank-name {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.card-number {\r\n  font-size: 22px;\r\n  letter-spacing: 2px;\r\n  font-family: 'Courier New', monospace;\r\n  text-align: center;\r\n  margin: 20px 0;\r\n  z-index: 2;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  z-index: 2;\r\n}\r\n\r\n/* 银行卡样式 */\r\n.icbc-card {\r\n  background: linear-gradient(135deg, #d10b0b, #ff5252);\r\n}\r\n\r\n.abc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.ccb-card {\r\n  background: linear-gradient(135deg, #003399, #0066cc);\r\n}\r\n\r\n.boc-card {\r\n  background: linear-gradient(135deg, #8b0000, #cc0000);\r\n}\r\n\r\n.bocom-card {\r\n  background: linear-gradient(135deg, #0066cc, #3399ff);\r\n}\r\n\r\n.psbc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.cmb-card {\r\n  background: linear-gradient(135deg, #cc0000, #ff3333);\r\n}\r\n\r\n.citic-card {\r\n  background: linear-gradient(135deg, #003366, #0066cc);\r\n}\r\n\r\n.spdb-card {\r\n  background: linear-gradient(135deg, #006699, #0099cc);\r\n}\r\n\r\n.cib-card {\r\n  background: linear-gradient(135deg, #003366, #0066cc);\r\n}\r\n\r\n.ceb-card {\r\n  background: linear-gradient(135deg, #990000, #cc3333);\r\n}\r\n\r\n.cmbc-card {\r\n  background: linear-gradient(135deg, #006633, #009966);\r\n}\r\n\r\n.hxb-card {\r\n  background: linear-gradient(135deg, #990000, #cc3333);\r\n}\r\n\r\n.cgb-card {\r\n  background: linear-gradient(135deg, #cc0000, #ff3333);\r\n}\r\n\r\n.pab-card {\r\n  background: linear-gradient(135deg, #cc6600, #ff9933);\r\n}\r\n\r\n.default-card {\r\n  background: linear-gradient(135deg, #0a4b78, #0a78a1);\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 20px;\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 银行选择对话框样式 */\r\n.bank-search {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .card-container {\r\n    width: 300px;\r\n    height: 180px;\r\n    padding: 15px;\r\n  }\r\n  \r\n  .bank-logo {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .bank-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .card-number {\r\n    font-size: 18px;\r\n    margin: 15px 0;\r\n  }\r\n  \r\n  .card-footer {\r\n    font-size: 12px;\r\n  }\r\n}\r\n</style> \r\n"]}]}