-- 暂退场执行日志表
CREATE TABLE IF NOT EXISTS `sys_team_exit_log` (
  `id` varchar(32) NOT NULL COMMENT '日志ID',
  `task_id` varchar(32) NOT NULL COMMENT '暂退场任务ID',
  `project_code` varchar(100) NOT NULL COMMENT '项目编码',
  `execute_type` tinyint(1) NOT NULL COMMENT '执行类型：1-手动执行，2-定时任务',
  `start_time` datetime NOT NULL COMMENT '开始执行时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束执行时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行耗时（毫秒）',
  `total_count` int(11) DEFAULT NULL COMMENT '总处理人数',
  `success_count` int(11) DEFAULT NULL COMMENT '成功人数',
  `fail_count` int(11) DEFAULT NULL COMMENT '失败人数',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '执行状态：1-成功，0-失败',
  `error_msg` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `fail_details` text COMMENT '失败详情（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='暂退场执行日志表'; 